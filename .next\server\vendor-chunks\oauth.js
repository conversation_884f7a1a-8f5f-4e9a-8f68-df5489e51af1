/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/oauth";
exports.ids = ["vendor-chunks/oauth"];
exports.modules = {

/***/ "(rsc)/./node_modules/oauth/index.js":
/*!*************************************!*\
  !*** ./node_modules/oauth/index.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("exports.OAuth = __webpack_require__(/*! ./lib/oauth */ \"(rsc)/./node_modules/oauth/lib/oauth.js\").OAuth;\nexports.OAuthEcho = __webpack_require__(/*! ./lib/oauth */ \"(rsc)/./node_modules/oauth/lib/oauth.js\").OAuthEcho;\nexports.OAuth2 = __webpack_require__(/*! ./lib/oauth2 */ \"(rsc)/./node_modules/oauth/lib/oauth2.js\").OAuth2;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb2F1dGgvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUFBLHVHQUE0QztBQUM1Q0EsK0dBQW9EO0FBQ3BEQSwyR0FBK0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9qYXZhLWxlYXJuaW5nLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL29hdXRoL2luZGV4LmpzPzU4OTUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0cy5PQXV0aCA9IHJlcXVpcmUoXCIuL2xpYi9vYXV0aFwiKS5PQXV0aDtcbmV4cG9ydHMuT0F1dGhFY2hvID0gcmVxdWlyZShcIi4vbGliL29hdXRoXCIpLk9BdXRoRWNobztcbmV4cG9ydHMuT0F1dGgyID0gcmVxdWlyZShcIi4vbGliL29hdXRoMlwiKS5PQXV0aDI7Il0sIm5hbWVzIjpbImV4cG9ydHMiLCJPQXV0aCIsInJlcXVpcmUiLCJPQXV0aEVjaG8iLCJPQXV0aDIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/oauth/lib/_utils.js":
/*!******************************************!*\
  !*** ./node_modules/oauth/lib/_utils.js ***!
  \******************************************/
/***/ ((module) => {

eval("// Returns true if this is a host that closes *before* it ends?!?!\nmodule.exports.isAnEarlyCloseHost = function(hostName) {\n    return hostName && hostName.match(\".*google(apis)?.com$\");\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb2F1dGgvbGliL191dGlscy5qcyIsIm1hcHBpbmdzIjoiQUFBQSxrRUFBa0U7QUFDbEVBLGlDQUFpQyxHQUFFLFNBQVVHLFFBQVE7SUFDbkQsT0FBT0EsWUFBWUEsU0FBU0MsS0FBSyxDQUFDO0FBQ3BDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vamF2YS1sZWFybmluZy1wbGF0Zm9ybS8uL25vZGVfbW9kdWxlcy9vYXV0aC9saWIvX3V0aWxzLmpzPzEzOTMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gUmV0dXJucyB0cnVlIGlmIHRoaXMgaXMgYSBob3N0IHRoYXQgY2xvc2VzICpiZWZvcmUqIGl0IGVuZHM/IT8hXG5tb2R1bGUuZXhwb3J0cy5pc0FuRWFybHlDbG9zZUhvc3Q9IGZ1bmN0aW9uKCBob3N0TmFtZSApIHtcbiAgcmV0dXJuIGhvc3ROYW1lICYmIGhvc3ROYW1lLm1hdGNoKFwiLipnb29nbGUoYXBpcyk/LmNvbSRcIilcbn0iXSwibmFtZXMiOlsibW9kdWxlIiwiZXhwb3J0cyIsImlzQW5FYXJseUNsb3NlSG9zdCIsImhvc3ROYW1lIiwibWF0Y2giXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/lib/_utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/oauth/lib/oauth.js":
/*!*****************************************!*\
  !*** ./node_modules/oauth/lib/oauth.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var crypto = __webpack_require__(/*! crypto */ \"crypto\"), sha1 = __webpack_require__(/*! ./sha1 */ \"(rsc)/./node_modules/oauth/lib/sha1.js\"), http = __webpack_require__(/*! http */ \"http\"), https = __webpack_require__(/*! https */ \"https\"), URL = __webpack_require__(/*! url */ \"url\"), querystring = __webpack_require__(/*! querystring */ \"querystring\"), OAuthUtils = __webpack_require__(/*! ./_utils */ \"(rsc)/./node_modules/oauth/lib/_utils.js\");\nexports.OAuth = function(requestUrl, accessUrl, consumerKey, consumerSecret, version, authorize_callback, signatureMethod, nonceSize, customHeaders) {\n    this._isEcho = false;\n    this._requestUrl = requestUrl;\n    this._accessUrl = accessUrl;\n    this._consumerKey = consumerKey;\n    this._consumerSecret = this._encodeData(consumerSecret);\n    if (signatureMethod == \"RSA-SHA1\") {\n        this._privateKey = consumerSecret;\n    }\n    this._version = version;\n    if (authorize_callback === undefined) {\n        this._authorize_callback = \"oob\";\n    } else {\n        this._authorize_callback = authorize_callback;\n    }\n    if (signatureMethod != \"PLAINTEXT\" && signatureMethod != \"HMAC-SHA1\" && signatureMethod != \"RSA-SHA1\") throw new Error(\"Un-supported signature method: \" + signatureMethod);\n    this._signatureMethod = signatureMethod;\n    this._nonceSize = nonceSize || 32;\n    this._headers = customHeaders || {\n        \"Accept\": \"*/*\",\n        \"Connection\": \"close\",\n        \"User-Agent\": \"Node authentication\"\n    };\n    this._clientOptions = this._defaultClientOptions = {\n        \"requestTokenHttpMethod\": \"POST\",\n        \"accessTokenHttpMethod\": \"POST\",\n        \"followRedirects\": true\n    };\n    this._oauthParameterSeperator = \",\";\n};\nexports.OAuthEcho = function(realm, verify_credentials, consumerKey, consumerSecret, version, signatureMethod, nonceSize, customHeaders) {\n    this._isEcho = true;\n    this._realm = realm;\n    this._verifyCredentials = verify_credentials;\n    this._consumerKey = consumerKey;\n    this._consumerSecret = this._encodeData(consumerSecret);\n    if (signatureMethod == \"RSA-SHA1\") {\n        this._privateKey = consumerSecret;\n    }\n    this._version = version;\n    if (signatureMethod != \"PLAINTEXT\" && signatureMethod != \"HMAC-SHA1\" && signatureMethod != \"RSA-SHA1\") throw new Error(\"Un-supported signature method: \" + signatureMethod);\n    this._signatureMethod = signatureMethod;\n    this._nonceSize = nonceSize || 32;\n    this._headers = customHeaders || {\n        \"Accept\": \"*/*\",\n        \"Connection\": \"close\",\n        \"User-Agent\": \"Node authentication\"\n    };\n    this._oauthParameterSeperator = \",\";\n};\nexports.OAuthEcho.prototype = exports.OAuth.prototype;\nexports.OAuth.prototype._getTimestamp = function() {\n    return Math.floor(new Date().getTime() / 1000);\n};\nexports.OAuth.prototype._encodeData = function(toEncode) {\n    if (toEncode == null || toEncode == \"\") return \"\";\n    else {\n        var result = encodeURIComponent(toEncode);\n        // Fix the mismatch between OAuth's  RFC3986's and Javascript's beliefs in what is right and wrong ;)\n        return result.replace(/\\!/g, \"%21\").replace(/\\'/g, \"%27\").replace(/\\(/g, \"%28\").replace(/\\)/g, \"%29\").replace(/\\*/g, \"%2A\");\n    }\n};\nexports.OAuth.prototype._decodeData = function(toDecode) {\n    if (toDecode != null) {\n        toDecode = toDecode.replace(/\\+/g, \" \");\n    }\n    return decodeURIComponent(toDecode);\n};\nexports.OAuth.prototype._getSignature = function(method, url, parameters, tokenSecret) {\n    var signatureBase = this._createSignatureBase(method, url, parameters);\n    return this._createSignature(signatureBase, tokenSecret);\n};\nexports.OAuth.prototype._normalizeUrl = function(url) {\n    var parsedUrl = URL.parse(url, true);\n    var port = \"\";\n    if (parsedUrl.port) {\n        if (parsedUrl.protocol == \"http:\" && parsedUrl.port != \"80\" || parsedUrl.protocol == \"https:\" && parsedUrl.port != \"443\") {\n            port = \":\" + parsedUrl.port;\n        }\n    }\n    if (!parsedUrl.pathname || parsedUrl.pathname == \"\") parsedUrl.pathname = \"/\";\n    return parsedUrl.protocol + \"//\" + parsedUrl.hostname + port + parsedUrl.pathname;\n};\n// Is the parameter considered an OAuth parameter\nexports.OAuth.prototype._isParameterNameAnOAuthParameter = function(parameter) {\n    var m = parameter.match(\"^oauth_\");\n    if (m && m[0] === \"oauth_\") {\n        return true;\n    } else {\n        return false;\n    }\n};\n// build the OAuth request authorization header\nexports.OAuth.prototype._buildAuthorizationHeaders = function(orderedParameters) {\n    var authHeader = \"OAuth \";\n    if (this._isEcho) {\n        authHeader += 'realm=\"' + this._realm + '\",';\n    }\n    for(var i = 0; i < orderedParameters.length; i++){\n        // Whilst the all the parameters should be included within the signature, only the oauth_ arguments\n        // should appear within the authorization header.\n        if (this._isParameterNameAnOAuthParameter(orderedParameters[i][0])) {\n            authHeader += \"\" + this._encodeData(orderedParameters[i][0]) + '=\"' + this._encodeData(orderedParameters[i][1]) + '\"' + this._oauthParameterSeperator;\n        }\n    }\n    authHeader = authHeader.substring(0, authHeader.length - this._oauthParameterSeperator.length);\n    return authHeader;\n};\n// Takes an object literal that represents the arguments, and returns an array\n// of argument/value pairs.\nexports.OAuth.prototype._makeArrayOfArgumentsHash = function(argumentsHash) {\n    var argument_pairs = [];\n    for(var key in argumentsHash){\n        if (argumentsHash.hasOwnProperty(key)) {\n            var value = argumentsHash[key];\n            if (Array.isArray(value)) {\n                for(var i = 0; i < value.length; i++){\n                    argument_pairs[argument_pairs.length] = [\n                        key,\n                        value[i]\n                    ];\n                }\n            } else {\n                argument_pairs[argument_pairs.length] = [\n                    key,\n                    value\n                ];\n            }\n        }\n    }\n    return argument_pairs;\n};\n// Sorts the encoded key value pairs by encoded name, then encoded value\nexports.OAuth.prototype._sortRequestParams = function(argument_pairs) {\n    // Sort by name, then value.\n    argument_pairs.sort(function(a, b) {\n        if (a[0] == b[0]) {\n            return a[1] < b[1] ? -1 : 1;\n        } else return a[0] < b[0] ? -1 : 1;\n    });\n    return argument_pairs;\n};\nexports.OAuth.prototype._normaliseRequestParams = function(args) {\n    var argument_pairs = this._makeArrayOfArgumentsHash(args);\n    // First encode them #3.4.1.3.2 .1\n    for(var i = 0; i < argument_pairs.length; i++){\n        argument_pairs[i][0] = this._encodeData(argument_pairs[i][0]);\n        argument_pairs[i][1] = this._encodeData(argument_pairs[i][1]);\n    }\n    // Then sort them #3.4.1.3.2 .2\n    argument_pairs = this._sortRequestParams(argument_pairs);\n    // Then concatenate together #3.4.1.3.2 .3 & .4\n    var args = \"\";\n    for(var i = 0; i < argument_pairs.length; i++){\n        args += argument_pairs[i][0];\n        args += \"=\";\n        args += argument_pairs[i][1];\n        if (i < argument_pairs.length - 1) args += \"&\";\n    }\n    return args;\n};\nexports.OAuth.prototype._createSignatureBase = function(method, url, parameters) {\n    url = this._encodeData(this._normalizeUrl(url));\n    parameters = this._encodeData(parameters);\n    return method.toUpperCase() + \"&\" + url + \"&\" + parameters;\n};\nexports.OAuth.prototype._createSignature = function(signatureBase, tokenSecret) {\n    if (tokenSecret === undefined) var tokenSecret = \"\";\n    else tokenSecret = this._encodeData(tokenSecret);\n    // consumerSecret is already encoded\n    var key = this._consumerSecret + \"&\" + tokenSecret;\n    var hash = \"\";\n    if (this._signatureMethod == \"PLAINTEXT\") {\n        hash = key;\n    } else if (this._signatureMethod == \"RSA-SHA1\") {\n        key = this._privateKey || \"\";\n        hash = crypto.createSign(\"RSA-SHA1\").update(signatureBase).sign(key, \"base64\");\n    } else {\n        if (crypto.Hmac) {\n            hash = crypto.createHmac(\"sha1\", key).update(signatureBase).digest(\"base64\");\n        } else {\n            hash = sha1.HMACSHA1(key, signatureBase);\n        }\n    }\n    return hash;\n};\nexports.OAuth.prototype.NONCE_CHARS = [\n    \"a\",\n    \"b\",\n    \"c\",\n    \"d\",\n    \"e\",\n    \"f\",\n    \"g\",\n    \"h\",\n    \"i\",\n    \"j\",\n    \"k\",\n    \"l\",\n    \"m\",\n    \"n\",\n    \"o\",\n    \"p\",\n    \"q\",\n    \"r\",\n    \"s\",\n    \"t\",\n    \"u\",\n    \"v\",\n    \"w\",\n    \"x\",\n    \"y\",\n    \"z\",\n    \"A\",\n    \"B\",\n    \"C\",\n    \"D\",\n    \"E\",\n    \"F\",\n    \"G\",\n    \"H\",\n    \"I\",\n    \"J\",\n    \"K\",\n    \"L\",\n    \"M\",\n    \"N\",\n    \"O\",\n    \"P\",\n    \"Q\",\n    \"R\",\n    \"S\",\n    \"T\",\n    \"U\",\n    \"V\",\n    \"W\",\n    \"X\",\n    \"Y\",\n    \"Z\",\n    \"0\",\n    \"1\",\n    \"2\",\n    \"3\",\n    \"4\",\n    \"5\",\n    \"6\",\n    \"7\",\n    \"8\",\n    \"9\"\n];\nexports.OAuth.prototype._getNonce = function(nonceSize) {\n    var result = [];\n    var chars = this.NONCE_CHARS;\n    var char_pos;\n    var nonce_chars_length = chars.length;\n    for(var i = 0; i < nonceSize; i++){\n        char_pos = Math.floor(Math.random() * nonce_chars_length);\n        result[i] = chars[char_pos];\n    }\n    return result.join(\"\");\n};\nexports.OAuth.prototype._createClient = function(port, hostname, method, path, headers, sslEnabled) {\n    var options = {\n        host: hostname,\n        port: port,\n        path: path,\n        method: method,\n        headers: headers\n    };\n    var httpModel;\n    if (sslEnabled) {\n        httpModel = https;\n    } else {\n        httpModel = http;\n    }\n    return httpModel.request(options);\n};\nexports.OAuth.prototype._prepareParameters = function(oauth_token, oauth_token_secret, method, url, extra_params) {\n    var oauthParameters = {\n        \"oauth_timestamp\": this._getTimestamp(),\n        \"oauth_nonce\": this._getNonce(this._nonceSize),\n        \"oauth_version\": this._version,\n        \"oauth_signature_method\": this._signatureMethod,\n        \"oauth_consumer_key\": this._consumerKey\n    };\n    if (oauth_token) {\n        oauthParameters[\"oauth_token\"] = oauth_token;\n    }\n    var sig;\n    if (this._isEcho) {\n        sig = this._getSignature(\"GET\", this._verifyCredentials, this._normaliseRequestParams(oauthParameters), oauth_token_secret);\n    } else {\n        if (extra_params) {\n            for(var key in extra_params){\n                if (extra_params.hasOwnProperty(key)) oauthParameters[key] = extra_params[key];\n            }\n        }\n        var parsedUrl = URL.parse(url, false);\n        if (parsedUrl.query) {\n            var key2;\n            var extraParameters = querystring.parse(parsedUrl.query);\n            for(var key in extraParameters){\n                var value = extraParameters[key];\n                if (typeof value == \"object\") {\n                    // TODO: This probably should be recursive\n                    for(key2 in value){\n                        oauthParameters[key + \"[\" + key2 + \"]\"] = value[key2];\n                    }\n                } else {\n                    oauthParameters[key] = value;\n                }\n            }\n        }\n        sig = this._getSignature(method, url, this._normaliseRequestParams(oauthParameters), oauth_token_secret);\n    }\n    var orderedParameters = this._sortRequestParams(this._makeArrayOfArgumentsHash(oauthParameters));\n    orderedParameters[orderedParameters.length] = [\n        \"oauth_signature\",\n        sig\n    ];\n    return orderedParameters;\n};\nexports.OAuth.prototype._performSecureRequest = function(oauth_token, oauth_token_secret, method, url, extra_params, post_body, post_content_type, callback) {\n    var orderedParameters = this._prepareParameters(oauth_token, oauth_token_secret, method, url, extra_params);\n    if (!post_content_type) {\n        post_content_type = \"application/x-www-form-urlencoded\";\n    }\n    var parsedUrl = URL.parse(url, false);\n    if (parsedUrl.protocol == \"http:\" && !parsedUrl.port) parsedUrl.port = 80;\n    if (parsedUrl.protocol == \"https:\" && !parsedUrl.port) parsedUrl.port = 443;\n    var headers = {};\n    var authorization = this._buildAuthorizationHeaders(orderedParameters);\n    if (this._isEcho) {\n        headers[\"X-Verify-Credentials-Authorization\"] = authorization;\n    } else {\n        headers[\"Authorization\"] = authorization;\n    }\n    headers[\"Host\"] = parsedUrl.host;\n    for(var key in this._headers){\n        if (this._headers.hasOwnProperty(key)) {\n            headers[key] = this._headers[key];\n        }\n    }\n    // Filter out any passed extra_params that are really to do with OAuth\n    for(var key in extra_params){\n        if (this._isParameterNameAnOAuthParameter(key)) {\n            delete extra_params[key];\n        }\n    }\n    if ((method == \"POST\" || method == \"PUT\") && post_body == null && extra_params != null) {\n        // Fix the mismatch between the output of querystring.stringify() and this._encodeData()\n        post_body = querystring.stringify(extra_params).replace(/\\!/g, \"%21\").replace(/\\'/g, \"%27\").replace(/\\(/g, \"%28\").replace(/\\)/g, \"%29\").replace(/\\*/g, \"%2A\");\n    }\n    if (post_body) {\n        if (Buffer.isBuffer(post_body)) {\n            headers[\"Content-length\"] = post_body.length;\n        } else {\n            headers[\"Content-length\"] = Buffer.byteLength(post_body);\n        }\n    } else {\n        headers[\"Content-length\"] = 0;\n    }\n    headers[\"Content-Type\"] = post_content_type;\n    var path;\n    if (!parsedUrl.pathname || parsedUrl.pathname == \"\") parsedUrl.pathname = \"/\";\n    if (parsedUrl.query) path = parsedUrl.pathname + \"?\" + parsedUrl.query;\n    else path = parsedUrl.pathname;\n    var request;\n    if (parsedUrl.protocol == \"https:\") {\n        request = this._createClient(parsedUrl.port, parsedUrl.hostname, method, path, headers, true);\n    } else {\n        request = this._createClient(parsedUrl.port, parsedUrl.hostname, method, path, headers);\n    }\n    var clientOptions = this._clientOptions;\n    if (callback) {\n        var data = \"\";\n        var self = this;\n        // Some hosts *cough* google appear to close the connection early / send no content-length header\n        // allow this behaviour.\n        var allowEarlyClose = OAuthUtils.isAnEarlyCloseHost(parsedUrl.hostname);\n        var callbackCalled = false;\n        var passBackControl = function(response) {\n            if (!callbackCalled) {\n                callbackCalled = true;\n                if (response.statusCode >= 200 && response.statusCode <= 299) {\n                    callback(null, data, response);\n                } else {\n                    // Follow 301 or 302 redirects with Location HTTP header\n                    if ((response.statusCode == 301 || response.statusCode == 302) && clientOptions.followRedirects && response.headers && response.headers.location) {\n                        self._performSecureRequest(oauth_token, oauth_token_secret, method, response.headers.location, extra_params, post_body, post_content_type, callback);\n                    } else {\n                        callback({\n                            statusCode: response.statusCode,\n                            data: data\n                        }, data, response);\n                    }\n                }\n            }\n        };\n        request.on(\"response\", function(response) {\n            response.setEncoding(\"utf8\");\n            response.on(\"data\", function(chunk) {\n                data += chunk;\n            });\n            response.on(\"end\", function() {\n                passBackControl(response);\n            });\n            response.on(\"close\", function() {\n                if (allowEarlyClose) {\n                    passBackControl(response);\n                }\n            });\n        });\n        request.on(\"error\", function(err) {\n            if (!callbackCalled) {\n                callbackCalled = true;\n                callback(err);\n            }\n        });\n        if ((method == \"POST\" || method == \"PUT\") && post_body != null && post_body != \"\") {\n            request.write(post_body);\n        }\n        request.end();\n    } else {\n        if ((method == \"POST\" || method == \"PUT\") && post_body != null && post_body != \"\") {\n            request.write(post_body);\n        }\n        return request;\n    }\n    return;\n};\nexports.OAuth.prototype.setClientOptions = function(options) {\n    var key, mergedOptions = {}, hasOwnProperty = Object.prototype.hasOwnProperty;\n    for(key in this._defaultClientOptions){\n        if (!hasOwnProperty.call(options, key)) {\n            mergedOptions[key] = this._defaultClientOptions[key];\n        } else {\n            mergedOptions[key] = options[key];\n        }\n    }\n    this._clientOptions = mergedOptions;\n};\nexports.OAuth.prototype.getOAuthAccessToken = function(oauth_token, oauth_token_secret, oauth_verifier, callback) {\n    var extraParams = {};\n    if (typeof oauth_verifier == \"function\") {\n        callback = oauth_verifier;\n    } else {\n        extraParams.oauth_verifier = oauth_verifier;\n    }\n    this._performSecureRequest(oauth_token, oauth_token_secret, this._clientOptions.accessTokenHttpMethod, this._accessUrl, extraParams, null, null, function(error, data, response) {\n        if (error) callback(error);\n        else {\n            var results = querystring.parse(data);\n            var oauth_access_token = results[\"oauth_token\"];\n            delete results[\"oauth_token\"];\n            var oauth_access_token_secret = results[\"oauth_token_secret\"];\n            delete results[\"oauth_token_secret\"];\n            callback(null, oauth_access_token, oauth_access_token_secret, results);\n        }\n    });\n};\n// Deprecated\nexports.OAuth.prototype.getProtectedResource = function(url, method, oauth_token, oauth_token_secret, callback) {\n    this._performSecureRequest(oauth_token, oauth_token_secret, method, url, null, \"\", null, callback);\n};\nexports.OAuth.prototype[\"delete\"] = function(url, oauth_token, oauth_token_secret, callback) {\n    return this._performSecureRequest(oauth_token, oauth_token_secret, \"DELETE\", url, null, \"\", null, callback);\n};\nexports.OAuth.prototype.get = function(url, oauth_token, oauth_token_secret, callback) {\n    return this._performSecureRequest(oauth_token, oauth_token_secret, \"GET\", url, null, \"\", null, callback);\n};\nexports.OAuth.prototype._putOrPost = function(method, url, oauth_token, oauth_token_secret, post_body, post_content_type, callback) {\n    var extra_params = null;\n    if (typeof post_content_type == \"function\") {\n        callback = post_content_type;\n        post_content_type = null;\n    }\n    if (typeof post_body != \"string\" && !Buffer.isBuffer(post_body)) {\n        post_content_type = \"application/x-www-form-urlencoded\";\n        extra_params = post_body;\n        post_body = null;\n    }\n    return this._performSecureRequest(oauth_token, oauth_token_secret, method, url, extra_params, post_body, post_content_type, callback);\n};\nexports.OAuth.prototype.put = function(url, oauth_token, oauth_token_secret, post_body, post_content_type, callback) {\n    return this._putOrPost(\"PUT\", url, oauth_token, oauth_token_secret, post_body, post_content_type, callback);\n};\nexports.OAuth.prototype.post = function(url, oauth_token, oauth_token_secret, post_body, post_content_type, callback) {\n    return this._putOrPost(\"POST\", url, oauth_token, oauth_token_secret, post_body, post_content_type, callback);\n};\n/**\n * Gets a request token from the OAuth provider and passes that information back\n * to the calling code.\n *\n * The callback should expect a function of the following form:\n *\n * function(err, token, token_secret, parsedQueryString) {}\n *\n * This method has optional parameters so can be called in the following 2 ways:\n *\n * 1) Primary use case: Does a basic request with no extra parameters\n *  getOAuthRequestToken( callbackFunction )\n *\n * 2) As above but allows for provision of extra parameters to be sent as part of the query to the server.\n *  getOAuthRequestToken( extraParams, callbackFunction )\n *\n * N.B. This method will HTTP POST verbs by default, if you wish to override this behaviour you will\n * need to provide a requestTokenHttpMethod option when creating the client.\n *\n **/ exports.OAuth.prototype.getOAuthRequestToken = function(extraParams, callback) {\n    if (typeof extraParams == \"function\") {\n        callback = extraParams;\n        extraParams = {};\n    }\n    // Callbacks are 1.0A related\n    if (this._authorize_callback) {\n        extraParams[\"oauth_callback\"] = this._authorize_callback;\n    }\n    this._performSecureRequest(null, null, this._clientOptions.requestTokenHttpMethod, this._requestUrl, extraParams, null, null, function(error, data, response) {\n        if (error) callback(error);\n        else {\n            var results = querystring.parse(data);\n            var oauth_token = results[\"oauth_token\"];\n            var oauth_token_secret = results[\"oauth_token_secret\"];\n            delete results[\"oauth_token\"];\n            delete results[\"oauth_token_secret\"];\n            callback(null, oauth_token, oauth_token_secret, results);\n        }\n    });\n};\nexports.OAuth.prototype.signUrl = function(url, oauth_token, oauth_token_secret, method) {\n    if (method === undefined) {\n        var method = \"GET\";\n    }\n    var orderedParameters = this._prepareParameters(oauth_token, oauth_token_secret, method, url, {});\n    var parsedUrl = URL.parse(url, false);\n    var query = \"\";\n    for(var i = 0; i < orderedParameters.length; i++){\n        query += orderedParameters[i][0] + \"=\" + this._encodeData(orderedParameters[i][1]) + \"&\";\n    }\n    query = query.substring(0, query.length - 1);\n    return parsedUrl.protocol + \"//\" + parsedUrl.host + parsedUrl.pathname + \"?\" + query;\n};\nexports.OAuth.prototype.authHeader = function(url, oauth_token, oauth_token_secret, method) {\n    if (method === undefined) {\n        var method = \"GET\";\n    }\n    var orderedParameters = this._prepareParameters(oauth_token, oauth_token_secret, method, url, {});\n    return this._buildAuthorizationHeaders(orderedParameters);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/lib/oauth.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/oauth/lib/oauth2.js":
/*!******************************************!*\
  !*** ./node_modules/oauth/lib/oauth2.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var querystring = __webpack_require__(/*! querystring */ \"querystring\"), crypto = __webpack_require__(/*! crypto */ \"crypto\"), https = __webpack_require__(/*! https */ \"https\"), http = __webpack_require__(/*! http */ \"http\"), URL = __webpack_require__(/*! url */ \"url\"), OAuthUtils = __webpack_require__(/*! ./_utils */ \"(rsc)/./node_modules/oauth/lib/_utils.js\");\nexports.OAuth2 = function(clientId, clientSecret, baseSite, authorizePath, accessTokenPath, customHeaders) {\n    this._clientId = clientId;\n    this._clientSecret = clientSecret;\n    this._baseSite = baseSite;\n    this._authorizeUrl = authorizePath || \"/oauth/authorize\";\n    this._accessTokenUrl = accessTokenPath || \"/oauth/access_token\";\n    this._accessTokenName = \"access_token\";\n    this._authMethod = \"Bearer\";\n    this._customHeaders = customHeaders || {};\n    this._useAuthorizationHeaderForGET = false;\n    //our agent\n    this._agent = undefined;\n};\n// Allows you to set an agent to use instead of the default HTTP or\n// HTTPS agents. Useful when dealing with your own certificates.\nexports.OAuth2.prototype.setAgent = function(agent) {\n    this._agent = agent;\n};\n// This 'hack' method is required for sites that don't use\n// 'access_token' as the name of the access token (for requests).\n// ( http://tools.ietf.org/html/draft-ietf-oauth-v2-16#section-7 )\n// it isn't clear what the correct value should be atm, so allowing\n// for specific (temporary?) override for now.\nexports.OAuth2.prototype.setAccessTokenName = function(name) {\n    this._accessTokenName = name;\n};\n// Sets the authorization method for Authorization header.\n// e.g. Authorization: Bearer <token>  # \"Bearer\" is the authorization method.\nexports.OAuth2.prototype.setAuthMethod = function(authMethod) {\n    this._authMethod = authMethod;\n};\n// If you use the OAuth2 exposed 'get' method (and don't construct your own _request call )\n// this will specify whether to use an 'Authorize' header instead of passing the access_token as a query parameter\nexports.OAuth2.prototype.useAuthorizationHeaderforGET = function(useIt) {\n    this._useAuthorizationHeaderForGET = useIt;\n};\nexports.OAuth2.prototype._getAccessTokenUrl = function() {\n    return this._baseSite + this._accessTokenUrl; /* + \"?\" + querystring.stringify(params); */ \n};\n// Build the authorization header. In particular, build the part after the colon.\n// e.g. Authorization: Bearer <token>  # Build \"Bearer <token>\"\nexports.OAuth2.prototype.buildAuthHeader = function(token) {\n    return this._authMethod + \" \" + token;\n};\nexports.OAuth2.prototype._chooseHttpLibrary = function(parsedUrl) {\n    var http_library = https;\n    // As this is OAUth2, we *assume* https unless told explicitly otherwise.\n    if (parsedUrl.protocol != \"https:\") {\n        http_library = http;\n    }\n    return http_library;\n};\nexports.OAuth2.prototype._request = function(method, url, headers, post_body, access_token, callback) {\n    var parsedUrl = URL.parse(url, true);\n    if (parsedUrl.protocol == \"https:\" && !parsedUrl.port) {\n        parsedUrl.port = 443;\n    }\n    var http_library = this._chooseHttpLibrary(parsedUrl);\n    var realHeaders = {};\n    for(var key in this._customHeaders){\n        realHeaders[key] = this._customHeaders[key];\n    }\n    if (headers) {\n        for(var key in headers){\n            realHeaders[key] = headers[key];\n        }\n    }\n    realHeaders[\"Host\"] = parsedUrl.host;\n    if (!realHeaders[\"User-Agent\"]) {\n        realHeaders[\"User-Agent\"] = \"Node-oauth\";\n    }\n    if (post_body) {\n        if (Buffer.isBuffer(post_body)) {\n            realHeaders[\"Content-Length\"] = post_body.length;\n        } else {\n            realHeaders[\"Content-Length\"] = Buffer.byteLength(post_body);\n        }\n    } else {\n        realHeaders[\"Content-length\"] = 0;\n    }\n    if (access_token && !(\"Authorization\" in realHeaders)) {\n        if (!parsedUrl.query) parsedUrl.query = {};\n        parsedUrl.query[this._accessTokenName] = access_token;\n    }\n    var queryStr = querystring.stringify(parsedUrl.query);\n    if (queryStr) queryStr = \"?\" + queryStr;\n    var options = {\n        host: parsedUrl.hostname,\n        port: parsedUrl.port,\n        path: parsedUrl.pathname + queryStr,\n        method: method,\n        headers: realHeaders\n    };\n    this._executeRequest(http_library, options, post_body, callback);\n};\nexports.OAuth2.prototype._executeRequest = function(http_library, options, post_body, callback) {\n    // Some hosts *cough* google appear to close the connection early / send no content-length header\n    // allow this behaviour.\n    var allowEarlyClose = OAuthUtils.isAnEarlyCloseHost(options.host);\n    var callbackCalled = false;\n    function passBackControl(response, result) {\n        if (!callbackCalled) {\n            callbackCalled = true;\n            if (!(response.statusCode >= 200 && response.statusCode <= 299) && response.statusCode != 301 && response.statusCode != 302) {\n                callback({\n                    statusCode: response.statusCode,\n                    data: result\n                });\n            } else {\n                callback(null, result, response);\n            }\n        }\n    }\n    var result = \"\";\n    //set the agent on the request options\n    if (this._agent) {\n        options.agent = this._agent;\n    }\n    var request = http_library.request(options);\n    request.on(\"response\", function(response) {\n        response.on(\"data\", function(chunk) {\n            result += chunk;\n        });\n        response.on(\"close\", function(err) {\n            if (allowEarlyClose) {\n                passBackControl(response, result);\n            }\n        });\n        response.addListener(\"end\", function() {\n            passBackControl(response, result);\n        });\n    });\n    request.on(\"error\", function(e) {\n        callbackCalled = true;\n        callback(e);\n    });\n    if ((options.method == \"POST\" || options.method == \"PUT\") && post_body) {\n        request.write(post_body);\n    }\n    request.end();\n};\nexports.OAuth2.prototype.getAuthorizeUrl = function(params) {\n    var params = params || {};\n    params[\"client_id\"] = this._clientId;\n    return this._baseSite + this._authorizeUrl + \"?\" + querystring.stringify(params);\n};\nexports.OAuth2.prototype.getOAuthAccessToken = function(code, params, callback) {\n    var params = params || {};\n    params[\"client_id\"] = this._clientId;\n    params[\"client_secret\"] = this._clientSecret;\n    var codeParam = params.grant_type === \"refresh_token\" ? \"refresh_token\" : \"code\";\n    params[codeParam] = code;\n    var post_data = querystring.stringify(params);\n    var post_headers = {\n        \"Content-Type\": \"application/x-www-form-urlencoded\"\n    };\n    this._request(\"POST\", this._getAccessTokenUrl(), post_headers, post_data, null, function(error, data, response) {\n        if (error) callback(error);\n        else {\n            var results;\n            try {\n                // As of http://tools.ietf.org/html/draft-ietf-oauth-v2-07\n                // responses should be in JSON\n                results = JSON.parse(data);\n            } catch (e) {\n                // .... However both Facebook + Github currently use rev05 of the spec\n                // and neither seem to specify a content-type correctly in their response headers :(\n                // clients of these services will suffer a *minor* performance cost of the exception\n                // being thrown\n                results = querystring.parse(data);\n            }\n            var access_token = results[\"access_token\"];\n            var refresh_token = results[\"refresh_token\"];\n            delete results[\"refresh_token\"];\n            callback(null, access_token, refresh_token, results); // callback results =-=\n        }\n    });\n};\n// Deprecated\nexports.OAuth2.prototype.getProtectedResource = function(url, access_token, callback) {\n    this._request(\"GET\", url, {}, \"\", access_token, callback);\n};\nexports.OAuth2.prototype.get = function(url, access_token, callback) {\n    if (this._useAuthorizationHeaderForGET) {\n        var headers = {\n            \"Authorization\": this.buildAuthHeader(access_token)\n        };\n        access_token = null;\n    } else {\n        headers = {};\n    }\n    this._request(\"GET\", url, headers, \"\", access_token, callback);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/lib/oauth2.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/oauth/lib/sha1.js":
/*!****************************************!*\
  !*** ./node_modules/oauth/lib/sha1.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/*\n * A JavaScript implementation of the Secure Hash Algorithm, SHA-1, as defined\n * in FIPS 180-1\n * Version 2.2 Copyright Paul Johnston 2000 - 2009.\n * Other contributors: Greg Holt, Andrew Kepert, Ydnar, Lostinet\n * Distributed under the BSD License\n * See http://pajhome.org.uk/crypt/md5 for details.\n */ /*\n * Configurable variables. You may need to tweak these to be compatible with\n * the server-side, but the defaults work in most cases.\n */ var hexcase = 1; /* hex output format. 0 - lowercase; 1 - uppercase        */ \nvar b64pad = \"=\"; /* base-64 pad character. \"=\" for strict RFC compliance   */ \n/*\n * These are the functions you'll usually want to call\n * They take string arguments and return either hex or base-64 encoded strings\n */ function hex_sha1(s) {\n    return rstr2hex(rstr_sha1(str2rstr_utf8(s)));\n}\nfunction b64_sha1(s) {\n    return rstr2b64(rstr_sha1(str2rstr_utf8(s)));\n}\nfunction any_sha1(s, e) {\n    return rstr2any(rstr_sha1(str2rstr_utf8(s)), e);\n}\nfunction hex_hmac_sha1(k, d) {\n    return rstr2hex(rstr_hmac_sha1(str2rstr_utf8(k), str2rstr_utf8(d)));\n}\nfunction b64_hmac_sha1(k, d) {\n    return rstr2b64(rstr_hmac_sha1(str2rstr_utf8(k), str2rstr_utf8(d)));\n}\nfunction any_hmac_sha1(k, d, e) {\n    return rstr2any(rstr_hmac_sha1(str2rstr_utf8(k), str2rstr_utf8(d)), e);\n}\n/*\n * Perform a simple self-test to see if the VM is working\n */ function sha1_vm_test() {\n    return hex_sha1(\"abc\").toLowerCase() == \"a9993e364706816aba3e25717850c26c9cd0d89d\";\n}\n/*\n * Calculate the SHA1 of a raw string\n */ function rstr_sha1(s) {\n    return binb2rstr(binb_sha1(rstr2binb(s), s.length * 8));\n}\n/*\n * Calculate the HMAC-SHA1 of a key and some data (raw strings)\n */ function rstr_hmac_sha1(key, data) {\n    var bkey = rstr2binb(key);\n    if (bkey.length > 16) bkey = binb_sha1(bkey, key.length * 8);\n    var ipad = Array(16), opad = Array(16);\n    for(var i = 0; i < 16; i++){\n        ipad[i] = bkey[i] ^ 0x36363636;\n        opad[i] = bkey[i] ^ 0x5C5C5C5C;\n    }\n    var hash = binb_sha1(ipad.concat(rstr2binb(data)), 512 + data.length * 8);\n    return binb2rstr(binb_sha1(opad.concat(hash), 512 + 160));\n}\n/*\n * Convert a raw string to a hex string\n */ function rstr2hex(input) {\n    try {\n        hexcase;\n    } catch (e) {\n        hexcase = 0;\n    }\n    var hex_tab = hexcase ? \"0123456789ABCDEF\" : \"0123456789abcdef\";\n    var output = \"\";\n    var x;\n    for(var i = 0; i < input.length; i++){\n        x = input.charCodeAt(i);\n        output += hex_tab.charAt(x >>> 4 & 0x0F) + hex_tab.charAt(x & 0x0F);\n    }\n    return output;\n}\n/*\n * Convert a raw string to a base-64 string\n */ function rstr2b64(input) {\n    try {\n        b64pad;\n    } catch (e) {\n        b64pad = \"\";\n    }\n    var tab = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\n    var output = \"\";\n    var len = input.length;\n    for(var i = 0; i < len; i += 3){\n        var triplet = input.charCodeAt(i) << 16 | (i + 1 < len ? input.charCodeAt(i + 1) << 8 : 0) | (i + 2 < len ? input.charCodeAt(i + 2) : 0);\n        for(var j = 0; j < 4; j++){\n            if (i * 8 + j * 6 > input.length * 8) output += b64pad;\n            else output += tab.charAt(triplet >>> 6 * (3 - j) & 0x3F);\n        }\n    }\n    return output;\n}\n/*\n * Convert a raw string to an arbitrary string encoding\n */ function rstr2any(input, encoding) {\n    var divisor = encoding.length;\n    var remainders = Array();\n    var i, q, x, quotient;\n    /* Convert to an array of 16-bit big-endian values, forming the dividend */ var dividend = Array(Math.ceil(input.length / 2));\n    for(i = 0; i < dividend.length; i++){\n        dividend[i] = input.charCodeAt(i * 2) << 8 | input.charCodeAt(i * 2 + 1);\n    }\n    /*\n   * Repeatedly perform a long division. The binary array forms the dividend,\n   * the length of the encoding is the divisor. Once computed, the quotient\n   * forms the dividend for the next step. We stop when the dividend is zero.\n   * All remainders are stored for later use.\n   */ while(dividend.length > 0){\n        quotient = Array();\n        x = 0;\n        for(i = 0; i < dividend.length; i++){\n            x = (x << 16) + dividend[i];\n            q = Math.floor(x / divisor);\n            x -= q * divisor;\n            if (quotient.length > 0 || q > 0) quotient[quotient.length] = q;\n        }\n        remainders[remainders.length] = x;\n        dividend = quotient;\n    }\n    /* Convert the remainders to the output string */ var output = \"\";\n    for(i = remainders.length - 1; i >= 0; i--)output += encoding.charAt(remainders[i]);\n    /* Append leading zero equivalents */ var full_length = Math.ceil(input.length * 8 / (Math.log(encoding.length) / Math.log(2)));\n    for(i = output.length; i < full_length; i++)output = encoding[0] + output;\n    return output;\n}\n/*\n * Encode a string as utf-8.\n * For efficiency, this assumes the input is valid utf-16.\n */ function str2rstr_utf8(input) {\n    var output = \"\";\n    var i = -1;\n    var x, y;\n    while(++i < input.length){\n        /* Decode utf-16 surrogate pairs */ x = input.charCodeAt(i);\n        y = i + 1 < input.length ? input.charCodeAt(i + 1) : 0;\n        if (0xD800 <= x && x <= 0xDBFF && 0xDC00 <= y && y <= 0xDFFF) {\n            x = 0x10000 + ((x & 0x03FF) << 10) + (y & 0x03FF);\n            i++;\n        }\n        /* Encode output as utf-8 */ if (x <= 0x7F) output += String.fromCharCode(x);\n        else if (x <= 0x7FF) output += String.fromCharCode(0xC0 | x >>> 6 & 0x1F, 0x80 | x & 0x3F);\n        else if (x <= 0xFFFF) output += String.fromCharCode(0xE0 | x >>> 12 & 0x0F, 0x80 | x >>> 6 & 0x3F, 0x80 | x & 0x3F);\n        else if (x <= 0x1FFFFF) output += String.fromCharCode(0xF0 | x >>> 18 & 0x07, 0x80 | x >>> 12 & 0x3F, 0x80 | x >>> 6 & 0x3F, 0x80 | x & 0x3F);\n    }\n    return output;\n}\n/*\n * Encode a string as utf-16\n */ function str2rstr_utf16le(input) {\n    var output = \"\";\n    for(var i = 0; i < input.length; i++)output += String.fromCharCode(input.charCodeAt(i) & 0xFF, input.charCodeAt(i) >>> 8 & 0xFF);\n    return output;\n}\nfunction str2rstr_utf16be(input) {\n    var output = \"\";\n    for(var i = 0; i < input.length; i++)output += String.fromCharCode(input.charCodeAt(i) >>> 8 & 0xFF, input.charCodeAt(i) & 0xFF);\n    return output;\n}\n/*\n * Convert a raw string to an array of big-endian words\n * Characters >255 have their high-byte silently ignored.\n */ function rstr2binb(input) {\n    var output = Array(input.length >> 2);\n    for(var i = 0; i < output.length; i++)output[i] = 0;\n    for(var i = 0; i < input.length * 8; i += 8)output[i >> 5] |= (input.charCodeAt(i / 8) & 0xFF) << 24 - i % 32;\n    return output;\n}\n/*\n * Convert an array of big-endian words to a string\n */ function binb2rstr(input) {\n    var output = \"\";\n    for(var i = 0; i < input.length * 32; i += 8)output += String.fromCharCode(input[i >> 5] >>> 24 - i % 32 & 0xFF);\n    return output;\n}\n/*\n * Calculate the SHA-1 of an array of big-endian words, and a bit length\n */ function binb_sha1(x, len) {\n    /* append padding */ x[len >> 5] |= 0x80 << 24 - len % 32;\n    x[(len + 64 >> 9 << 4) + 15] = len;\n    var w = Array(80);\n    var a = 1732584193;\n    var b = -271733879;\n    var c = -1732584194;\n    var d = 271733878;\n    var e = -1009589776;\n    for(var i = 0; i < x.length; i += 16){\n        var olda = a;\n        var oldb = b;\n        var oldc = c;\n        var oldd = d;\n        var olde = e;\n        for(var j = 0; j < 80; j++){\n            if (j < 16) w[j] = x[i + j];\n            else w[j] = bit_rol(w[j - 3] ^ w[j - 8] ^ w[j - 14] ^ w[j - 16], 1);\n            var t = safe_add(safe_add(bit_rol(a, 5), sha1_ft(j, b, c, d)), safe_add(safe_add(e, w[j]), sha1_kt(j)));\n            e = d;\n            d = c;\n            c = bit_rol(b, 30);\n            b = a;\n            a = t;\n        }\n        a = safe_add(a, olda);\n        b = safe_add(b, oldb);\n        c = safe_add(c, oldc);\n        d = safe_add(d, oldd);\n        e = safe_add(e, olde);\n    }\n    return Array(a, b, c, d, e);\n}\n/*\n * Perform the appropriate triplet combination function for the current\n * iteration\n */ function sha1_ft(t, b, c, d) {\n    if (t < 20) return b & c | ~b & d;\n    if (t < 40) return b ^ c ^ d;\n    if (t < 60) return b & c | b & d | c & d;\n    return b ^ c ^ d;\n}\n/*\n * Determine the appropriate additive constant for the current iteration\n */ function sha1_kt(t) {\n    return t < 20 ? 1518500249 : t < 40 ? 1859775393 : t < 60 ? -1894007588 : -899497514;\n}\n/*\n * Add integers, wrapping at 2^32. This uses 16-bit operations internally\n * to work around bugs in some JS interpreters.\n */ function safe_add(x, y) {\n    var lsw = (x & 0xFFFF) + (y & 0xFFFF);\n    var msw = (x >> 16) + (y >> 16) + (lsw >> 16);\n    return msw << 16 | lsw & 0xFFFF;\n}\n/*\n * Bitwise rotate a 32-bit number to the left.\n */ function bit_rol(num, cnt) {\n    return num << cnt | num >>> 32 - cnt;\n}\nexports.HMACSHA1 = function(key, data) {\n    return b64_hmac_sha1(key, data);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/lib/sha1.js\n");

/***/ })

};
;