"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@monaco-editor";
exports.ids = ["vendor-chunks/@monaco-editor"];
exports.modules = {

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   arrayLikeToArray: () => (/* binding */ _arrayLikeToArray),\n/* harmony export */   arrayWithHoles: () => (/* binding */ _arrayWithHoles),\n/* harmony export */   defineProperty: () => (/* binding */ _defineProperty),\n/* harmony export */   iterableToArrayLimit: () => (/* binding */ _iterableToArrayLimit),\n/* harmony export */   nonIterableRest: () => (/* binding */ _nonIterableRest),\n/* harmony export */   objectSpread2: () => (/* binding */ _objectSpread2),\n/* harmony export */   objectWithoutProperties: () => (/* binding */ _objectWithoutProperties),\n/* harmony export */   objectWithoutPropertiesLoose: () => (/* binding */ _objectWithoutPropertiesLoose),\n/* harmony export */   slicedToArray: () => (/* binding */ _slicedToArray),\n/* harmony export */   unsupportedIterableToArray: () => (/* binding */ _unsupportedIterableToArray)\n/* harmony export */ });\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction ownKeys(object, enumerableOnly) {\n    var keys = Object.keys(object);\n    if (Object.getOwnPropertySymbols) {\n        var symbols = Object.getOwnPropertySymbols(object);\n        if (enumerableOnly) symbols = symbols.filter(function(sym) {\n            return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n        });\n        keys.push.apply(keys, symbols);\n    }\n    return keys;\n}\nfunction _objectSpread2(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = arguments[i] != null ? arguments[i] : {};\n        if (i % 2) {\n            ownKeys(Object(source), true).forEach(function(key) {\n                _defineProperty(target, key, source[key]);\n            });\n        } else if (Object.getOwnPropertyDescriptors) {\n            Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n        } else {\n            ownKeys(Object(source)).forEach(function(key) {\n                Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n            });\n        }\n    }\n    return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n    for(i = 0; i < sourceKeys.length; i++){\n        key = sourceKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        target[key] = source[key];\n    }\n    return target;\n}\nfunction _objectWithoutProperties(source, excluded) {\n    if (source == null) return {};\n    var target = _objectWithoutPropertiesLoose(source, excluded);\n    var key, i;\n    if (Object.getOwnPropertySymbols) {\n        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n        for(i = 0; i < sourceSymbolKeys.length; i++){\n            key = sourceSymbolKeys[i];\n            if (excluded.indexOf(key) >= 0) continue;\n            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\nfunction _slicedToArray(arr, i) {\n    return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _arrayWithHoles(arr) {\n    if (Array.isArray(arr)) return arr;\n}\nfunction _iterableToArrayLimit(arr, i) {\n    if (typeof Symbol === \"undefined\" || !(Symbol.iterator in Object(arr))) return;\n    var _arr = [];\n    var _n = true;\n    var _d = false;\n    var _e = undefined;\n    try {\n        for(var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true){\n            _arr.push(_s.value);\n            if (i && _arr.length === i) break;\n        }\n    } catch (err) {\n        _d = true;\n        _e = err;\n    } finally{\n        try {\n            if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n        } finally{\n            if (_d) throw _e;\n        }\n    }\n    return _arr;\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];\n    return arr2;\n}\nfunction _nonIterableRest() {\n    throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/config/index.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/config/index.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nvar config = {\n    paths: {\n        vs: \"https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs\"\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (config);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy9jb25maWcvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLElBQUlBLFNBQVM7SUFDWEMsT0FBTztRQUNMQyxJQUFJO0lBQ047QUFDRjtBQUVBLGlFQUFlRixNQUFNQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vamF2YS1sZWFybmluZy1wbGF0Zm9ybS8uL25vZGVfbW9kdWxlcy9AbW9uYWNvLWVkaXRvci9sb2FkZXIvbGliL2VzL2NvbmZpZy9pbmRleC5qcz8zN2ZkIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBjb25maWcgPSB7XG4gIHBhdGhzOiB7XG4gICAgdnM6ICdodHRwczovL2Nkbi5qc2RlbGl2ci5uZXQvbnBtL21vbmFjby1lZGl0b3JAMC41Mi4yL21pbi92cydcbiAgfVxufTtcblxuZXhwb3J0IGRlZmF1bHQgY29uZmlnO1xuIl0sIm5hbWVzIjpbImNvbmZpZyIsInBhdGhzIiwidnMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/config/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/index.js":
/*!************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _loader_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _loader_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./loader/index.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/loader/index.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF1QztBQUNLIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vamF2YS1sZWFybmluZy1wbGF0Zm9ybS8uL25vZGVfbW9kdWxlcy9AbW9uYWNvLWVkaXRvci9sb2FkZXIvbGliL2VzL2luZGV4LmpzP2ZmYjYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGxvYWRlciBmcm9tICcuL2xvYWRlci9pbmRleC5qcyc7XG5leHBvcnQgeyBkZWZhdWx0IH0gZnJvbSAnLi9sb2FkZXIvaW5kZXguanMnO1xuIl0sIm5hbWVzIjpbImxvYWRlciIsImRlZmF1bHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/loader/index.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/loader/index.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js\");\n/* harmony import */ var state_local__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! state-local */ \"(ssr)/./node_modules/state-local/lib/es/state-local.js\");\n/* harmony import */ var _config_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../config/index.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/config/index.js\");\n/* harmony import */ var _validators_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../validators/index.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/validators/index.js\");\n/* harmony import */ var _utils_compose_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/compose.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/compose.js\");\n/* harmony import */ var _utils_deepMerge_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/deepMerge.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/deepMerge.js\");\n/* harmony import */ var _utils_makeCancelable_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/makeCancelable.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/makeCancelable.js\");\n\n\n\n\n\n\n\n/** the local state of the module */ var _state$create = state_local__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    config: _config_index_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n    isInitialized: false,\n    resolve: null,\n    reject: null,\n    monaco: null\n}), _state$create2 = (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__.slicedToArray)(_state$create, 2), getState = _state$create2[0], setState = _state$create2[1];\n/**\n * set the loader configuration\n * @param {Object} config - the configuration object\n */ function config(globalConfig) {\n    var _validators$config = _validators_index_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"].config(globalConfig), monaco = _validators$config.monaco, config = (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__.objectWithoutProperties)(_validators$config, [\n        \"monaco\"\n    ]);\n    setState(function(state) {\n        return {\n            config: (0,_utils_deepMerge_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(state.config, config),\n            monaco: monaco\n        };\n    });\n}\n/**\n * handles the initialization of the monaco-editor\n * @return {Promise} - returns an instance of monaco (with a cancelable promise)\n */ function init() {\n    var state = getState(function(_ref) {\n        var monaco = _ref.monaco, isInitialized = _ref.isInitialized, resolve = _ref.resolve;\n        return {\n            monaco: monaco,\n            isInitialized: isInitialized,\n            resolve: resolve\n        };\n    });\n    if (!state.isInitialized) {\n        setState({\n            isInitialized: true\n        });\n        if (state.monaco) {\n            state.resolve(state.monaco);\n            return (0,_utils_makeCancelable_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(wrapperPromise);\n        }\n        if (window.monaco && window.monaco.editor) {\n            storeMonacoInstance(window.monaco);\n            state.resolve(window.monaco);\n            return (0,_utils_makeCancelable_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(wrapperPromise);\n        }\n        (0,_utils_compose_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(injectScripts, getMonacoLoaderScript)(configureLoader);\n    }\n    return (0,_utils_makeCancelable_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(wrapperPromise);\n}\n/**\n * injects provided scripts into the document.body\n * @param {Object} script - an HTML script element\n * @return {Object} - the injected HTML script element\n */ function injectScripts(script) {\n    return document.body.appendChild(script);\n}\n/**\n * creates an HTML script element with/without provided src\n * @param {string} [src] - the source path of the script\n * @return {Object} - the created HTML script element\n */ function createScript(src) {\n    var script = document.createElement(\"script\");\n    return src && (script.src = src), script;\n}\n/**\n * creates an HTML script element with the monaco loader src\n * @return {Object} - the created HTML script element\n */ function getMonacoLoaderScript(configureLoader) {\n    var state = getState(function(_ref2) {\n        var config = _ref2.config, reject = _ref2.reject;\n        return {\n            config: config,\n            reject: reject\n        };\n    });\n    var loaderScript = createScript(\"\".concat(state.config.paths.vs, \"/loader.js\"));\n    loaderScript.onload = function() {\n        return configureLoader();\n    };\n    loaderScript.onerror = state.reject;\n    return loaderScript;\n}\n/**\n * configures the monaco loader\n */ function configureLoader() {\n    var state = getState(function(_ref3) {\n        var config = _ref3.config, resolve = _ref3.resolve, reject = _ref3.reject;\n        return {\n            config: config,\n            resolve: resolve,\n            reject: reject\n        };\n    });\n    var require = window.require;\n    require.config(state.config);\n    require([\n        \"vs/editor/editor.main\"\n    ], function(monaco) {\n        storeMonacoInstance(monaco);\n        state.resolve(monaco);\n    }, function(error) {\n        state.reject(error);\n    });\n}\n/**\n * store monaco instance in local state\n */ function storeMonacoInstance(monaco) {\n    if (!getState().monaco) {\n        setState({\n            monaco: monaco\n        });\n    }\n}\n/**\n * internal helper function\n * extracts stored monaco instance\n * @return {Object|null} - the monaco instance\n */ function __getMonacoInstance() {\n    return getState(function(_ref4) {\n        var monaco = _ref4.monaco;\n        return monaco;\n    });\n}\nvar wrapperPromise = new Promise(function(resolve, reject) {\n    return setState({\n        resolve: resolve,\n        reject: reject\n    });\n});\nvar loader = {\n    config: config,\n    init: init,\n    __getMonacoInstance: __getMonacoInstance\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (loader);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/loader/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/compose.js":
/*!********************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/utils/compose.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nvar compose = function compose() {\n    for(var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++){\n        fns[_key] = arguments[_key];\n    }\n    return function(x) {\n        return fns.reduceRight(function(y, f) {\n            return f(y);\n        }, x);\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (compose);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy91dGlscy9jb21wb3NlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxJQUFJQSxVQUFVLFNBQVNBO0lBQ3JCLElBQUssSUFBSUMsT0FBT0MsVUFBVUMsTUFBTSxFQUFFQyxNQUFNLElBQUlDLE1BQU1KLE9BQU9LLE9BQU8sR0FBR0EsT0FBT0wsTUFBTUssT0FBUTtRQUN0RkYsR0FBRyxDQUFDRSxLQUFLLEdBQUdKLFNBQVMsQ0FBQ0ksS0FBSztJQUM3QjtJQUVBLE9BQU8sU0FBVUMsQ0FBQztRQUNoQixPQUFPSCxJQUFJSSxXQUFXLENBQUMsU0FBVUMsQ0FBQyxFQUFFQyxDQUFDO1lBQ25DLE9BQU9BLEVBQUVEO1FBQ1gsR0FBR0Y7SUFDTDtBQUNGO0FBRUEsaUVBQWVQLE9BQU9BLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9qYXZhLWxlYXJuaW5nLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL0Btb25hY28tZWRpdG9yL2xvYWRlci9saWIvZXMvdXRpbHMvY29tcG9zZS5qcz9hNjFjIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBjb21wb3NlID0gZnVuY3Rpb24gY29tcG9zZSgpIHtcbiAgZm9yICh2YXIgX2xlbiA9IGFyZ3VtZW50cy5sZW5ndGgsIGZucyA9IG5ldyBBcnJheShfbGVuKSwgX2tleSA9IDA7IF9rZXkgPCBfbGVuOyBfa2V5KyspIHtcbiAgICBmbnNbX2tleV0gPSBhcmd1bWVudHNbX2tleV07XG4gIH1cblxuICByZXR1cm4gZnVuY3Rpb24gKHgpIHtcbiAgICByZXR1cm4gZm5zLnJlZHVjZVJpZ2h0KGZ1bmN0aW9uICh5LCBmKSB7XG4gICAgICByZXR1cm4gZih5KTtcbiAgICB9LCB4KTtcbiAgfTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGNvbXBvc2U7XG4iXSwibmFtZXMiOlsiY29tcG9zZSIsIl9sZW4iLCJhcmd1bWVudHMiLCJsZW5ndGgiLCJmbnMiLCJBcnJheSIsIl9rZXkiLCJ4IiwicmVkdWNlUmlnaHQiLCJ5IiwiZiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/compose.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/curry.js":
/*!******************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/utils/curry.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction curry(fn) {\n    return function curried() {\n        var _this = this;\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        return args.length >= fn.length ? fn.apply(this, args) : function() {\n            for(var _len2 = arguments.length, nextArgs = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++){\n                nextArgs[_key2] = arguments[_key2];\n            }\n            return curried.apply(_this, [].concat(args, nextArgs));\n        };\n    };\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (curry);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy91dGlscy9jdXJyeS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsU0FBU0EsTUFBTUMsRUFBRTtJQUNmLE9BQU8sU0FBU0M7UUFDZCxJQUFJQyxRQUFRLElBQUk7UUFFaEIsSUFBSyxJQUFJQyxPQUFPQyxVQUFVQyxNQUFNLEVBQUVDLE9BQU8sSUFBSUMsTUFBTUosT0FBT0ssT0FBTyxHQUFHQSxPQUFPTCxNQUFNSyxPQUFRO1lBQ3ZGRixJQUFJLENBQUNFLEtBQUssR0FBR0osU0FBUyxDQUFDSSxLQUFLO1FBQzlCO1FBRUEsT0FBT0YsS0FBS0QsTUFBTSxJQUFJTCxHQUFHSyxNQUFNLEdBQUdMLEdBQUdTLEtBQUssQ0FBQyxJQUFJLEVBQUVILFFBQVE7WUFDdkQsSUFBSyxJQUFJSSxRQUFRTixVQUFVQyxNQUFNLEVBQUVNLFdBQVcsSUFBSUosTUFBTUcsUUFBUUUsUUFBUSxHQUFHQSxRQUFRRixPQUFPRSxRQUFTO2dCQUNqR0QsUUFBUSxDQUFDQyxNQUFNLEdBQUdSLFNBQVMsQ0FBQ1EsTUFBTTtZQUNwQztZQUVBLE9BQU9YLFFBQVFRLEtBQUssQ0FBQ1AsT0FBTyxFQUFFLENBQUNXLE1BQU0sQ0FBQ1AsTUFBTUs7UUFDOUM7SUFDRjtBQUNGO0FBRUEsaUVBQWVaLEtBQUtBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9qYXZhLWxlYXJuaW5nLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL0Btb25hY28tZWRpdG9yL2xvYWRlci9saWIvZXMvdXRpbHMvY3VycnkuanM/MTI1ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBjdXJyeShmbikge1xuICByZXR1cm4gZnVuY3Rpb24gY3VycmllZCgpIHtcbiAgICB2YXIgX3RoaXMgPSB0aGlzO1xuXG4gICAgZm9yICh2YXIgX2xlbiA9IGFyZ3VtZW50cy5sZW5ndGgsIGFyZ3MgPSBuZXcgQXJyYXkoX2xlbiksIF9rZXkgPSAwOyBfa2V5IDwgX2xlbjsgX2tleSsrKSB7XG4gICAgICBhcmdzW19rZXldID0gYXJndW1lbnRzW19rZXldO1xuICAgIH1cblxuICAgIHJldHVybiBhcmdzLmxlbmd0aCA+PSBmbi5sZW5ndGggPyBmbi5hcHBseSh0aGlzLCBhcmdzKSA6IGZ1bmN0aW9uICgpIHtcbiAgICAgIGZvciAodmFyIF9sZW4yID0gYXJndW1lbnRzLmxlbmd0aCwgbmV4dEFyZ3MgPSBuZXcgQXJyYXkoX2xlbjIpLCBfa2V5MiA9IDA7IF9rZXkyIDwgX2xlbjI7IF9rZXkyKyspIHtcbiAgICAgICAgbmV4dEFyZ3NbX2tleTJdID0gYXJndW1lbnRzW19rZXkyXTtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIGN1cnJpZWQuYXBwbHkoX3RoaXMsIFtdLmNvbmNhdChhcmdzLCBuZXh0QXJncykpO1xuICAgIH07XG4gIH07XG59XG5cbmV4cG9ydCBkZWZhdWx0IGN1cnJ5O1xuIl0sIm5hbWVzIjpbImN1cnJ5IiwiZm4iLCJjdXJyaWVkIiwiX3RoaXMiLCJfbGVuIiwiYXJndW1lbnRzIiwibGVuZ3RoIiwiYXJncyIsIkFycmF5IiwiX2tleSIsImFwcGx5IiwiX2xlbjIiLCJuZXh0QXJncyIsIl9rZXkyIiwiY29uY2F0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/curry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/deepMerge.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/utils/deepMerge.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js\");\n\nfunction merge(target, source) {\n    Object.keys(source).forEach(function(key) {\n        if (source[key] instanceof Object) {\n            if (target[key]) {\n                Object.assign(source[key], merge(target[key], source[key]));\n            }\n        }\n    });\n    return (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__.objectSpread2)((0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__.objectSpread2)({}, target), source);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (merge);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy91dGlscy9kZWVwTWVyZ2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMkY7QUFFM0YsU0FBU0UsTUFBTUMsTUFBTSxFQUFFQyxNQUFNO0lBQzNCQyxPQUFPQyxJQUFJLENBQUNGLFFBQVFHLE9BQU8sQ0FBQyxTQUFVQyxHQUFHO1FBQ3ZDLElBQUlKLE1BQU0sQ0FBQ0ksSUFBSSxZQUFZSCxRQUFRO1lBQ2pDLElBQUlGLE1BQU0sQ0FBQ0ssSUFBSSxFQUFFO2dCQUNmSCxPQUFPSSxNQUFNLENBQUNMLE1BQU0sQ0FBQ0ksSUFBSSxFQUFFTixNQUFNQyxNQUFNLENBQUNLLElBQUksRUFBRUosTUFBTSxDQUFDSSxJQUFJO1lBQzNEO1FBQ0Y7SUFDRjtJQUNBLE9BQU9QLG1GQUFjQSxDQUFDQSxtRkFBY0EsQ0FBQyxDQUFDLEdBQUdFLFNBQVNDO0FBQ3BEO0FBRUEsaUVBQWVGLEtBQUtBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9qYXZhLWxlYXJuaW5nLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL0Btb25hY28tZWRpdG9yL2xvYWRlci9saWIvZXMvdXRpbHMvZGVlcE1lcmdlLmpzP2I2YzUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgb2JqZWN0U3ByZWFkMiBhcyBfb2JqZWN0U3ByZWFkMiB9IGZyb20gJy4uL192aXJ0dWFsL19yb2xsdXBQbHVnaW5CYWJlbEhlbHBlcnMuanMnO1xuXG5mdW5jdGlvbiBtZXJnZSh0YXJnZXQsIHNvdXJjZSkge1xuICBPYmplY3Qua2V5cyhzb3VyY2UpLmZvckVhY2goZnVuY3Rpb24gKGtleSkge1xuICAgIGlmIChzb3VyY2Vba2V5XSBpbnN0YW5jZW9mIE9iamVjdCkge1xuICAgICAgaWYgKHRhcmdldFtrZXldKSB7XG4gICAgICAgIE9iamVjdC5hc3NpZ24oc291cmNlW2tleV0sIG1lcmdlKHRhcmdldFtrZXldLCBzb3VyY2Vba2V5XSkpO1xuICAgICAgfVxuICAgIH1cbiAgfSk7XG4gIHJldHVybiBfb2JqZWN0U3ByZWFkMihfb2JqZWN0U3ByZWFkMih7fSwgdGFyZ2V0KSwgc291cmNlKTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgbWVyZ2U7XG4iXSwibmFtZXMiOlsib2JqZWN0U3ByZWFkMiIsIl9vYmplY3RTcHJlYWQyIiwibWVyZ2UiLCJ0YXJnZXQiLCJzb3VyY2UiLCJPYmplY3QiLCJrZXlzIiwiZm9yRWFjaCIsImtleSIsImFzc2lnbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/deepMerge.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/isObject.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/utils/isObject.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction isObject(value) {\n    return ({}).toString.call(value).includes(\"Object\");\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (isObject);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy91dGlscy9pc09iamVjdC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsU0FBU0EsU0FBU0MsS0FBSztJQUNyQixPQUFPLEVBQUMsR0FBRUMsUUFBUSxDQUFDQyxJQUFJLENBQUNGLE9BQU9HLFFBQVEsQ0FBQztBQUMxQztBQUVBLGlFQUFlSixRQUFRQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vamF2YS1sZWFybmluZy1wbGF0Zm9ybS8uL25vZGVfbW9kdWxlcy9AbW9uYWNvLWVkaXRvci9sb2FkZXIvbGliL2VzL3V0aWxzL2lzT2JqZWN0LmpzPzRhNmQiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gaXNPYmplY3QodmFsdWUpIHtcbiAgcmV0dXJuIHt9LnRvU3RyaW5nLmNhbGwodmFsdWUpLmluY2x1ZGVzKCdPYmplY3QnKTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgaXNPYmplY3Q7XG4iXSwibmFtZXMiOlsiaXNPYmplY3QiLCJ2YWx1ZSIsInRvU3RyaW5nIiwiY2FsbCIsImluY2x1ZGVzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/isObject.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/makeCancelable.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/utils/makeCancelable.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CANCELATION_MESSAGE: () => (/* binding */ CANCELATION_MESSAGE),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// The source (has been changed) is https://github.com/facebook/react/issues/5465#issuecomment-157888325\nvar CANCELATION_MESSAGE = {\n    type: \"cancelation\",\n    msg: \"operation is manually canceled\"\n};\nfunction makeCancelable(promise) {\n    var hasCanceled_ = false;\n    var wrappedPromise = new Promise(function(resolve, reject) {\n        promise.then(function(val) {\n            return hasCanceled_ ? reject(CANCELATION_MESSAGE) : resolve(val);\n        });\n        promise[\"catch\"](reject);\n    });\n    return wrappedPromise.cancel = function() {\n        return hasCanceled_ = true;\n    }, wrappedPromise;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (makeCancelable);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/makeCancelable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/validators/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/validators/index.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   errorHandler: () => (/* binding */ errorHandler),\n/* harmony export */   errorMessages: () => (/* binding */ errorMessages)\n/* harmony export */ });\n/* harmony import */ var _utils_curry_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/curry.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/curry.js\");\n/* harmony import */ var _utils_isObject_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/isObject.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/isObject.js\");\n\n\n/**\n * validates the configuration object and informs about deprecation\n * @param {Object} config - the configuration object \n * @return {Object} config - the validated configuration object\n */ function validateConfig(config) {\n    if (!config) errorHandler(\"configIsRequired\");\n    if (!(0,_utils_isObject_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(config)) errorHandler(\"configType\");\n    if (config.urls) {\n        informAboutDeprecation();\n        return {\n            paths: {\n                vs: config.urls.monacoBase\n            }\n        };\n    }\n    return config;\n}\n/**\n * logs deprecation message\n */ function informAboutDeprecation() {\n    console.warn(errorMessages.deprecation);\n}\nfunction throwError(errorMessages, type) {\n    throw new Error(errorMessages[type] || errorMessages[\"default\"]);\n}\nvar errorMessages = {\n    configIsRequired: \"the configuration object is required\",\n    configType: \"the configuration object should be an object\",\n    \"default\": \"an unknown error accured in `@monaco-editor/loader` package\",\n    deprecation: \"Deprecation warning!\\n    You are using deprecated way of configuration.\\n\\n    Instead of using\\n      monaco.config({ urls: { monacoBase: '...' } })\\n    use\\n      monaco.config({ paths: { vs: '...' } })\\n\\n    For more please check the link https://github.com/suren-atoyan/monaco-loader#config\\n  \"\n};\nvar errorHandler = (0,_utils_curry_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(throwError)(errorMessages);\nvar validators = {\n    config: validateConfig\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (validators);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy92YWxpZGF0b3JzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXNDO0FBQ007QUFFNUM7Ozs7Q0FJQyxHQUVELFNBQVNFLGVBQWVDLE1BQU07SUFDNUIsSUFBSSxDQUFDQSxRQUFRQyxhQUFhO0lBQzFCLElBQUksQ0FBQ0gsOERBQVFBLENBQUNFLFNBQVNDLGFBQWE7SUFFcEMsSUFBSUQsT0FBT0UsSUFBSSxFQUFFO1FBQ2ZDO1FBQ0EsT0FBTztZQUNMQyxPQUFPO2dCQUNMQyxJQUFJTCxPQUFPRSxJQUFJLENBQUNJLFVBQVU7WUFDNUI7UUFDRjtJQUNGO0lBRUEsT0FBT047QUFDVDtBQUNBOztDQUVDLEdBR0QsU0FBU0c7SUFDUEksUUFBUUMsSUFBSSxDQUFDQyxjQUFjQyxXQUFXO0FBQ3hDO0FBRUEsU0FBU0MsV0FBV0YsYUFBYSxFQUFFRyxJQUFJO0lBQ3JDLE1BQU0sSUFBSUMsTUFBTUosYUFBYSxDQUFDRyxLQUFLLElBQUlILGFBQWEsQ0FBQyxVQUFVO0FBQ2pFO0FBRUEsSUFBSUEsZ0JBQWdCO0lBQ2xCSyxrQkFBa0I7SUFDbEJDLFlBQVk7SUFDWixXQUFXO0lBQ1hMLGFBQWE7QUFDZjtBQUNBLElBQUlULGVBQWVKLDJEQUFLQSxDQUFDYyxZQUFZRjtBQUNyQyxJQUFJTyxhQUFhO0lBQ2ZoQixRQUFRRDtBQUNWO0FBRUEsaUVBQWVpQixVQUFVQSxFQUFDO0FBQ2EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9qYXZhLWxlYXJuaW5nLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL0Btb25hY28tZWRpdG9yL2xvYWRlci9saWIvZXMvdmFsaWRhdG9ycy9pbmRleC5qcz8yYzM3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjdXJyeSBmcm9tICcuLi91dGlscy9jdXJyeS5qcyc7XG5pbXBvcnQgaXNPYmplY3QgZnJvbSAnLi4vdXRpbHMvaXNPYmplY3QuanMnO1xuXG4vKipcbiAqIHZhbGlkYXRlcyB0aGUgY29uZmlndXJhdGlvbiBvYmplY3QgYW5kIGluZm9ybXMgYWJvdXQgZGVwcmVjYXRpb25cbiAqIEBwYXJhbSB7T2JqZWN0fSBjb25maWcgLSB0aGUgY29uZmlndXJhdGlvbiBvYmplY3QgXG4gKiBAcmV0dXJuIHtPYmplY3R9IGNvbmZpZyAtIHRoZSB2YWxpZGF0ZWQgY29uZmlndXJhdGlvbiBvYmplY3RcbiAqL1xuXG5mdW5jdGlvbiB2YWxpZGF0ZUNvbmZpZyhjb25maWcpIHtcbiAgaWYgKCFjb25maWcpIGVycm9ySGFuZGxlcignY29uZmlnSXNSZXF1aXJlZCcpO1xuICBpZiAoIWlzT2JqZWN0KGNvbmZpZykpIGVycm9ySGFuZGxlcignY29uZmlnVHlwZScpO1xuXG4gIGlmIChjb25maWcudXJscykge1xuICAgIGluZm9ybUFib3V0RGVwcmVjYXRpb24oKTtcbiAgICByZXR1cm4ge1xuICAgICAgcGF0aHM6IHtcbiAgICAgICAgdnM6IGNvbmZpZy51cmxzLm1vbmFjb0Jhc2VcbiAgICAgIH1cbiAgICB9O1xuICB9XG5cbiAgcmV0dXJuIGNvbmZpZztcbn1cbi8qKlxuICogbG9ncyBkZXByZWNhdGlvbiBtZXNzYWdlXG4gKi9cblxuXG5mdW5jdGlvbiBpbmZvcm1BYm91dERlcHJlY2F0aW9uKCkge1xuICBjb25zb2xlLndhcm4oZXJyb3JNZXNzYWdlcy5kZXByZWNhdGlvbik7XG59XG5cbmZ1bmN0aW9uIHRocm93RXJyb3IoZXJyb3JNZXNzYWdlcywgdHlwZSkge1xuICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JNZXNzYWdlc1t0eXBlXSB8fCBlcnJvck1lc3NhZ2VzW1wiZGVmYXVsdFwiXSk7XG59XG5cbnZhciBlcnJvck1lc3NhZ2VzID0ge1xuICBjb25maWdJc1JlcXVpcmVkOiAndGhlIGNvbmZpZ3VyYXRpb24gb2JqZWN0IGlzIHJlcXVpcmVkJyxcbiAgY29uZmlnVHlwZTogJ3RoZSBjb25maWd1cmF0aW9uIG9iamVjdCBzaG91bGQgYmUgYW4gb2JqZWN0JyxcbiAgXCJkZWZhdWx0XCI6ICdhbiB1bmtub3duIGVycm9yIGFjY3VyZWQgaW4gYEBtb25hY28tZWRpdG9yL2xvYWRlcmAgcGFja2FnZScsXG4gIGRlcHJlY2F0aW9uOiBcIkRlcHJlY2F0aW9uIHdhcm5pbmchXFxuICAgIFlvdSBhcmUgdXNpbmcgZGVwcmVjYXRlZCB3YXkgb2YgY29uZmlndXJhdGlvbi5cXG5cXG4gICAgSW5zdGVhZCBvZiB1c2luZ1xcbiAgICAgIG1vbmFjby5jb25maWcoeyB1cmxzOiB7IG1vbmFjb0Jhc2U6ICcuLi4nIH0gfSlcXG4gICAgdXNlXFxuICAgICAgbW9uYWNvLmNvbmZpZyh7IHBhdGhzOiB7IHZzOiAnLi4uJyB9IH0pXFxuXFxuICAgIEZvciBtb3JlIHBsZWFzZSBjaGVjayB0aGUgbGluayBodHRwczovL2dpdGh1Yi5jb20vc3VyZW4tYXRveWFuL21vbmFjby1sb2FkZXIjY29uZmlnXFxuICBcIlxufTtcbnZhciBlcnJvckhhbmRsZXIgPSBjdXJyeSh0aHJvd0Vycm9yKShlcnJvck1lc3NhZ2VzKTtcbnZhciB2YWxpZGF0b3JzID0ge1xuICBjb25maWc6IHZhbGlkYXRlQ29uZmlnXG59O1xuXG5leHBvcnQgZGVmYXVsdCB2YWxpZGF0b3JzO1xuZXhwb3J0IHsgZXJyb3JIYW5kbGVyLCBlcnJvck1lc3NhZ2VzIH07XG4iXSwibmFtZXMiOlsiY3VycnkiLCJpc09iamVjdCIsInZhbGlkYXRlQ29uZmlnIiwiY29uZmlnIiwiZXJyb3JIYW5kbGVyIiwidXJscyIsImluZm9ybUFib3V0RGVwcmVjYXRpb24iLCJwYXRocyIsInZzIiwibW9uYWNvQmFzZSIsImNvbnNvbGUiLCJ3YXJuIiwiZXJyb3JNZXNzYWdlcyIsImRlcHJlY2F0aW9uIiwidGhyb3dFcnJvciIsInR5cGUiLCJFcnJvciIsImNvbmZpZ0lzUmVxdWlyZWQiLCJjb25maWdUeXBlIiwidmFsaWRhdG9ycyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/validators/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/react/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@monaco-editor/react/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DiffEditor: () => (/* binding */ we),\n/* harmony export */   Editor: () => (/* binding */ de),\n/* harmony export */   \"default\": () => (/* binding */ Ft),\n/* harmony export */   loader: () => (/* reexport safe */ _monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   useMonaco: () => (/* binding */ Le)\n/* harmony export */ });\n/* harmony import */ var _monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @monaco-editor/loader */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\n\n\n\nvar le = {\n    wrapper: {\n        display: \"flex\",\n        position: \"relative\",\n        textAlign: \"initial\"\n    },\n    fullWidth: {\n        width: \"100%\"\n    },\n    hide: {\n        display: \"none\"\n    }\n}, v = le;\n\nvar ae = {\n    container: {\n        display: \"flex\",\n        height: \"100%\",\n        width: \"100%\",\n        justifyContent: \"center\",\n        alignItems: \"center\"\n    }\n}, Y = ae;\nfunction Me({ children: e }) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n        style: Y.container\n    }, e);\n}\nvar Z = Me;\nvar $ = Z;\nfunction Ee({ width: e, height: r, isEditorReady: n, loading: t, _ref: a, className: m, wrapperProps: E }) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"section\", {\n        style: {\n            ...v.wrapper,\n            width: e,\n            height: r\n        },\n        ...E\n    }, !n && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement($, null, t), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n        ref: a,\n        style: {\n            ...v.fullWidth,\n            ...!n && v.hide\n        },\n        className: m\n    }));\n}\nvar ee = Ee;\nvar H = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(ee);\n\nfunction Ce(e) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(e, []);\n}\nvar k = Ce;\n\nfunction he(e, r, n = !0) {\n    let t = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(!0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(t.current || !n ? ()=>{\n        t.current = !1;\n    } : e, r);\n}\nvar l = he;\nfunction D() {}\nfunction h(e, r, n, t) {\n    return De(e, t) || be(e, r, n, t);\n}\nfunction De(e, r) {\n    return e.editor.getModel(te(e, r));\n}\nfunction be(e, r, n, t) {\n    return e.editor.createModel(r, n, t ? te(e, t) : void 0);\n}\nfunction te(e, r) {\n    return e.Uri.parse(r);\n}\nfunction Oe({ original: e, modified: r, language: n, originalLanguage: t, modifiedLanguage: a, originalModelPath: m, modifiedModelPath: E, keepCurrentOriginalModel: g = !1, keepCurrentModifiedModel: N = !1, theme: x = \"light\", loading: P = \"Loading...\", options: y = {}, height: V = \"100%\", width: z = \"100%\", className: F, wrapperProps: j = {}, beforeMount: A = D, onMount: q = D }) {\n    let [M, O] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!1), [T, s] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!0), u = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null), c = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null), w = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null), d = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(q), o = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(A), b = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(!1);\n    k(()=>{\n        let i = _monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__[\"default\"].init();\n        return i.then((f)=>(c.current = f) && s(!1)).catch((f)=>f?.type !== \"cancelation\" && console.error(\"Monaco initialization: error:\", f)), ()=>u.current ? I() : i.cancel();\n    }), l(()=>{\n        if (u.current && c.current) {\n            let i = u.current.getOriginalEditor(), f = h(c.current, e || \"\", t || n || \"text\", m || \"\");\n            f !== i.getModel() && i.setModel(f);\n        }\n    }, [\n        m\n    ], M), l(()=>{\n        if (u.current && c.current) {\n            let i = u.current.getModifiedEditor(), f = h(c.current, r || \"\", a || n || \"text\", E || \"\");\n            f !== i.getModel() && i.setModel(f);\n        }\n    }, [\n        E\n    ], M), l(()=>{\n        let i = u.current.getModifiedEditor();\n        i.getOption(c.current.editor.EditorOption.readOnly) ? i.setValue(r || \"\") : r !== i.getValue() && (i.executeEdits(\"\", [\n            {\n                range: i.getModel().getFullModelRange(),\n                text: r || \"\",\n                forceMoveMarkers: !0\n            }\n        ]), i.pushUndoStop());\n    }, [\n        r\n    ], M), l(()=>{\n        u.current?.getModel()?.original.setValue(e || \"\");\n    }, [\n        e\n    ], M), l(()=>{\n        let { original: i, modified: f } = u.current.getModel();\n        c.current.editor.setModelLanguage(i, t || n || \"text\"), c.current.editor.setModelLanguage(f, a || n || \"text\");\n    }, [\n        n,\n        t,\n        a\n    ], M), l(()=>{\n        c.current?.editor.setTheme(x);\n    }, [\n        x\n    ], M), l(()=>{\n        u.current?.updateOptions(y);\n    }, [\n        y\n    ], M);\n    let L = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!c.current) return;\n        o.current(c.current);\n        let i = h(c.current, e || \"\", t || n || \"text\", m || \"\"), f = h(c.current, r || \"\", a || n || \"text\", E || \"\");\n        u.current?.setModel({\n            original: i,\n            modified: f\n        });\n    }, [\n        n,\n        r,\n        a,\n        e,\n        t,\n        m,\n        E\n    ]), U = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        !b.current && w.current && (u.current = c.current.editor.createDiffEditor(w.current, {\n            automaticLayout: !0,\n            ...y\n        }), L(), c.current?.editor.setTheme(x), O(!0), b.current = !0);\n    }, [\n        y,\n        x,\n        L\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        M && d.current(u.current, c.current);\n    }, [\n        M\n    ]), (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        !T && !M && U();\n    }, [\n        T,\n        M,\n        U\n    ]);\n    function I() {\n        let i = u.current?.getModel();\n        g || i?.original?.dispose(), N || i?.modified?.dispose(), u.current?.dispose();\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(H, {\n        width: z,\n        height: V,\n        isEditorReady: M,\n        loading: P,\n        _ref: w,\n        className: F,\n        wrapperProps: j\n    });\n}\nvar ie = Oe;\nvar we = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(ie);\n\n\nfunction Pe() {\n    let [e, r] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__[\"default\"].__getMonacoInstance());\n    return k(()=>{\n        let n;\n        return e || (n = _monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__[\"default\"].init(), n.then((t)=>{\n            r(t);\n        })), ()=>n?.cancel();\n    }), e;\n}\nvar Le = Pe;\n\n\n\n\nfunction He(e) {\n    let r = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        r.current = e;\n    }, [\n        e\n    ]), r.current;\n}\nvar se = He;\nvar _ = new Map;\nfunction Ve({ defaultValue: e, defaultLanguage: r, defaultPath: n, value: t, language: a, path: m, theme: E = \"light\", line: g, loading: N = \"Loading...\", options: x = {}, overrideServices: P = {}, saveViewState: y = !0, keepCurrentModel: V = !1, width: z = \"100%\", height: F = \"100%\", className: j, wrapperProps: A = {}, beforeMount: q = D, onMount: M = D, onChange: O, onValidate: T = D }) {\n    let [s, u] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!1), [c, w] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!0), d = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null), o = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null), b = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null), L = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(M), U = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(q), I = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(), i = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(t), f = se(m), Q = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(!1), B = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(!1);\n    k(()=>{\n        let p = _monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__[\"default\"].init();\n        return p.then((R)=>(d.current = R) && w(!1)).catch((R)=>R?.type !== \"cancelation\" && console.error(\"Monaco initialization: error:\", R)), ()=>o.current ? pe() : p.cancel();\n    }), l(()=>{\n        let p = h(d.current, e || t || \"\", r || a || \"\", m || n || \"\");\n        p !== o.current?.getModel() && (y && _.set(f, o.current?.saveViewState()), o.current?.setModel(p), y && o.current?.restoreViewState(_.get(m)));\n    }, [\n        m\n    ], s), l(()=>{\n        o.current?.updateOptions(x);\n    }, [\n        x\n    ], s), l(()=>{\n        !o.current || t === void 0 || (o.current.getOption(d.current.editor.EditorOption.readOnly) ? o.current.setValue(t) : t !== o.current.getValue() && (B.current = !0, o.current.executeEdits(\"\", [\n            {\n                range: o.current.getModel().getFullModelRange(),\n                text: t,\n                forceMoveMarkers: !0\n            }\n        ]), o.current.pushUndoStop(), B.current = !1));\n    }, [\n        t\n    ], s), l(()=>{\n        let p = o.current?.getModel();\n        p && a && d.current?.editor.setModelLanguage(p, a);\n    }, [\n        a\n    ], s), l(()=>{\n        g !== void 0 && o.current?.revealLine(g);\n    }, [\n        g\n    ], s), l(()=>{\n        d.current?.editor.setTheme(E);\n    }, [\n        E\n    ], s);\n    let X = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!(!b.current || !d.current) && !Q.current) {\n            U.current(d.current);\n            let p = m || n, R = h(d.current, t || e || \"\", r || a || \"\", p || \"\");\n            o.current = d.current?.editor.create(b.current, {\n                model: R,\n                automaticLayout: !0,\n                ...x\n            }, P), y && o.current.restoreViewState(_.get(p)), d.current.editor.setTheme(E), g !== void 0 && o.current.revealLine(g), u(!0), Q.current = !0;\n        }\n    }, [\n        e,\n        r,\n        n,\n        t,\n        a,\n        m,\n        x,\n        P,\n        y,\n        E,\n        g\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        s && L.current(o.current, d.current);\n    }, [\n        s\n    ]), (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        !c && !s && X();\n    }, [\n        c,\n        s,\n        X\n    ]), i.current = t, (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        s && O && (I.current?.dispose(), I.current = o.current?.onDidChangeModelContent((p)=>{\n            B.current || O(o.current.getValue(), p);\n        }));\n    }, [\n        s,\n        O\n    ]), (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (s) {\n            let p = d.current.editor.onDidChangeMarkers((R)=>{\n                let G = o.current.getModel()?.uri;\n                if (G && R.find((J)=>J.path === G.path)) {\n                    let J = d.current.editor.getModelMarkers({\n                        resource: G\n                    });\n                    T?.(J);\n                }\n            });\n            return ()=>{\n                p?.dispose();\n            };\n        }\n        return ()=>{};\n    }, [\n        s,\n        T\n    ]);\n    function pe() {\n        I.current?.dispose(), V ? y && _.set(m, o.current.saveViewState()) : o.current.getModel()?.dispose(), o.current.dispose();\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(H, {\n        width: z,\n        height: F,\n        isEditorReady: s,\n        loading: N,\n        _ref: b,\n        className: j,\n        wrapperProps: A\n    });\n}\nvar fe = Ve;\nvar de = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(fe);\nvar Ft = de;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/react/dist/index.mjs\n");

/***/ })

};
;