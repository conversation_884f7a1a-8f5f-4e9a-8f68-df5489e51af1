'use client'

import { useState, useRef, useEffect } from 'react'
import Editor from '@monaco-editor/react'
import { Play, Square, RotateCcw, Copy, Download, Settings } from 'lucide-react'
import toast from 'react-hot-toast'

interface CodeEditorProps {
  initialCode?: string
  language?: string
  theme?: string
  height?: string
  readOnly?: boolean
  onCodeChange?: (code: string) => void
  onRun?: (code: string) => void
  showOutput?: boolean
  expectedOutput?: string
}

export default function CodeEditor({
  initialCode = '// Write your Java code here\npublic class Main {\n    public static void main(String[] args) {\n        System.out.println("Hello, World!");\n    }\n}',
  language = 'java',
  theme = 'vs-dark',
  height = '400px',
  readOnly = false,
  onCodeChange,
  onRun,
  showOutput = true,
  expectedOutput
}: CodeEditorProps) {
  const [code, setCode] = useState(initialCode)
  const [output, setOutput] = useState('')
  const [isRunning, setIsRunning] = useState(false)
  const [executionTime, setExecutionTime] = useState<number | null>(null)
  const editorRef = useRef<any>(null)

  const handleEditorDidMount = (editor: any, monaco: any) => {
    editorRef.current = editor
    
    // Configure Java language features
    monaco.languages.java?.setDiagnosticsOptions({
      noSemanticValidation: false,
      noSyntaxValidation: false,
    })

    // Add custom keyboard shortcuts
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.Enter, () => {
      handleRunCode()
    })
  }

  const handleCodeChange = (value: string | undefined) => {
    const newCode = value || ''
    setCode(newCode)
    onCodeChange?.(newCode)
  }

  const handleRunCode = async () => {
    if (isRunning) return

    setIsRunning(true)
    setOutput('')
    const startTime = Date.now()

    try {
      // Call the parent's onRun function if provided
      if (onRun) {
        onRun(code)
        setIsRunning(false)
        return
      }

      console.log('🚀 Executing code:', code.substring(0, 100) + '...')

      // Default execution logic
      const response = await fetch('/api/execute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code,
          language: 'java',
        }),
      })

      console.log('📡 Response status:', response.status)
      console.log('📡 Response headers:', [...response.headers.entries()])

      if (!response.ok) {
        const errorText = await response.text()
        console.log('❌ Response error text:', errorText)
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`)
      }

      const result = await response.json()
      console.log('✅ Execution result:', result)

      const endTime = Date.now()
      setExecutionTime(endTime - startTime)

      if (result.success) {
        setOutput(result.output || 'Program executed successfully (no output)')
        toast.success('Code executed successfully!')

        // Check if output matches expected output
        if (expectedOutput && result.output?.trim() === expectedOutput.trim()) {
          toast.success('Correct! Your output matches the expected result.')
        } else if (expectedOutput) {
          toast.error('Output doesn\'t match expected result. Try again!')
        }
      } else {
        setOutput(`Error: ${result.error}`)
        toast.error('Compilation or runtime error occurred')
      }
    } catch (error) {
      console.error('Code execution error:', error)
      setOutput(`Error: Failed to execute code. ${error instanceof Error ? error.message : 'Please try again.'}`)
      toast.error('Failed to execute code')
    } finally {
      setIsRunning(false)
    }
  }

  const handleStopExecution = () => {
    setIsRunning(false)
    setOutput('Execution stopped by user')
  }

  const handleResetCode = () => {
    setCode(initialCode)
    setOutput('')
    setExecutionTime(null)
    toast.success('Code reset to initial state')
  }

  const handleCopyCode = () => {
    navigator.clipboard.writeText(code)
    toast.success('Code copied to clipboard')
  }

  const handleDownloadCode = () => {
    const blob = new Blob([code], { type: 'text/java' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'Main.java'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    toast.success('Code downloaded as Main.java')
  }

  const formatCode = () => {
    if (editorRef.current) {
      editorRef.current.getAction('editor.action.formatDocument').run()
      toast.success('Code formatted')
    }
  }

  return (
    <div className="code-editor bg-white rounded-lg shadow-lg overflow-hidden">
      {/* Toolbar */}
      <div className="bg-gray-800 text-white px-4 py-2 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium">Java Editor</span>
          {executionTime && (
            <span className="text-xs text-gray-300">
              Executed in {executionTime}ms
            </span>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={handleCopyCode}
            className="p-1 hover:bg-gray-700 rounded transition-colors"
            title="Copy code"
          >
            <Copy className="w-4 h-4" />
          </button>
          <button
            onClick={handleDownloadCode}
            className="p-1 hover:bg-gray-700 rounded transition-colors"
            title="Download code"
          >
            <Download className="w-4 h-4" />
          </button>
          <button
            onClick={formatCode}
            className="p-1 hover:bg-gray-700 rounded transition-colors"
            title="Format code"
          >
            <Settings className="w-4 h-4" />
          </button>
          <button
            onClick={handleResetCode}
            className="p-1 hover:bg-gray-700 rounded transition-colors"
            title="Reset code"
          >
            <RotateCcw className="w-4 h-4" />
          </button>
          {isRunning ? (
            <button
              onClick={handleStopExecution}
              className="flex items-center space-x-1 bg-red-600 hover:bg-red-700 px-3 py-1 rounded text-sm transition-colors"
            >
              <Square className="w-4 h-4" />
              <span>Stop</span>
            </button>
          ) : (
            <button
              onClick={handleRunCode}
              className="flex items-center space-x-1 bg-green-600 hover:bg-green-700 px-3 py-1 rounded text-sm transition-colors"
              disabled={readOnly}
            >
              <Play className="w-4 h-4" />
              <span>Run</span>
            </button>
          )}
        </div>
      </div>

      {/* Editor */}
      <div className="relative">
        <Editor
          height={height}
          language={language}
          theme={theme}
          value={code}
          onChange={handleCodeChange}
          onMount={handleEditorDidMount}
          options={{
            readOnly,
            minimap: { enabled: false },
            fontSize: 14,
            lineNumbers: 'on',
            roundedSelection: false,
            scrollBeyondLastLine: false,
            automaticLayout: true,
            tabSize: 4,
            insertSpaces: true,
            wordWrap: 'on',
            contextmenu: true,
            selectOnLineNumbers: true,
            glyphMargin: true,
            folding: true,
            foldingStrategy: 'indentation',
            showFoldingControls: 'always',
            bracketPairColorization: { enabled: true },
            guides: {
              bracketPairs: true,
              indentation: true,
            },
          }}
        />
        {isRunning && (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
            <div className="bg-white rounded-lg p-4 flex items-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
              <span className="text-gray-700">Executing code...</span>
            </div>
          </div>
        )}
      </div>

      {/* Output Panel */}
      {showOutput && (
        <div className="border-t border-gray-200">
          <div className="bg-gray-100 px-4 py-2 border-b border-gray-200">
            <span className="text-sm font-medium text-gray-700">Output</span>
          </div>
          <div className="p-4 bg-gray-900 text-green-400 font-mono text-sm min-h-[100px] max-h-[200px] overflow-y-auto">
            {output ? (
              <pre className="whitespace-pre-wrap">{output}</pre>
            ) : (
              <span className="text-gray-500">Click "Run" to execute your code</span>
            )}
          </div>
        </div>
      )}

      {/* Expected Output (if provided) */}
      {expectedOutput && (
        <div className="border-t border-gray-200">
          <div className="bg-blue-50 px-4 py-2 border-b border-blue-200">
            <span className="text-sm font-medium text-blue-700">Expected Output</span>
          </div>
          <div className="p-4 bg-blue-900 text-blue-100 font-mono text-sm">
            <pre className="whitespace-pre-wrap">{expectedOutput}</pre>
          </div>
        </div>
      )}
    </div>
  )
}
