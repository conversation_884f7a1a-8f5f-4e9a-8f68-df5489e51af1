const express = require('express')
const http = require('http')
const socketIo = require('socket.io')
const cors = require('cors')
const fs = require('fs').promises
const path = require('path')
const { exec } = require('child_process')
const { promisify } = require('util')

const execAsync = promisify(exec)

const app = express()
const server = http.createServer(app)
const io = socketIo(server, {
  cors: {
    origin: ["http://localhost:3000", "http://127.0.0.1:3000"],
    methods: ["GET", "POST"],
    credentials: true
  }
})

const PORT = process.env.PORT || 3001

// Middleware
app.use(cors({
  origin: ["http://localhost:3000", "http://127.0.0.1:3000"],
  credentials: true
}))
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// Create temp directory for code execution
const TEMP_DIR = path.join(__dirname, 'temp')

async function ensureTempDir() {
  try {
    await fs.access(TEMP_DIR)
  } catch {
    await fs.mkdir(TEMP_DIR, { recursive: true })
  }
}

// Initialize temp directory
ensureTempDir()

// Code execution endpoint
app.post('/api/execute', async (req, res) => {
  const { code, language = 'java' } = req.body

  if (!code) {
    return res.status(400).json({ success: false, error: 'No code provided' })
  }

  if (language !== 'java') {
    return res.status(400).json({ success: false, error: 'Only Java is supported currently' })
  }

  const sessionId = Date.now().toString()
  const fileName = `Main_${sessionId}.java`
  const filePath = path.join(TEMP_DIR, fileName)
  const classPath = path.join(TEMP_DIR, `Main_${sessionId}.class`)

  try {
    // Write Java code to file
    await fs.writeFile(filePath, code)

    // Compile Java code
    const compileCommand = `javac "${filePath}"`
    const { stderr: compileError } = await execAsync(compileCommand)

    if (compileError) {
      return res.json({
        success: false,
        error: `Compilation Error: ${compileError}`
      })
    }

    // Execute Java code with timeout
    const executeCommand = `cd "${TEMP_DIR}" && timeout 10s java Main_${sessionId}`
    const { stdout, stderr } = await execAsync(executeCommand)

    if (stderr && !stderr.includes('Note:')) {
      return res.json({
        success: false,
        error: `Runtime Error: ${stderr}`
      })
    }

    res.json({
      success: true,
      output: stdout.trim()
    })

  } catch (error) {
    let errorMessage = 'Execution failed'
    
    if (error.code === 124) {
      errorMessage = 'Execution timeout (10 seconds exceeded)'
    } else if (error.stderr) {
      errorMessage = error.stderr
    } else if (error.message) {
      errorMessage = error.message
    }

    res.json({
      success: false,
      error: errorMessage
    })
  } finally {
    // Cleanup files
    try {
      await fs.unlink(filePath).catch(() => {})
      await fs.unlink(classPath).catch(() => {})
    } catch (error) {
      console.error('Cleanup error:', error)
    }
  }
})

// Quiz endpoints
app.get('/api/quizzes', (req, res) => {
  const quizzes = [
    {
      id: 1,
      title: 'Java Basics Quiz',
      description: 'Test your knowledge of Java fundamentals',
      difficulty: 'Beginner',
      questions: 10,
      timeLimit: 15
    },
    {
      id: 2,
      title: 'Object-Oriented Programming',
      description: 'Quiz on OOP concepts in Java',
      difficulty: 'Intermediate',
      questions: 15,
      timeLimit: 20
    },
    {
      id: 3,
      title: 'Advanced Java Concepts',
      description: 'Test your advanced Java knowledge',
      difficulty: 'Advanced',
      questions: 20,
      timeLimit: 30
    }
  ]
  
  res.json(quizzes)
})

app.get('/api/quizzes/:id', (req, res) => {
  const quizId = parseInt(req.params.id)
  
  // Mock quiz data
  const quiz = {
    id: quizId,
    title: 'Java Basics Quiz',
    description: 'Test your knowledge of Java fundamentals',
    timeLimit: 15,
    questions: [
      {
        id: 1,
        question: 'Which of the following is NOT a primitive data type in Java?',
        type: 'multiple-choice',
        options: ['int', 'String', 'boolean', 'char'],
        correctAnswer: 1,
        explanation: 'String is a reference type, not a primitive data type in Java.'
      },
      {
        id: 2,
        question: 'What is the correct way to declare a variable in Java?',
        type: 'multiple-choice',
        options: [
          'var name = "John";',
          'String name = "John";',
          'name = "John";',
          'declare String name = "John";'
        ],
        correctAnswer: 1,
        explanation: 'In Java, you must specify the data type when declaring a variable.'
      },
      {
        id: 3,
        question: 'Write a Java statement to print "Hello World" to the console.',
        type: 'code',
        correctAnswer: 'System.out.println("Hello World");',
        explanation: 'System.out.println() is used to print text to the console in Java.'
      }
    ]
  }
  
  res.json(quiz)
})

// Progress tracking endpoints
app.get('/api/progress/:userId', (req, res) => {
  const userId = req.params.userId
  
  // Mock progress data
  const progress = {
    userId,
    lessonsCompleted: 12,
    totalLessons: 50,
    quizzesCompleted: 8,
    totalQuizzes: 25,
    certificatesEarned: 2,
    currentStreak: 5,
    totalPoints: 1250,
    rank: 156,
    recentActivity: [
      { type: 'lesson', title: 'Java Basics: Variables', date: '2024-01-15', completed: true },
      { type: 'quiz', title: 'OOP Quiz', date: '2024-01-14', completed: true },
      { type: 'lesson', title: 'Control Structures', date: '2024-01-13', completed: false }
    ]
  }
  
  res.json(progress)
})

// Certificate generation endpoint
app.post('/api/certificates', async (req, res) => {
  const { userId, courseId, courseName, completionDate } = req.body
  
  // Mock certificate generation
  const certificate = {
    id: Date.now().toString(),
    userId,
    courseId,
    courseName,
    completionDate,
    certificateUrl: `/certificates/${Date.now()}.pdf`,
    verificationCode: Math.random().toString(36).substring(2, 15)
  }
  
  res.json({
    success: true,
    certificate
  })
})

// Lessons endpoints
app.get('/api/lessons', (req, res) => {
  const lessons = [
    {
      id: 1,
      title: 'Java Basics: Variables and Data Types',
      description: 'Learn about variables and primitive data types',
      difficulty: 'Beginner',
      duration: '30 min',
      completed: false
    },
    {
      id: 2,
      title: 'Control Structures',
      description: 'Learn about if statements, loops, and switch cases',
      difficulty: 'Beginner',
      duration: '45 min',
      completed: false
    },
    {
      id: 3,
      title: 'Object-Oriented Programming',
      description: 'Introduction to classes, objects, and OOP principles',
      difficulty: 'Intermediate',
      duration: '60 min',
      completed: false
    }
  ]
  
  res.json(lessons)
})

// Socket.io for real-time features
io.on('connection', (socket) => {
  console.log('User connected:', socket.id)
  
  socket.on('join-lesson', (lessonId) => {
    socket.join(`lesson-${lessonId}`)
    console.log(`User ${socket.id} joined lesson ${lessonId}`)
  })
  
  socket.on('code-change', (data) => {
    socket.to(`lesson-${data.lessonId}`).emit('code-update', {
      userId: socket.id,
      code: data.code
    })
  })
  
  socket.on('quiz-answer', (data) => {
    // Handle real-time quiz answers
    socket.to(`quiz-${data.quizId}`).emit('answer-submitted', {
      userId: socket.id,
      questionId: data.questionId,
      answer: data.answer
    })
  })
  
  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id)
  })
})

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() })
})

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Server error:', error)
  res.status(500).json({
    success: false,
    error: 'Internal server error'
  })
})

server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`)
  console.log(`Health check: http://localhost:${PORT}/health`)
})
