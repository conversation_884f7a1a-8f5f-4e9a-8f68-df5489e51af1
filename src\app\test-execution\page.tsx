'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { <PERSON>, Bug, CheckCircle, XCircle } from 'lucide-react'
import toast from 'react-hot-toast'

export default function TestExecutionPage() {
  const [isRunning, setIsRunning] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [logs, setLogs] = useState<string[]>([])

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
    console.log(message)
  }

  const testCode = `public class HelloWorld {
    public static void main(String[] args) {
        System.out.println("Hello, World!");
        System.out.println("Testing Java execution");
        
        int a = 10;
        int b = 20;
        System.out.println("Sum: " + (a + b));
        
        String name = "Alice";
        System.out.println("Name: " + name);
    }
}`

  const runTest = async () => {
    setIsRunning(true)
    setResult(null)
    setLogs([])
    
    addLog('🚀 Starting code execution test...')
    
    try {
      addLog('📡 Sending request to /api/execute...')
      
      const response = await fetch('/api/execute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code: testCode,
          language: 'java'
        })
      })

      addLog(`📡 Response status: ${response.status}`)
      addLog(`📡 Response headers: ${JSON.stringify([...response.headers.entries()])}`)

      if (!response.ok) {
        const errorText = await response.text()
        addLog(`❌ Response error: ${errorText}`)
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      addLog(`✅ Response received: ${JSON.stringify(result, null, 2)}`)
      
      setResult(result)
      
      if (result.success) {
        toast.success('Code execution successful!')
        addLog('🎉 Test completed successfully!')
      } else {
        toast.error('Code execution failed!')
        addLog(`❌ Test failed: ${result.error}`)
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      addLog(`💥 Test error: ${errorMessage}`)
      setResult({ success: false, error: errorMessage })
      toast.error('Test failed!')
    } finally {
      setIsRunning(false)
    }
  }

  const testAPI = async () => {
    try {
      addLog('🔧 Testing basic API endpoint...')
      const response = await fetch('/api/test')
      const result = await response.json()
      addLog(`✅ API test result: ${JSON.stringify(result)}`)
      toast.success('API endpoint working!')
    } catch (error) {
      addLog(`❌ API test error: ${error}`)
      toast.error('API endpoint failed!')
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center mb-8">
            <Bug className="w-8 h-8 text-red-600 mr-3" />
            <h1 className="text-3xl font-bold text-gray-900">Code Execution Test</h1>
          </div>

          {/* Test Controls */}
          <div className="card p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">Test Controls</h2>
            <div className="flex space-x-4">
              <button
                onClick={testAPI}
                className="btn-secondary"
              >
                Test API Endpoint
              </button>
              <button
                onClick={runTest}
                disabled={isRunning}
                className="btn-primary"
              >
                {isRunning ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Running Test...
                  </div>
                ) : (
                  <>
                    <Play className="w-4 h-4 mr-2" />
                    Run Code Execution Test
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Test Code */}
          <div className="card p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">Test Code</h2>
            <pre className="bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto text-sm">
              {testCode}
            </pre>
          </div>

          {/* Result */}
          {result && (
            <div className="card p-6 mb-6">
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                {result.success ? (
                  <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                ) : (
                  <XCircle className="w-5 h-5 text-red-600 mr-2" />
                )}
                Test Result
              </h2>
              
              {result.success ? (
                <div>
                  <h3 className="font-medium text-green-800 mb-2">✅ Success!</h3>
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h4 className="font-medium mb-2">Output:</h4>
                    <pre className="whitespace-pre-wrap text-sm">{result.output}</pre>
                  </div>
                </div>
              ) : (
                <div>
                  <h3 className="font-medium text-red-800 mb-2">❌ Failed!</h3>
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <h4 className="font-medium mb-2">Error:</h4>
                    <pre className="whitespace-pre-wrap text-sm text-red-800">{result.error}</pre>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Debug Logs */}
          <div className="card p-6">
            <h2 className="text-xl font-semibold mb-4">Debug Logs</h2>
            <div className="bg-gray-900 text-green-400 p-4 rounded-lg max-h-96 overflow-y-auto">
              {logs.length === 0 ? (
                <div className="text-gray-500">No logs yet. Click "Run Code Execution Test" to start.</div>
              ) : (
                logs.map((log, index) => (
                  <div key={index} className="text-sm mb-1">
                    {log}
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Instructions */}
          <div className="card p-6 mt-6">
            <h2 className="text-xl font-semibold mb-4">Instructions</h2>
            <div className="space-y-2 text-sm text-gray-600">
              <p>1. <strong>Test API Endpoint</strong>: Verifies the basic API is working</p>
              <p>2. <strong>Run Code Execution Test</strong>: Tests the Java code execution functionality</p>
              <p>3. Check the <strong>Debug Logs</strong> for detailed information about what's happening</p>
              <p>4. If the test fails, check the browser console (F12) for additional error details</p>
              <p>5. The system will use simulation mode if Java is not installed</p>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}
