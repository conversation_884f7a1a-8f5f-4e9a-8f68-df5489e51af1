"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/lessons/[id]/page",{

/***/ "(app-pages-browser)/./src/components/CodeEditor.tsx":
/*!***************************************!*\
  !*** ./src/components/CodeEditor.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CodeEditor; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @monaco-editor/react */ \"(app-pages-browser)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Download,Play,RotateCcw,Settings,Square!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Download,Play,RotateCcw,Settings,Square!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Download,Play,RotateCcw,Settings,Square!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Download,Play,RotateCcw,Settings,Square!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Download,Play,RotateCcw,Settings,Square!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Download,Play,RotateCcw,Settings,Square!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction CodeEditor(param) {\n    let { initialCode = '// Write your Java code here\\npublic class Main {\\n    public static void main(String[] args) {\\n        System.out.println(\"Hello, World!\");\\n    }\\n}', language = \"java\", theme = \"vs-dark\", height = \"400px\", readOnly = false, onCodeChange, onRun, showOutput = true, expectedOutput } = param;\n    _s();\n    const [code, setCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialCode);\n    const [output, setOutput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isRunning, setIsRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [executionTime, setExecutionTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleEditorDidMount = (editor, monaco)=>{\n        var // Configure Java language features\n        _monaco_languages_java;\n        editorRef.current = editor;\n        (_monaco_languages_java = monaco.languages.java) === null || _monaco_languages_java === void 0 ? void 0 : _monaco_languages_java.setDiagnosticsOptions({\n            noSemanticValidation: false,\n            noSyntaxValidation: false\n        });\n        // Add custom keyboard shortcuts\n        editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.Enter, ()=>{\n            handleRunCode();\n        });\n    };\n    const handleCodeChange = (value)=>{\n        const newCode = value || \"\";\n        setCode(newCode);\n        onCodeChange === null || onCodeChange === void 0 ? void 0 : onCodeChange(newCode);\n    };\n    const handleRunCode = async ()=>{\n        if (isRunning) return;\n        setIsRunning(true);\n        setOutput(\"\");\n        const startTime = Date.now();\n        try {\n            // Call the parent's onRun function if provided, but still handle execution\n            if (onRun) {\n                console.log(\"\\uD83D\\uDD04 Using parent onRun function, but also executing locally for output\");\n                // Call parent function but don't return - continue with local execution for output\n                onRun(code);\n            }\n            console.log(\"\\uD83D\\uDE80 Executing code:\", code.substring(0, 100) + \"...\");\n            // Default execution logic with absolute URL\n            const apiUrl = window.location.origin + \"/api/execute\";\n            console.log(\"\\uD83D\\uDCE1 API URL:\", apiUrl);\n            const requestBody = {\n                code,\n                language: \"java\"\n            };\n            console.log(\"\\uD83D\\uDCE1 Request body:\", requestBody);\n            const response = await fetch(apiUrl, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestBody)\n            });\n            console.log(\"\\uD83D\\uDCE1 Response status:\", response.status);\n            console.log(\"\\uD83D\\uDCE1 Response headers:\", [\n                ...response.headers.entries()\n            ]);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.log(\"❌ Response error text:\", errorText);\n                throw new Error(\"HTTP error! status: \".concat(response.status, \", message: \").concat(errorText));\n            }\n            const result = await response.json();\n            console.log(\"✅ Execution result:\", result);\n            const endTime = Date.now();\n            setExecutionTime(endTime - startTime);\n            if (result.success) {\n                var _result_output;\n                setOutput(result.output || \"Program executed successfully (no output)\");\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"Code executed successfully!\");\n                // Check if output matches expected output\n                if (expectedOutput && ((_result_output = result.output) === null || _result_output === void 0 ? void 0 : _result_output.trim()) === expectedOutput.trim()) {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"Correct! Your output matches the expected result.\");\n                } else if (expectedOutput) {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"Output doesn't match expected result. Try again!\");\n                }\n            } else {\n                setOutput(\"Error: \".concat(result.error));\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"Compilation or runtime error occurred\");\n            }\n        } catch (error) {\n            console.error(\"Code execution error:\", error);\n            setOutput(\"Error: Failed to execute code. \".concat(error instanceof Error ? error.message : \"Please try again.\"));\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"Failed to execute code\");\n        } finally{\n            setIsRunning(false);\n        }\n    };\n    const handleStopExecution = ()=>{\n        setIsRunning(false);\n        setOutput(\"Execution stopped by user\");\n    };\n    const handleResetCode = ()=>{\n        setCode(initialCode);\n        setOutput(\"\");\n        setExecutionTime(null);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"Code reset to initial state\");\n    };\n    const handleCopyCode = ()=>{\n        navigator.clipboard.writeText(code);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"Code copied to clipboard\");\n    };\n    const handleDownloadCode = ()=>{\n        const blob = new Blob([\n            code\n        ], {\n            type: \"text/java\"\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = \"Main.java\";\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"Code downloaded as Main.java\");\n    };\n    const formatCode = ()=>{\n        if (editorRef.current) {\n            editorRef.current.getAction(\"editor.action.formatDocument\").run();\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"Code formatted\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"code-editor bg-white rounded-lg shadow-lg overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 text-white px-4 py-2 flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium\",\n                                children: \"Java Editor\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 11\n                            }, this),\n                            executionTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-300\",\n                                children: [\n                                    \"Executed in \",\n                                    executionTime,\n                                    \"ms\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCopyCode,\n                                className: \"p-1 hover:bg-gray-700 rounded transition-colors\",\n                                title: \"Copy code\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleDownloadCode,\n                                className: \"p-1 hover:bg-gray-700 rounded transition-colors\",\n                                title: \"Download code\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: formatCode,\n                                className: \"p-1 hover:bg-gray-700 rounded transition-colors\",\n                                title: \"Format code\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleResetCode,\n                                className: \"p-1 hover:bg-gray-700 rounded transition-colors\",\n                                title: \"Reset code\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, this),\n                            isRunning ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleStopExecution,\n                                className: \"flex items-center space-x-1 bg-red-600 hover:bg-red-700 px-3 py-1 rounded text-sm transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Stop\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleRunCode,\n                                className: \"flex items-center space-x-1 bg-green-600 hover:bg-green-700 px-3 py-1 rounded text-sm transition-colors\",\n                                disabled: readOnly,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Run\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        height: height,\n                        language: language,\n                        theme: theme,\n                        value: code,\n                        onChange: handleCodeChange,\n                        onMount: handleEditorDidMount,\n                        options: {\n                            readOnly,\n                            minimap: {\n                                enabled: false\n                            },\n                            fontSize: 14,\n                            lineNumbers: \"on\",\n                            roundedSelection: false,\n                            scrollBeyondLastLine: false,\n                            automaticLayout: true,\n                            tabSize: 4,\n                            insertSpaces: true,\n                            wordWrap: \"on\",\n                            contextmenu: true,\n                            selectOnLineNumbers: true,\n                            glyphMargin: true,\n                            folding: true,\n                            foldingStrategy: \"indentation\",\n                            showFoldingControls: \"always\",\n                            bracketPairColorization: {\n                                enabled: true\n                            },\n                            guides: {\n                                bracketPairs: true,\n                                indentation: true\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, this),\n                    isRunning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg p-4 flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-700\",\n                                    children: \"Executing code...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, this),\n            showOutput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-100 px-4 py-2 border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium text-gray-700\",\n                            children: \"Output\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 bg-gray-900 text-green-400 font-mono text-sm min-h-[100px] max-h-[200px] overflow-y-auto\",\n                        children: output ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"whitespace-pre-wrap\",\n                            children: output\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-500\",\n                            children: 'Click \"Run\" to execute your code'\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                lineNumber: 275,\n                columnNumber: 9\n            }, this),\n            expectedOutput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-50 px-4 py-2 border-b border-blue-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium text-blue-700\",\n                            children: \"Expected Output\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 bg-blue-900 text-blue-100 font-mono text-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"whitespace-pre-wrap\",\n                            children: expectedOutput\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                lineNumber: 291,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n        lineNumber: 169,\n        columnNumber: 5\n    }, this);\n}\n_s(CodeEditor, \"3PhLssUSq/FQoAH2og8hj2ua05Q=\");\n_c = CodeEditor;\nvar _c;\n$RefreshReg$(_c, \"CodeEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CodeEditor.tsx\n"));

/***/ })

});