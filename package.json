{"name": "java-learning-platform", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "server": "node server/index.js", "dev:server": "nodemon server/index.js"}, "dependencies": {"next": "14.0.4", "react": "^18", "react-dom": "^18", "typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "tailwindcss": "^3.3.0", "autoprefixer": "^10.0.1", "postcss": "^8", "eslint": "^8", "eslint-config-next": "14.0.4", "express": "^4.18.2", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4", "prisma": "^5.7.1", "@prisma/client": "^5.7.1", "next-auth": "^4.24.5", "bcryptjs": "^2.4.3", "@monaco-editor/react": "^4.6.0", "axios": "^1.6.2", "react-hot-toast": "^2.4.1", "lucide-react": "^0.294.0", "recharts": "^2.8.0", "jspdf": "^2.5.1", "html2canvas": "^1.4.1", "@headlessui/react": "^1.7.17", "framer-motion": "^10.16.16", "react-confetti": "^6.1.0", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.2", "@types/express": "^4.17.21", "@types/bcryptjs": "^2.4.6", "@eslint/eslintrc": "^2.1.4", "@types/cors": "^2.8.17"}}