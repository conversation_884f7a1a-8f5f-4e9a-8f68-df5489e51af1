"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/state-local";
exports.ids = ["vendor-chunks/state-local"];
exports.modules = {

/***/ "(ssr)/./node_modules/state-local/lib/es/state-local.js":
/*!********************************************************!*\
  !*** ./node_modules/state-local/lib/es/state-local.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction ownKeys(object, enumerableOnly) {\n    var keys = Object.keys(object);\n    if (Object.getOwnPropertySymbols) {\n        var symbols = Object.getOwnPropertySymbols(object);\n        if (enumerableOnly) symbols = symbols.filter(function(sym) {\n            return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n        });\n        keys.push.apply(keys, symbols);\n    }\n    return keys;\n}\nfunction _objectSpread2(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = arguments[i] != null ? arguments[i] : {};\n        if (i % 2) {\n            ownKeys(Object(source), true).forEach(function(key) {\n                _defineProperty(target, key, source[key]);\n            });\n        } else if (Object.getOwnPropertyDescriptors) {\n            Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n        } else {\n            ownKeys(Object(source)).forEach(function(key) {\n                Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n            });\n        }\n    }\n    return target;\n}\nfunction compose() {\n    for(var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++){\n        fns[_key] = arguments[_key];\n    }\n    return function(x) {\n        return fns.reduceRight(function(y, f) {\n            return f(y);\n        }, x);\n    };\n}\nfunction curry(fn) {\n    return function curried() {\n        var _this = this;\n        for(var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++){\n            args[_key2] = arguments[_key2];\n        }\n        return args.length >= fn.length ? fn.apply(this, args) : function() {\n            for(var _len3 = arguments.length, nextArgs = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++){\n                nextArgs[_key3] = arguments[_key3];\n            }\n            return curried.apply(_this, [].concat(args, nextArgs));\n        };\n    };\n}\nfunction isObject(value) {\n    return ({}).toString.call(value).includes(\"Object\");\n}\nfunction isEmpty(obj) {\n    return !Object.keys(obj).length;\n}\nfunction isFunction(value) {\n    return typeof value === \"function\";\n}\nfunction hasOwnProperty(object, property) {\n    return Object.prototype.hasOwnProperty.call(object, property);\n}\nfunction validateChanges(initial, changes) {\n    if (!isObject(changes)) errorHandler(\"changeType\");\n    if (Object.keys(changes).some(function(field) {\n        return !hasOwnProperty(initial, field);\n    })) errorHandler(\"changeField\");\n    return changes;\n}\nfunction validateSelector(selector) {\n    if (!isFunction(selector)) errorHandler(\"selectorType\");\n}\nfunction validateHandler(handler) {\n    if (!(isFunction(handler) || isObject(handler))) errorHandler(\"handlerType\");\n    if (isObject(handler) && Object.values(handler).some(function(_handler) {\n        return !isFunction(_handler);\n    })) errorHandler(\"handlersType\");\n}\nfunction validateInitial(initial) {\n    if (!initial) errorHandler(\"initialIsRequired\");\n    if (!isObject(initial)) errorHandler(\"initialType\");\n    if (isEmpty(initial)) errorHandler(\"initialContent\");\n}\nfunction throwError(errorMessages, type) {\n    throw new Error(errorMessages[type] || errorMessages[\"default\"]);\n}\nvar errorMessages = {\n    initialIsRequired: \"initial state is required\",\n    initialType: \"initial state should be an object\",\n    initialContent: \"initial state shouldn't be an empty object\",\n    handlerType: \"handler should be an object or a function\",\n    handlersType: \"all handlers should be a functions\",\n    selectorType: \"selector should be a function\",\n    changeType: \"provided value of changes should be an object\",\n    changeField: 'it seams you want to change a field in the state which is not specified in the \"initial\" state',\n    \"default\": \"an unknown error accured in `state-local` package\"\n};\nvar errorHandler = curry(throwError)(errorMessages);\nvar validators = {\n    changes: validateChanges,\n    selector: validateSelector,\n    handler: validateHandler,\n    initial: validateInitial\n};\nfunction create(initial) {\n    var handler = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    validators.initial(initial);\n    validators.handler(handler);\n    var state = {\n        current: initial\n    };\n    var didUpdate = curry(didStateUpdate)(state, handler);\n    var update = curry(updateState)(state);\n    var validate = curry(validators.changes)(initial);\n    var getChanges = curry(extractChanges)(state);\n    function getState() {\n        var selector = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : function(state) {\n            return state;\n        };\n        validators.selector(selector);\n        return selector(state.current);\n    }\n    function setState(causedChanges) {\n        compose(didUpdate, update, validate, getChanges)(causedChanges);\n    }\n    return [\n        getState,\n        setState\n    ];\n}\nfunction extractChanges(state, causedChanges) {\n    return isFunction(causedChanges) ? causedChanges(state.current) : causedChanges;\n}\nfunction updateState(state, changes) {\n    state.current = _objectSpread2(_objectSpread2({}, state.current), changes);\n    return changes;\n}\nfunction didStateUpdate(state, handler, changes) {\n    isFunction(handler) ? handler(state.current) : Object.keys(changes).forEach(function(field) {\n        var _handler$field;\n        return (_handler$field = handler[field]) === null || _handler$field === void 0 ? void 0 : _handler$field.call(handler, state.current[field]);\n    });\n    return changes;\n}\nvar index = {\n    create: create\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (index);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/state-local/lib/es/state-local.js\n");

/***/ })

};
;