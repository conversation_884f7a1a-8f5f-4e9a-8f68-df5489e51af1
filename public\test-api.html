<!DOCTYPE html>
<html>
<head>
    <title>Simple API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        button { padding: 10px 20px; margin: 10px; background: #007bff; color: white; border: none; cursor: pointer; }
        .result { background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; margin: 10px 0; white-space: pre-wrap; }
    </style>
</head>
<body>
    <h1>🧪 Simple API Test</h1>
    
    <button onclick="testBasicAPI()">Test Basic API</button>
    <button onclick="testExecuteAPI()">Test Execute API</button>
    <button onclick="testExecutePost()">Test Execute POST</button>
    
    <div id="result" class="result">Click a button to test...</div>

    <script>
        async function testBasicAPI() {
            const result = document.getElementById('result');
            result.textContent = 'Testing /api/test...';
            
            try {
                const response = await fetch('/api/test');
                const data = await response.json();
                result.textContent = `✅ SUCCESS!\nStatus: ${response.status}\nData: ${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                result.textContent = `❌ ERROR!\n${error.message}`;
            }
        }
        
        async function testExecuteAPI() {
            const result = document.getElementById('result');
            result.textContent = 'Testing GET /api/execute...';
            
            try {
                const response = await fetch('/api/execute');
                const data = await response.json();
                result.textContent = `✅ SUCCESS!\nStatus: ${response.status}\nData: ${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                result.textContent = `❌ ERROR!\n${error.message}`;
            }
        }
        
        async function testExecutePost() {
            const result = document.getElementById('result');
            result.textContent = 'Testing POST /api/execute...';
            
            try {
                const response = await fetch('/api/execute', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        code: 'public class Test { public static void main(String[] args) { System.out.println("Hello!"); } }',
                        language: 'java'
                    })
                });
                
                const data = await response.json();
                result.textContent = `✅ SUCCESS!\nStatus: ${response.status}\nData: ${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                result.textContent = `❌ ERROR!\n${error.message}`;
            }
        }
    </script>
</body>
</html>
