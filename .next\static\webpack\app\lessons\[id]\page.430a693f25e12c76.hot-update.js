"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/lessons/[id]/page",{

/***/ "(app-pages-browser)/./src/app/lessons/[id]/page.tsx":
/*!***************************************!*\
  !*** ./src/app/lessons/[id]/page.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LessonPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_CodeEditor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/CodeEditor */ \"(app-pages-browser)/./src/components/CodeEditor.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction LessonPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [lesson, setLesson] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"theory\");\n    const [userCode, setUserCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isCompleted, setIsCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showHints, setShowHints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Function to get lesson data based on ID\n    const getLessonData = (id)=>{\n        const lessons = {\n            \"1\": {\n                id: \"1\",\n                title: \"Java Basics: Variables and Data Types\",\n                description: \"Learn about variables, primitive data types, and how to declare and use them in Java.\",\n                difficulty: \"Beginner\",\n                duration: \"30 min\",\n                content: {\n                    theory: \"\\n# Variables and Data Types in Java\\n\\n## What are Variables?\\nVariables are containers that store data values. In Java, every variable has a specific type that determines what kind of data it can hold.\\n\\n## Primitive Data Types\\nJava has 8 primitive data types:\\n\\n### Numeric Types:\\n- **byte**: 8-bit signed integer (-128 to 127)\\n- **short**: 16-bit signed integer (-32,768 to 32,767)\\n- **int**: 32-bit signed integer (-2^31 to 2^31-1)\\n- **long**: 64-bit signed integer (-2^63 to 2^63-1)\\n- **float**: 32-bit floating point\\n- **double**: 64-bit floating point\\n\\n### Other Types:\\n- **char**: 16-bit Unicode character\\n- **boolean**: true or false\\n\\n## Variable Declaration\\nTo declare a variable in Java:\\n```java\\ndataType variableName = value;\\n```\\n\\n## Naming Rules\\n- Must start with a letter, underscore, or dollar sign\\n- Cannot start with a number\\n- Case-sensitive\\n- Cannot use Java keywords\\n- Use camelCase convention\\n          \",\n                    example: 'public class VariableExample {\\n    public static void main(String[] args) {\\n        // Integer variables\\n        int age = 25;\\n        long population = 7800000000L;\\n\\n        // Floating point variables\\n        double price = 19.99;\\n        float temperature = 98.6f;\\n\\n        // Character and boolean\\n        char grade = \\'A\\';\\n        boolean isStudent = true;\\n\\n        // String (reference type)\\n        String name = \"John Doe\";\\n\\n        // Print all variables\\n        System.out.println(\"Name: \" + name);\\n        System.out.println(\"Age: \" + age);\\n        System.out.println(\"Grade: \" + grade);\\n        System.out.println(\"Is Student: \" + isStudent);\\n        System.out.println(\"Price: $\" + price);\\n    }\\n}',\n                    exercise: {\n                        description: \"Create a program that declares variables for a student's information and prints them out.\\n\\n**Requirements:**\\n1. Declare a String variable for the student's name\\n2. Declare an int variable for the student's age\\n3. Declare a double variable for the student's GPA\\n4. Declare a boolean variable for enrollment status\\n5. Print all the information in a formatted way\\n\\n**Expected format:**\\nStudent: [name]\\nAge: [age]\\nGPA: [gpa]\\nEnrolled: [status]\",\n                        starterCode: \"public class StudentInfo {\\n    public static void main(String[] args) {\\n        // TODO: Declare variables for student information\\n\\n\\n        // TODO: Print the student information\\n\\n    }\\n}\",\n                        expectedOutput: \"Student: Alice Johnson\\nAge: 20\\nGPA: 3.8\\nEnrolled: true\",\n                        hints: [\n                            \"Use String for the student's name\",\n                            \"Use int for age, double for GPA, and boolean for enrollment status\",\n                            \"Use System.out.println() to print each line\",\n                            \"You can concatenate strings using the + operator\"\n                        ]\n                    }\n                },\n                nextLessonId: \"2\",\n                prevLessonId: undefined\n            },\n            \"2\": {\n                id: \"2\",\n                title: \"Control Structures: If Statements and Loops\",\n                description: \"Master conditional statements and loops to control program flow.\",\n                difficulty: \"Beginner\",\n                duration: \"45 min\",\n                content: {\n                    theory: \"\\n# Control Structures in Java\\n\\nControl structures allow you to control the flow of execution in your Java programs. They include conditional statements and loops.\\n\\n## If Statements\\nIf statements execute code based on boolean conditions.\\n\\n### Basic If Statement:\\n```java\\nif (condition) {\\n    // code to execute if condition is true\\n}\\n```\\n\\n### If-Else Statement:\\n```java\\nif (condition) {\\n    // code if true\\n} else {\\n    // code if false\\n}\\n```\\n\\n### If-Else If-Else Chain:\\n```java\\nif (condition1) {\\n    // code if condition1 is true\\n} else if (condition2) {\\n    // code if condition2 is true\\n} else {\\n    // code if all conditions are false\\n}\\n```\\n\\n## For Loops\\nFor loops repeat code a specific number of times.\\n\\n### Basic For Loop:\\n```java\\nfor (initialization; condition; increment) {\\n    // code to repeat\\n}\\n```\\n\\n### Enhanced For Loop (for arrays):\\n```java\\nfor (dataType variable : array) {\\n    // code using variable\\n}\\n```\\n\\n## While Loops\\nWhile loops repeat code while a condition is true.\\n\\n### While Loop:\\n```java\\nwhile (condition) {\\n    // code to repeat\\n    // don't forget to update condition!\\n}\\n```\\n\\n### Do-While Loop:\\n```java\\ndo {\\n    // code to repeat at least once\\n} while (condition);\\n```\\n\\n## Switch Cases\\nSwitch statements provide an alternative to multiple if-else statements.\\n\\n```java\\nswitch (variable) {\\n    case value1:\\n        // code for value1\\n        break;\\n    case value2:\\n        // code for value2\\n        break;\\n    default:\\n        // default code\\n        break;\\n}\\n```\\n          \",\n                    example: 'public class ControlStructuresDemo {\\n    public static void main(String[] args) {\\n        // If Statements Example\\n        int score = 85;\\n        System.out.println(\"=== If Statements ===\");\\n\\n        if (score >= 90) {\\n            System.out.println(\"Grade: A - Excellent!\");\\n        } else if (score >= 80) {\\n            System.out.println(\"Grade: B - Good job!\");\\n        } else if (score >= 70) {\\n            System.out.println(\"Grade: C - Average\");\\n        } else {\\n            System.out.println(\"Grade: F - Need improvement\");\\n        }\\n\\n        // For Loop Example\\n        System.out.println(\"\\\\n=== For Loop ===\");\\n        System.out.println(\"Counting from 1 to 5:\");\\n        for (int i = 1; i <= 5; i++) {\\n            System.out.println(\"Count: \" + i);\\n        }\\n\\n        // While Loop Example\\n        System.out.println(\"\\\\n=== While Loop ===\");\\n        System.out.println(\"Countdown:\");\\n        int countdown = 3;\\n        while (countdown > 0) {\\n            System.out.println(countdown + \"...\");\\n            countdown--;\\n        }\\n        System.out.println(\"Blast off!\");\\n\\n        // Switch Case Example\\n        System.out.println(\"\\\\n=== Switch Case ===\");\\n        int dayOfWeek = 3;\\n        switch (dayOfWeek) {\\n            case 1:\\n                System.out.println(\"Monday\");\\n                break;\\n            case 2:\\n                System.out.println(\"Tuesday\");\\n                break;\\n            case 3:\\n                System.out.println(\"Wednesday\");\\n                break;\\n            case 4:\\n                System.out.println(\"Thursday\");\\n                break;\\n            case 5:\\n                System.out.println(\"Friday\");\\n                break;\\n            default:\\n                System.out.println(\"Weekend!\");\\n                break;\\n        }\\n    }\\n}',\n                    exercise: {\n                        description: \"Create a program that demonstrates all four control structure topics: If Statements, For Loops, While Loops, and Switch Cases.\\n\\n**Requirements:**\\n1. **If Statements**: Check if a student's grade (0-100) and print the letter grade (A, B, C, D, F)\\n2. **For Loops**: Print multiplication table for number 5 (5x1 to 5x10)\\n3. **While Loops**: Print numbers from 10 down to 1\\n4. **Switch Cases**: Given a month number (1-12), print the season\\n\\n**Expected output format:**\\nGrade: [letter grade]\\n=== Multiplication Table ===\\n5 x 1 = 5\\n5 x 2 = 10\\n...\\n=== Countdown ===\\n10\\n9\\n...\\n1\\nSeason: [season name]\",\n                        starterCode: 'public class ControlStructuresExercise {\\n    public static void main(String[] args) {\\n        // Test values\\n        int grade = 87;\\n        int tableNumber = 5;\\n        int month = 6;\\n\\n        // TODO: 1. If Statements - Check grade and print letter grade\\n        // A: 90-100, B: 80-89, C: 70-79, D: 60-69, F: below 60\\n\\n\\n        // TODO: 2. For Loop - Print multiplication table for tableNumber\\n        System.out.println(\"=== Multiplication Table ===\");\\n\\n\\n        // TODO: 3. While Loop - Print countdown from 10 to 1\\n        System.out.println(\"=== Countdown ===\");\\n\\n\\n        // TODO: 4. Switch Case - Print season based on month\\n        // Spring: 3,4,5  Summer: 6,7,8  Fall: 9,10,11  Winter: 12,1,2\\n\\n    }\\n}',\n                        expectedOutput: \"Grade: B\\n=== Multiplication Table ===\\n5 x 1 = 5\\n5 x 2 = 10\\n5 x 3 = 15\\n5 x 4 = 20\\n5 x 5 = 25\\n5 x 6 = 30\\n5 x 7 = 35\\n5 x 8 = 40\\n5 x 9 = 45\\n5 x 10 = 50\\n=== Countdown ===\\n10\\n9\\n8\\n7\\n6\\n5\\n4\\n3\\n2\\n1\\nSeason: Summer\",\n                        hints: [\n                            \"For if statements: Use >= for grade ranges (90, 80, 70, 60)\",\n                            \"For multiplication: Use for(int i = 1; i <= 10; i++)\",\n                            \"For countdown: Use while loop with int counter = 10; counter >= 1; counter--\",\n                            \"For switch: Use cases 3,4,5 for Spring; 6,7,8 for Summer, etc.\"\n                        ]\n                    }\n                },\n                nextLessonId: \"3\",\n                prevLessonId: \"1\"\n            },\n            \"3\": {\n                id: \"3\",\n                title: \"Methods and Functions\",\n                description: \"Learn how to create reusable code with methods and understand parameters.\",\n                difficulty: \"Beginner\",\n                duration: \"40 min\",\n                content: {\n                    theory: '\\n# Methods and Functions in Java\\n\\nMethods are blocks of code that perform specific tasks and can be reused throughout your program.\\n\\n## Method Declaration\\nThe basic syntax for declaring a method:\\n\\n```java\\naccessModifier returnType methodName(parameters) {\\n    // method body\\n    return value; // if returnType is not void\\n}\\n```\\n\\n### Components:\\n- **Access Modifier**: public, private, protected\\n- **Return Type**: void, int, String, etc.\\n- **Method Name**: follows camelCase convention\\n- **Parameters**: input values (optional)\\n\\n## Method Declaration Examples\\n\\n### Void Method (no return value):\\n```java\\npublic void printMessage() {\\n    System.out.println(\"Hello from method!\");\\n}\\n```\\n\\n### Method with Return Value:\\n```java\\npublic int addNumbers(int a, int b) {\\n    return a + b;\\n}\\n```\\n\\n## Parameters\\nParameters allow you to pass data into methods.\\n\\n### Single Parameter:\\n```java\\npublic void greetUser(String name) {\\n    System.out.println(\"Hello, \" + name + \"!\");\\n}\\n```\\n\\n### Multiple Parameters:\\n```java\\npublic double calculateArea(double length, double width) {\\n    return length * width;\\n}\\n```\\n\\n## Return Types\\nMethods can return different types of data.\\n\\n### Common Return Types:\\n- **void**: No return value\\n- **int**: Integer numbers\\n- **double**: Decimal numbers\\n- **String**: Text\\n- **boolean**: true/false\\n\\n## Method Overloading\\nYou can have multiple methods with the same name but different parameters.\\n\\n```java\\npublic int add(int a, int b) {\\n    return a + b;\\n}\\n\\npublic double add(double a, double b) {\\n    return a + b;\\n}\\n\\npublic int add(int a, int b, int c) {\\n    return a + b + c;\\n}\\n```\\n          ',\n                    example: 'public class MethodsDemo {\\n\\n    // Method Declaration - void method with no parameters\\n    public static void printWelcome() {\\n        System.out.println(\"Welcome to Java Methods!\");\\n        System.out.println(\"=========================\");\\n    }\\n\\n    // Method with Parameters - single parameter\\n    public static void greetUser(String userName) {\\n        System.out.println(\"Hello, \" + userName + \"! Nice to meet you.\");\\n    }\\n\\n    // Method with Return Type - returns an integer\\n    public static int addTwoNumbers(int num1, int num2) {\\n        int sum = num1 + num2;\\n        return sum;\\n    }\\n\\n    // Method with multiple parameters and return type\\n    public static double calculateRectangleArea(double length, double width) {\\n        return length * width;\\n    }\\n\\n    // Method Overloading - same name, different parameters\\n    public static int multiply(int a, int b) {\\n        return a * b;\\n    }\\n\\n    public static double multiply(double a, double b) {\\n        return a * b;\\n    }\\n\\n    public static int multiply(int a, int b, int c) {\\n        return a * b * c;\\n    }\\n\\n    public static void main(String[] args) {\\n        // Calling methods\\n        printWelcome();\\n\\n        greetUser(\"Alice\");\\n        greetUser(\"Bob\");\\n\\n        int result = addTwoNumbers(15, 25);\\n        System.out.println(\"15 + 25 = \" + result);\\n\\n        double area = calculateRectangleArea(5.5, 3.2);\\n        System.out.println(\"Rectangle area: \" + area);\\n\\n        // Method overloading examples\\n        System.out.println(\"2 * 3 = \" + multiply(2, 3));\\n        System.out.println(\"2.5 * 3.0 = \" + multiply(2.5, 3.0));\\n        System.out.println(\"2 * 3 * 4 = \" + multiply(2, 3, 4));\\n    }\\n}',\n                    exercise: {\n                        description: 'Create a program that demonstrates all four method topics: Method Declaration, Parameters, Return Types, and Method Overloading.\\n\\n**Requirements:**\\n1. **Method Declaration**: Create a void method that prints a welcome message\\n2. **Parameters**: Create a method that takes a name and age, prints a personalized message\\n3. **Return Types**: Create a method that calculates and returns the square of a number\\n4. **Method Overloading**: Create three versions of a \"calculate\" method:\\n   - One that adds two integers\\n   - One that adds two doubles\\n   - One that adds three integers\\n\\n**Expected output format:**\\nWelcome to the Method Exercise!\\nHello John, you are 25 years old.\\nThe square of 7 is: 49\\nSum of 5 and 3: 8\\nSum of 2.5 and 3.7: 6.2\\nSum of 1, 2, and 3: 6',\n                        starterCode: 'public class MethodsExercise {\\n\\n    // TODO: 1. Method Declaration - Create a void method called printWelcome()\\n\\n\\n    // TODO: 2. Parameters - Create a method called printPersonInfo(String name, int age)\\n\\n\\n    // TODO: 3. Return Types - Create a method called calculateSquare(int number) that returns int\\n\\n\\n    // TODO: 4. Method Overloading - Create three calculate methods:\\n    // - calculate(int a, int b) returns int\\n    // - calculate(double a, double b) returns double\\n    // - calculate(int a, int b, int c) returns int\\n\\n\\n\\n\\n    public static void main(String[] args) {\\n        // TODO: Call all your methods here with these values:\\n        // printWelcome()\\n        // printPersonInfo(\"John\", 25)\\n        // calculateSquare(7)\\n        // calculate(5, 3)\\n        // calculate(2.5, 3.7)\\n        // calculate(1, 2, 3)\\n\\n    }\\n}',\n                        expectedOutput: \"Welcome to the Method Exercise!\\nHello John, you are 25 years old.\\nThe square of 7 is: 49\\nSum of 5 and 3: 8\\nSum of 2.5 and 3.7: 6.2\\nSum of 1, 2, and 3: 6\",\n                        hints: [\n                            \"Use 'public static void' for methods that don't return values\",\n                            \"Use 'public static int' for methods that return integers\",\n                            \"Method overloading means same name, different parameter types or counts\",\n                            \"Don't forget to call all methods in main() and print the results\"\n                        ]\n                    }\n                },\n                nextLessonId: \"4\",\n                prevLessonId: \"2\"\n            },\n            \"4\": {\n                id: \"4\",\n                title: \"Object-Oriented Programming: Classes and Objects\",\n                description: \"Introduction to OOP concepts with classes, objects, and encapsulation.\",\n                difficulty: \"Intermediate\",\n                duration: \"60 min\",\n                content: {\n                    theory: '\\n# Object-Oriented Programming in Java\\n\\nObject-Oriented Programming (OOP) is a programming paradigm based on the concept of \"objects\" which contain data and code.\\n\\n## Classes\\nA class is a blueprint or template for creating objects. It defines the properties and behaviors that objects of that type will have.\\n\\n### Class Declaration:\\n```java\\npublic class ClassName {\\n    // fields (attributes)\\n    // constructors\\n    // methods\\n}\\n```\\n\\n### Example Class:\\n```java\\npublic class Car {\\n    // Fields (attributes)\\n    private String brand;\\n    private String model;\\n    private int year;\\n\\n    // Constructor\\n    public Car(String brand, String model, int year) {\\n        this.brand = brand;\\n        this.model = model;\\n        this.year = year;\\n    }\\n\\n    // Methods\\n    public void startEngine() {\\n        System.out.println(\"Engine started!\");\\n    }\\n}\\n```\\n\\n## Objects\\nAn object is an instance of a class. You create objects using the `new` keyword.\\n\\n```java\\nCar myCar = new Car(\"Toyota\", \"Camry\", 2023);\\n```\\n\\n## Constructors\\nConstructors are special methods used to initialize objects when they are created.\\n\\n### Default Constructor:\\n```java\\npublic Car() {\\n    // default values\\n}\\n```\\n\\n### Parameterized Constructor:\\n```java\\npublic Car(String brand, String model, int year) {\\n    this.brand = brand;\\n    this.model = model;\\n    this.year = year;\\n}\\n```\\n\\n## Encapsulation\\nEncapsulation is the practice of keeping fields private and providing public methods to access them.\\n\\n### Private Fields with Public Methods:\\n```java\\nprivate String name;\\n\\npublic String getName() {\\n    return name;\\n}\\n\\npublic void setName(String name) {\\n    this.name = name;\\n}\\n```\\n          ',\n                    example: '// Student class demonstrating OOP concepts\\nclass Student {\\n    // Private fields (Encapsulation)\\n    private String name;\\n    private int age;\\n    private String studentId;\\n    private double gpa;\\n\\n    // Default Constructor\\n    public Student() {\\n        this.name = \"Unknown\";\\n        this.age = 0;\\n        this.studentId = \"000000\";\\n        this.gpa = 0.0;\\n    }\\n\\n    // Parameterized Constructor\\n    public Student(String name, int age, String studentId, double gpa) {\\n        this.name = name;\\n        this.age = age;\\n        this.studentId = studentId;\\n        this.gpa = gpa;\\n    }\\n\\n    // Getter methods (Encapsulation)\\n    public String getName() {\\n        return name;\\n    }\\n\\n    public int getAge() {\\n        return age;\\n    }\\n\\n    public String getStudentId() {\\n        return studentId;\\n    }\\n\\n    public double getGpa() {\\n        return gpa;\\n    }\\n\\n    // Setter methods (Encapsulation)\\n    public void setName(String name) {\\n        this.name = name;\\n    }\\n\\n    public void setAge(int age) {\\n        if (age > 0) {\\n            this.age = age;\\n        }\\n    }\\n\\n    public void setGpa(double gpa) {\\n        if (gpa >= 0.0 && gpa <= 4.0) {\\n            this.gpa = gpa;\\n        }\\n    }\\n\\n    // Method to display student information\\n    public void displayInfo() {\\n        System.out.println(\"Student Information:\");\\n        System.out.println(\"Name: \" + name);\\n        System.out.println(\"Age: \" + age);\\n        System.out.println(\"Student ID: \" + studentId);\\n        System.out.println(\"GPA: \" + gpa);\\n    }\\n\\n    // Method to check if student is on honor roll\\n    public boolean isHonorRoll() {\\n        return gpa >= 3.5;\\n    }\\n}\\n\\npublic class OOPDemo {\\n    public static void main(String[] args) {\\n        // Creating objects using different constructors\\n        Student student1 = new Student();\\n        Student student2 = new Student(\"Alice Johnson\", 20, \"STU001\", 3.8);\\n\\n        System.out.println(\"=== Student 1 (Default Constructor) ===\");\\n        student1.displayInfo();\\n\\n        System.out.println(\"\\\\n=== Student 2 (Parameterized Constructor) ===\");\\n        student2.displayInfo();\\n\\n        // Using setter methods to modify student1\\n        student1.setName(\"Bob Smith\");\\n        student1.setAge(19);\\n        student1.setGpa(3.2);\\n\\n        System.out.println(\"\\\\n=== Student 1 (After modifications) ===\");\\n        student1.displayInfo();\\n\\n        // Using methods\\n        System.out.println(\"\\\\n=== Honor Roll Status ===\");\\n        System.out.println(student1.getName() + \" honor roll: \" + student1.isHonorRoll());\\n        System.out.println(student2.getName() + \" honor roll: \" + student2.isHonorRoll());\\n    }\\n}',\n                    exercise: {\n                        description: \"Create a Book class that demonstrates all four OOP topics: Classes, Objects, Constructors, and Encapsulation.\\n\\n**Requirements:**\\n1. **Classes**: Create a Book class with private fields: title, author, pages, price\\n2. **Objects**: Create two Book objects in main method\\n3. **Constructors**: Implement both default and parameterized constructors\\n4. **Encapsulation**: Create getter and setter methods for all fields, plus a displayInfo() method\\n\\n**Expected output format:**\\n=== Book 1 (Default Constructor) ===\\nTitle: Unknown\\nAuthor: Unknown\\nPages: 0\\nPrice: $0.0\\n\\n=== Book 2 (Parameterized Constructor) ===\\nTitle: Java Programming\\nAuthor: John Doe\\nPages: 500\\nPrice: $49.99\\n\\n=== Book 1 (After modifications) ===\\nTitle: Python Basics\\nAuthor: Jane Smith\\nPages: 300\\nPrice: $29.99\",\n                        starterCode: '// TODO: Create Book class here\\nclass Book {\\n    // TODO: 1. Classes - Add private fields: title, author, pages, price\\n\\n\\n    // TODO: 2. Constructors - Create default constructor\\n\\n\\n    // TODO: 2. Constructors - Create parameterized constructor\\n\\n\\n    // TODO: 3. Encapsulation - Create getter methods\\n\\n\\n\\n\\n\\n    // TODO: 3. Encapsulation - Create setter methods\\n\\n\\n\\n\\n\\n    // TODO: Create displayInfo() method\\n\\n}\\n\\npublic class BookDemo {\\n    public static void main(String[] args) {\\n        // TODO: 4. Objects - Create two Book objects\\n        // book1 using default constructor\\n        // book2 using parameterized constructor with values:\\n        // \"Java Programming\", \"John Doe\", 500, 49.99\\n\\n\\n        // TODO: Display both books\\n\\n\\n        // TODO: Modify book1 using setters:\\n        // title: \"Python Basics\", author: \"Jane Smith\", pages: 300, price: 29.99\\n\\n\\n        // TODO: Display book1 again\\n\\n    }\\n}',\n                        expectedOutput: \"=== Book 1 (Default Constructor) ===\\nTitle: Unknown\\nAuthor: Unknown\\nPages: 0\\nPrice: $0.0\\n\\n=== Book 2 (Parameterized Constructor) ===\\nTitle: Java Programming\\nAuthor: John Doe\\nPages: 500\\nPrice: $49.99\\n\\n=== Book 1 (After modifications) ===\\nTitle: Python Basics\\nAuthor: Jane Smith\\nPages: 300\\nPrice: $29.99\",\n                        hints: [\n                            \"Use private fields and public methods for encapsulation\",\n                            \"Default constructor should set default values like 'Unknown' and 0\",\n                            \"Parameterized constructor should accept all four parameters\",\n                            \"Getter methods return field values, setter methods update them\"\n                        ]\n                    }\n                },\n                nextLessonId: \"5\",\n                prevLessonId: \"3\"\n            },\n            \"5\": {\n                id: \"5\",\n                title: \"Arrays and Collections\",\n                description: \"Work with arrays and Java collections like ArrayList and HashMap.\",\n                difficulty: \"Intermediate\",\n                duration: \"50 min\",\n                content: {\n                    theory: '\\n# Arrays and Collections in Java\\n\\nArrays and Collections are used to store multiple values in Java.\\n\\n## Arrays\\nArrays store multiple values of the same type in a fixed-size sequential collection.\\n\\n### Array Declaration and Initialization:\\n```java\\n// Declaration\\nint[] numbers;\\nString[] names;\\n\\n// Initialization\\nint[] numbers = new int[5];  // Array of 5 integers\\nString[] names = {\"Alice\", \"Bob\", \"Charlie\"};  // Array with values\\n```\\n\\n### Accessing Array Elements:\\n```java\\nint[] numbers = {10, 20, 30, 40, 50};\\nSystem.out.println(numbers[0]);  // Prints 10\\nnumbers[1] = 25;  // Changes second element to 25\\n```\\n\\n### Array Properties:\\n```java\\nint[] numbers = {1, 2, 3, 4, 5};\\nSystem.out.println(numbers.length);  // Prints 5\\n```\\n\\n## ArrayList\\nArrayList is a resizable array implementation that can grow and shrink dynamically.\\n\\n### ArrayList Declaration and Initialization:\\n```java\\nimport java.util.ArrayList;\\n\\nArrayList<String> names = new ArrayList<>();\\nArrayList<Integer> numbers = new ArrayList<>();\\n```\\n\\n### ArrayList Methods:\\n```java\\nArrayList<String> fruits = new ArrayList<>();\\nfruits.add(\"Apple\");        // Add element\\nfruits.add(\"Banana\");\\nfruits.get(0);             // Get element at index 0\\nfruits.set(0, \"Orange\");   // Replace element at index 0\\nfruits.remove(1);          // Remove element at index 1\\nfruits.size();             // Get size\\n```\\n\\n## HashMap\\nHashMap stores key-value pairs and allows fast lookup by key.\\n\\n### HashMap Declaration and Usage:\\n```java\\nimport java.util.HashMap;\\n\\nHashMap<String, Integer> ages = new HashMap<>();\\nages.put(\"Alice\", 25);     // Add key-value pair\\nages.put(\"Bob\", 30);\\nages.get(\"Alice\");         // Get value by key (returns 25)\\nages.remove(\"Bob\");        // Remove key-value pair\\n```\\n\\n## Iteration\\nYou can iterate through arrays and collections using loops.\\n\\n### For-each Loop:\\n```java\\n// Arrays\\nint[] numbers = {1, 2, 3, 4, 5};\\nfor (int num : numbers) {\\n    System.out.println(num);\\n}\\n\\n// ArrayList\\nArrayList<String> names = new ArrayList<>();\\nfor (String name : names) {\\n    System.out.println(name);\\n}\\n```\\n          ',\n                    example: 'import java.util.ArrayList;\\nimport java.util.HashMap;\\n\\npublic class ArraysCollectionsDemo {\\n    public static void main(String[] args) {\\n\\n        // === ARRAYS ===\\n        System.out.println(\"=== Arrays Demo ===\");\\n\\n        // Array declaration and initialization\\n        int[] scores = {85, 92, 78, 96, 88};\\n        String[] subjects = new String[3];\\n        subjects[0] = \"Math\";\\n        subjects[1] = \"Science\";\\n        subjects[2] = \"English\";\\n\\n        // Accessing and modifying arrays\\n        System.out.println(\"First score: \" + scores[0]);\\n        System.out.println(\"Array length: \" + scores.length);\\n        scores[0] = 90;  // Modify first element\\n\\n        // Iteration through array\\n        System.out.println(\"All scores:\");\\n        for (int score : scores) {\\n            System.out.println(\"Score: \" + score);\\n        }\\n\\n        // === ARRAYLIST ===\\n        System.out.println(\"\\\\n=== ArrayList Demo ===\");\\n\\n        ArrayList<String> students = new ArrayList<>();\\n\\n        // Adding elements\\n        students.add(\"Alice\");\\n        students.add(\"Bob\");\\n        students.add(\"Charlie\");\\n        students.add(\"Diana\");\\n\\n        System.out.println(\"Number of students: \" + students.size());\\n        System.out.println(\"First student: \" + students.get(0));\\n\\n        // Modifying ArrayList\\n        students.set(1, \"Robert\");  // Change Bob to Robert\\n        students.remove(\"Charlie\"); // Remove Charlie\\n\\n        // Iteration through ArrayList\\n        System.out.println(\"Current students:\");\\n        for (String student : students) {\\n            System.out.println(\"Student: \" + student);\\n        }\\n\\n        // === HASHMAP ===\\n        System.out.println(\"\\\\n=== HashMap Demo ===\");\\n\\n        HashMap<String, Integer> studentGrades = new HashMap<>();\\n\\n        // Adding key-value pairs\\n        studentGrades.put(\"Alice\", 95);\\n        studentGrades.put(\"Robert\", 87);\\n        studentGrades.put(\"Diana\", 92);\\n\\n        // Accessing values\\n        System.out.println(\"Alice\\'s grade: \" + studentGrades.get(\"Alice\"));\\n        System.out.println(\"Number of grades: \" + studentGrades.size());\\n\\n        // Iteration through HashMap\\n        System.out.println(\"All grades:\");\\n        for (String name : studentGrades.keySet()) {\\n            System.out.println(name + \": \" + studentGrades.get(name));\\n        }\\n\\n        // Check if key exists\\n        if (studentGrades.containsKey(\"Alice\")) {\\n            System.out.println(\"Alice\\'s grade found!\");\\n        }\\n    }\\n}',\n                    exercise: {\n                        description: \"Create a program that demonstrates all four collection topics: Arrays, ArrayList, HashMap, and Iteration.\\n\\n**Requirements:**\\n1. **Arrays**: Create an array of 5 integers, display them, and calculate their sum\\n2. **ArrayList**: Create an ArrayList of fruits, add 4 fruits, remove one, and display the list\\n3. **HashMap**: Create a HashMap of countries and their capitals, add 3 pairs, and display them\\n4. **Iteration**: Use for-each loops to iterate through all collections\\n\\n**Expected output format:**\\n=== Arrays ===\\nNumbers: 10 20 30 40 50\\nSum: 150\\n\\n=== ArrayList ===\\nFruits: [Apple, Banana, Orange]\\n\\n=== HashMap ===\\nCountries and Capitals:\\nUSA: Washington DC\\nFrance: Paris\\nJapan: Tokyo\",\n                        starterCode: 'import java.util.ArrayList;\\nimport java.util.HashMap;\\n\\npublic class CollectionsExercise {\\n    public static void main(String[] args) {\\n\\n        // TODO: 1. Arrays - Create array with values {10, 20, 30, 40, 50}\\n        System.out.println(\"=== Arrays ===\");\\n\\n\\n        // TODO: Display array elements and calculate sum using iteration\\n\\n\\n        // TODO: 2. ArrayList - Create ArrayList of fruits\\n        System.out.println(\"\\\\n=== ArrayList ===\");\\n\\n\\n        // TODO: Add fruits: \"Apple\", \"Banana\", \"Cherry\", \"Orange\"\\n        // TODO: Remove \"Cherry\"\\n        // TODO: Display remaining fruits\\n\\n\\n        // TODO: 3. HashMap - Create HashMap of countries and capitals\\n        System.out.println(\"\\\\n=== HashMap ===\");\\n\\n\\n        // TODO: Add pairs: \"USA\"->\"Washington DC\", \"France\"->\"Paris\", \"Japan\"->\"Tokyo\"\\n        // TODO: Display all pairs using iteration\\n\\n    }\\n}',\n                        expectedOutput: \"=== Arrays ===\\nNumbers: 10 20 30 40 50\\nSum: 150\\n\\n=== ArrayList ===\\nFruits: [Apple, Banana, Orange]\\n\\n=== HashMap ===\\nCountries and Capitals:\\nUSA: Washington DC\\nFrance: Paris\\nJapan: Tokyo\",\n                        hints: [\n                            \"Use for-each loop: for(int num : array) to iterate arrays\",\n                            \"ArrayList methods: add(), remove(), toString() for display\",\n                            \"HashMap methods: put(), keySet() for iteration\",\n                            \"Calculate sum by adding each array element in the loop\"\n                        ]\n                    }\n                },\n                nextLessonId: \"6\",\n                prevLessonId: \"4\"\n            },\n            \"6\": {\n                id: \"6\",\n                title: \"Exception Handling\",\n                description: \"Learn to handle errors gracefully with try-catch blocks and custom exceptions.\",\n                difficulty: \"Advanced\",\n                duration: \"45 min\",\n                content: {\n                    theory: '\\n# Exception Handling in Java\\n\\nException handling allows you to manage runtime errors gracefully and prevent your program from crashing.\\n\\n## Try-Catch Blocks\\nThe basic structure for handling exceptions:\\n\\n```java\\ntry {\\n    // Code that might throw an exception\\n} catch (ExceptionType e) {\\n    // Handle the exception\\n}\\n```\\n\\n### Example:\\n```java\\ntry {\\n    int result = 10 / 0;  // This will throw ArithmeticException\\n} catch (ArithmeticException e) {\\n    System.out.println(\"Cannot divide by zero!\");\\n}\\n```\\n\\n## Finally Block\\nThe finally block always executes, whether an exception occurs or not:\\n\\n```java\\ntry {\\n    // risky code\\n} catch (Exception e) {\\n    // handle exception\\n} finally {\\n    // cleanup code - always runs\\n    System.out.println(\"Cleanup completed\");\\n}\\n```\\n\\n## Custom Exceptions\\nYou can create your own exception classes:\\n\\n```java\\nclass CustomException extends Exception {\\n    public CustomException(String message) {\\n        super(message);\\n    }\\n}\\n```\\n\\n## Throws Keyword\\nUse throws to declare that a method might throw an exception:\\n\\n```java\\npublic void riskyMethod() throws IOException {\\n    // code that might throw IOException\\n}\\n```\\n\\n### Calling methods that throw exceptions:\\n```java\\ntry {\\n    riskyMethod();\\n} catch (IOException e) {\\n    System.out.println(\"IO Error: \" + e.getMessage());\\n}\\n```\\n\\n## Common Exception Types\\n- **ArithmeticException**: Division by zero\\n- **NullPointerException**: Using null reference\\n- **ArrayIndexOutOfBoundsException**: Invalid array index\\n- **NumberFormatException**: Invalid number conversion\\n- **IOException**: Input/output operations\\n- **FileNotFoundException**: File not found\\n\\n## Best Practices\\n1. Catch specific exceptions rather than generic Exception\\n2. Always clean up resources in finally block\\n3. Don\\'t ignore exceptions - at least log them\\n4. Use meaningful error messages\\n          ',\n                    example: '// Custom Exception class\\nclass InvalidAgeException extends Exception {\\n    public InvalidAgeException(String message) {\\n        super(message);\\n    }\\n}\\n\\npublic class ExceptionHandlingDemo {\\n\\n    // Method that throws custom exception\\n    public static void validateAge(int age) throws InvalidAgeException {\\n        if (age < 0 || age > 150) {\\n            throw new InvalidAgeException(\"Age must be between 0 and 150. Got: \" + age);\\n        }\\n        System.out.println(\"Valid age: \" + age);\\n    }\\n\\n    // Method demonstrating different exception types\\n    public static void demonstrateExceptions() {\\n\\n        // Try-Catch with ArithmeticException\\n        System.out.println(\"=== ArithmeticException Demo ===\");\\n        try {\\n            int result = 10 / 0;\\n            System.out.println(\"Result: \" + result);\\n        } catch (ArithmeticException e) {\\n            System.out.println(\"Error: Cannot divide by zero!\");\\n        }\\n\\n        // Try-Catch with ArrayIndexOutOfBoundsException\\n        System.out.println(\"\\\\n=== ArrayIndexOutOfBoundsException Demo ===\");\\n        try {\\n            int[] numbers = {1, 2, 3};\\n            System.out.println(\"Element at index 5: \" + numbers[5]);\\n        } catch (ArrayIndexOutOfBoundsException e) {\\n            System.out.println(\"Error: Array index out of bounds!\");\\n        }\\n\\n        // Try-Catch with NumberFormatException\\n        System.out.println(\"\\\\n=== NumberFormatException Demo ===\");\\n        try {\\n            String text = \"abc\";\\n            int number = Integer.parseInt(text);\\n            System.out.println(\"Number: \" + number);\\n        } catch (NumberFormatException e) {\\n            System.out.println(\"Error: Cannot convert \\'\" + e.getMessage().split(\"\"\")[1] + \"\\' to number!\");\\n        }\\n    }\\n\\n    public static void main(String[] args) {\\n\\n        // Demonstrate common exceptions\\n        demonstrateExceptions();\\n\\n        // Custom Exception with Throws\\n        System.out.println(\"\\\\n=== Custom Exception Demo ===\");\\n\\n        int[] testAges = {25, -5, 200, 30};\\n\\n        for (int age : testAges) {\\n            try {\\n                validateAge(age);\\n            } catch (InvalidAgeException e) {\\n                System.out.println(\"Custom Error: \" + e.getMessage());\\n            }\\n        }\\n\\n        // Finally Block Demo\\n        System.out.println(\"\\\\n=== Finally Block Demo ===\");\\n        try {\\n            System.out.println(\"Executing risky operation...\");\\n            int result = 10 / 2;  // This works fine\\n            System.out.println(\"Result: \" + result);\\n        } catch (Exception e) {\\n            System.out.println(\"Exception caught: \" + e.getMessage());\\n        } finally {\\n            System.out.println(\"Finally block: Cleanup completed!\");\\n        }\\n\\n        System.out.println(\"\\\\nProgram completed successfully!\");\\n    }\\n}',\n                    exercise: {\n                        description: \"Create a program that demonstrates all four exception handling topics: Try-Catch, Finally Block, Custom Exceptions, and Throws.\\n\\n**Requirements:**\\n1. **Try-Catch**: Handle division by zero and array index out of bounds\\n2. **Finally Block**: Use finally to print cleanup message\\n3. **Custom Exceptions**: Create InvalidScoreException for scores outside 0-100 range\\n4. **Throws**: Create a method that throws the custom exception\\n\\n**Expected output format:**\\n=== Try-Catch Demo ===\\nError: Division by zero!\\nError: Array index out of bounds!\\n\\n=== Custom Exception Demo ===\\nValid score: 85\\nError: Score must be between 0 and 100. Got: 150\\n\\n=== Finally Block Demo ===\\nProcessing...\\nFinally: Cleanup completed!\",\n                        starterCode: '// TODO: 1. Custom Exceptions - Create InvalidScoreException class\\n\\n\\npublic class ExceptionExercise {\\n\\n    // TODO: 2. Throws - Create validateScore method that throws InvalidScoreException\\n    // Method should accept int score and throw exception if score < 0 or score > 100\\n\\n\\n    public static void main(String[] args) {\\n\\n        System.out.println(\"=== Try-Catch Demo ===\");\\n\\n        // TODO: 3. Try-Catch - Handle division by zero\\n        try {\\n\\n        } catch () {\\n\\n        }\\n\\n        // TODO: 3. Try-Catch - Handle array index out of bounds\\n        // Create array {1, 2, 3} and try to access index 5\\n        try {\\n\\n        } catch () {\\n\\n        }\\n\\n        System.out.println(\"\\\\n=== Custom Exception Demo ===\");\\n\\n        // TODO: Test validateScore with values 85 and 150\\n\\n\\n        System.out.println(\"\\\\n=== Finally Block Demo ===\");\\n\\n        // TODO: 4. Finally Block - Use try-catch-finally\\n        try {\\n            System.out.println(\"Processing...\");\\n            // Some operation\\n        } catch (Exception e) {\\n            System.out.println(\"Error occurred\");\\n        } finally {\\n            // TODO: Print cleanup message\\n        }\\n    }\\n}',\n                        expectedOutput: \"=== Try-Catch Demo ===\\nError: Division by zero!\\nError: Array index out of bounds!\\n\\n=== Custom Exception Demo ===\\nValid score: 85\\nError: Score must be between 0 and 100. Got: 150\\n\\n=== Finally Block Demo ===\\nProcessing...\\nFinally: Cleanup completed!\",\n                        hints: [\n                            \"Custom exception: class InvalidScoreException extends Exception\",\n                            \"Throws: public static void validateScore(int score) throws InvalidScoreException\",\n                            \"Try-catch: catch (ArithmeticException e) and catch (ArrayIndexOutOfBoundsException e)\",\n                            \"Finally block always executes after try-catch\"\n                        ]\n                    }\n                },\n                nextLessonId: undefined,\n                prevLessonId: \"5\"\n            }\n        };\n        return lessons[id] || lessons[\"1\"] // Default to lesson 1 if not found\n        ;\n    };\n    const mockLesson = getLessonData(params.id);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simulate loading lesson data\n        setLesson(mockLesson);\n        setUserCode(mockLesson.content.exercise.starterCode);\n        // Calculate progress based on current tab\n        const tabProgress = {\n            theory: 33,\n            example: 66,\n            exercise: 100\n        };\n        setProgress(tabProgress[currentTab]);\n    }, [\n        currentTab\n    ]);\n    const handleCodeChange = (code)=>{\n        setUserCode(code);\n    };\n    const handleRunCode = async (code)=>{\n        // Simulate code execution\n        try {\n            // In a real app, this would send the code to a backend service\n            const mockOutput = \"Student: Alice Johnson\\nAge: 20\\nGPA: 3.8\\nEnrolled: true\";\n            // Check if the output matches expected output\n            if (lesson && mockOutput.trim() === lesson.content.exercise.expectedOutput.trim()) {\n                setIsCompleted(true);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(\"Congratulations! Exercise completed successfully!\");\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Output doesn't match expected result. Keep trying!\");\n            }\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Error executing code\");\n        }\n    };\n    const handleNextLesson = ()=>{\n        if (lesson === null || lesson === void 0 ? void 0 : lesson.nextLessonId) {\n            router.push(\"/lessons/\".concat(lesson.nextLessonId));\n        }\n    };\n    const handlePrevLesson = ()=>{\n        if (lesson === null || lesson === void 0 ? void 0 : lesson.prevLessonId) {\n            router.push(\"/lessons/\".concat(lesson.prevLessonId));\n        }\n    };\n    if (!lesson) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                lineNumber: 1457,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n            lineNumber: 1456,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/dashboard\",\n                                        className: \"flex items-center text-gray-600 hover:text-primary-600 transition-colors mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-5 h-5 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1470,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Back to Dashboard\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1469,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-8 h-8 text-primary-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1473,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-xl font-bold text-gray-900\",\n                                        children: \"JavaLearn\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1474,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1468,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Progress: \",\n                                            progress,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1477,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-32 bg-gray-200 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-primary-600 h-2 rounded-full transition-all duration-300\",\n                                            style: {\n                                                width: \"\".concat(progress, \"%\")\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1481,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1480,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1476,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                        lineNumber: 1467,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                    lineNumber: 1466,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                lineNumber: 1465,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        className: \"mb-8\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                            children: lesson.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1501,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: lesson.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1502,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 py-1 rounded-full text-xs \".concat(lesson.difficulty === \"Beginner\" ? \"bg-green-100 text-green-800\" : lesson.difficulty === \"Intermediate\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                                                    children: lesson.difficulty\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1504,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1512,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        lesson.duration\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1511,\n                                                    columnNumber: 17\n                                                }, this),\n                                                isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex items-center text-green-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1517,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Completed\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1516,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1503,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1500,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1499,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"-mb-px flex space-x-8\",\n                                    children: [\n                                        {\n                                            id: \"theory\",\n                                            label: \"Theory\",\n                                            icon: _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                                        },\n                                        {\n                                            id: \"example\",\n                                            label: \"Example\",\n                                            icon: _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                                        },\n                                        {\n                                            id: \"exercise\",\n                                            label: \"Exercise\",\n                                            icon: _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n                                        }\n                                    ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setCurrentTab(tab.id),\n                                            className: \"flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors \".concat(currentTab === tab.id ? \"border-primary-500 text-primary-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1542,\n                                                    columnNumber: 19\n                                                }, this),\n                                                tab.label\n                                            ]\n                                        }, tab.id, true, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1533,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1527,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1526,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                        lineNumber: 1493,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            duration: 0.4\n                        },\n                        children: [\n                            currentTab === \"theory\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card p-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"prose max-w-none\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        dangerouslySetInnerHTML: {\n                                            __html: lesson.content.theory.replace(/\\n/g, \"<br>\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1560,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1559,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1558,\n                                columnNumber: 13\n                            }, this),\n                            currentTab === \"example\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                            children: \"Example Code\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1568,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CodeEditor__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            initialCode: lesson.content.example,\n                                            readOnly: true,\n                                            height: \"500px\",\n                                            showOutput: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1569,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1567,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1566,\n                                columnNumber: 13\n                            }, this),\n                            currentTab === \"exercise\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900\",\n                                                        children: \"Practice Exercise\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1583,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowHints(!showHints),\n                                                        className: \"btn-secondary text-sm\",\n                                                        children: showHints ? \"Hide Hints\" : \"Show Hints\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1584,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1582,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"prose max-w-none mb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    dangerouslySetInnerHTML: {\n                                                        __html: lesson.content.exercise.description.replace(/\\n/g, \"<br>\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1592,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1591,\n                                                columnNumber: 17\n                                            }, this),\n                                            showHints && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-yellow-800 mb-2\",\n                                                        children: \"Hints:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1597,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"text-yellow-700 text-sm space-y-1\",\n                                                        children: lesson.content.exercise.hints.map((hint, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    \"• \",\n                                                                    hint\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 1600,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1598,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1596,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1581,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CodeEditor__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        initialCode: lesson.content.exercise.starterCode,\n                                        onCodeChange: handleCodeChange,\n                                        onRun: handleRunCode,\n                                        height: \"400px\",\n                                        showOutput: true,\n                                        expectedOutput: lesson.content.exercise.expectedOutput\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1607,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1580,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, currentTab, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                        lineNumber: 1551,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mt-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handlePrevLesson,\n                                disabled: !lesson.prevLessonId,\n                                className: \"flex items-center btn-secondary disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-4 h-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1626,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Previous Lesson\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1621,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4\",\n                                children: [\n                                    currentTab !== \"exercise\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            const nextTab = currentTab === \"theory\" ? \"example\" : \"exercise\";\n                                            setCurrentTab(nextTab);\n                                        },\n                                        className: \"btn-primary\",\n                                        children: [\n                                            \"Continue\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 ml-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1640,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1632,\n                                        columnNumber: 15\n                                    }, this),\n                                    currentTab === \"exercise\" && isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNextLesson,\n                                        disabled: !lesson.nextLessonId,\n                                        className: \"flex items-center btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: [\n                                            \"Next Lesson\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 ml-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1651,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1645,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1630,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                        lineNumber: 1620,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                lineNumber: 1491,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n        lineNumber: 1463,\n        columnNumber: 5\n    }, this);\n}\n_s(LessonPage, \"dBUNDWH67aICokwnd2zYUw3Quos=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = LessonPage;\nvar _c;\n$RefreshReg$(_c, \"LessonPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvbGVzc29ucy9baWRdL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFMkM7QUFDVztBQUNoQjtBQVlqQjtBQUNPO0FBQ29CO0FBQ2I7QUFzQnBCLFNBQVNpQjs7SUFDdEIsTUFBTUMsU0FBU2hCLDBEQUFTQTtJQUN4QixNQUFNaUIsU0FBU2hCLDBEQUFTQTtJQUN4QixNQUFNLENBQUNpQixRQUFRQyxVQUFVLEdBQUdyQiwrQ0FBUUEsQ0FBZ0I7SUFDcEQsTUFBTSxDQUFDc0IsWUFBWUMsY0FBYyxHQUFHdkIsK0NBQVFBLENBQW9DO0lBQ2hGLE1BQU0sQ0FBQ3dCLFVBQVVDLFlBQVksR0FBR3pCLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQzBCLGFBQWFDLGVBQWUsR0FBRzNCLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQzRCLFdBQVdDLGFBQWEsR0FBRzdCLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQzhCLFVBQVVDLFlBQVksR0FBRy9CLCtDQUFRQSxDQUFDO0lBRXpDLDBDQUEwQztJQUMxQyxNQUFNZ0MsZ0JBQWdCLENBQUNDO1FBQ3JCLE1BQU1DLFVBQXFDO1lBQ3pDLEtBQUs7Z0JBQ0hELElBQUk7Z0JBQ0pFLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JDLFlBQVk7Z0JBQ1pDLFVBQVU7Z0JBQ1ZDLFNBQVM7b0JBQ1BDLFFBQVM7b0JBa0NUQyxTQUFVO29CQXlCVkMsVUFBVTt3QkFDUk4sYUFBYzt3QkFjZE8sYUFBYzt3QkFTZEMsZ0JBQWlCO3dCQUlqQkMsT0FBTzs0QkFDTDs0QkFDQTs0QkFDQTs0QkFDQTt5QkFDRDtvQkFDSDtnQkFDRjtnQkFDQUMsY0FBYztnQkFDZEMsY0FBY0M7WUFDaEI7WUFDQSxLQUFLO2dCQUNIZixJQUFJO2dCQUNKRSxPQUFPO2dCQUNQQyxhQUFhO2dCQUNiQyxZQUFZO2dCQUNaQyxVQUFVO2dCQUNWQyxTQUFTO29CQUNQQyxRQUFTO29CQXVGVEMsU0FBVTtvQkEwRFZDLFVBQVU7d0JBQ1JOLGFBQWM7d0JBb0JkTyxhQUFjO3dCQXdCZEMsZ0JBQWlCO3dCQXdCakJDLE9BQU87NEJBQ0w7NEJBQ0E7NEJBQ0E7NEJBQ0E7eUJBQ0Q7b0JBQ0g7Z0JBQ0Y7Z0JBQ0FDLGNBQWM7Z0JBQ2RDLGNBQWM7WUFDaEI7WUFDQSxLQUFLO2dCQUNIZCxJQUFJO2dCQUNKRSxPQUFPO2dCQUNQQyxhQUFhO2dCQUNiQyxZQUFZO2dCQUNaQyxVQUFVO2dCQUNWQyxTQUFTO29CQUNQQyxRQUFTO29CQWlGVEMsU0FBVTtvQkF3RFZDLFVBQVU7d0JBQ1JOLGFBQWM7d0JBa0JkTyxhQUFjO3dCQThCZEMsZ0JBQWlCO3dCQU1qQkMsT0FBTzs0QkFDTDs0QkFDQTs0QkFDQTs0QkFDQTt5QkFDRDtvQkFDSDtnQkFDRjtnQkFDQUMsY0FBYztnQkFDZEMsY0FBYztZQUNoQjtZQUNBLEtBQUs7Z0JBQ0hkLElBQUk7Z0JBQ0pFLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JDLFlBQVk7Z0JBQ1pDLFVBQVU7Z0JBQ1ZDLFNBQVM7b0JBQ1BDLFFBQVM7b0JBaUZUQyxTQUFVO29CQW1HVkMsVUFBVTt3QkFDUk4sYUFBYzt3QkEwQmRPLGFBQWM7d0JBOENkQyxnQkFBaUI7d0JBaUJqQkMsT0FBTzs0QkFDTDs0QkFDQTs0QkFDQTs0QkFDQTt5QkFDRDtvQkFDSDtnQkFDRjtnQkFDQUMsY0FBYztnQkFDZEMsY0FBYztZQUNoQjtZQUNBLEtBQUs7Z0JBQ0hkLElBQUk7Z0JBQ0pFLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JDLFlBQVk7Z0JBQ1pDLFVBQVU7Z0JBQ1ZDLFNBQVM7b0JBQ1BDLFFBQVM7b0JBc0ZUQyxTQUFVO29CQTZFVkMsVUFBVTt3QkFDUk4sYUFBYzt3QkFxQmRPLGFBQWM7d0JBK0JkQyxnQkFBaUI7d0JBWWpCQyxPQUFPOzRCQUNMOzRCQUNBOzRCQUNBOzRCQUNBO3lCQUNEO29CQUNIO2dCQUNGO2dCQUNBQyxjQUFjO2dCQUNkQyxjQUFjO1lBQ2hCO1lBQ0EsS0FBSztnQkFDSGQsSUFBSTtnQkFDSkUsT0FBTztnQkFDUEMsYUFBYTtnQkFDYkMsWUFBWTtnQkFDWkMsVUFBVTtnQkFDVkMsU0FBUztvQkFDUEMsUUFBUztvQkFrRlRDLFNBQVU7b0JBa0ZWQyxVQUFVO3dCQUNSTixhQUFjO3dCQW9CZE8sYUFBYzt3QkE4Q2RDLGdCQUFpQjt3QkFXakJDLE9BQU87NEJBQ0w7NEJBQ0E7NEJBQ0E7NEJBQ0E7eUJBQ0Q7b0JBQ0g7Z0JBQ0Y7Z0JBQ0FDLGNBQWNFO2dCQUNkRCxjQUFjO1lBQ2hCO1FBQ0Y7UUFFQSxPQUFPYixPQUFPLENBQUNELEdBQUcsSUFBSUMsT0FBTyxDQUFDLElBQUksQ0FBQyxtQ0FBbUM7O0lBQ3hFO0lBRUEsTUFBTWUsYUFBYWpCLGNBQWNkLE9BQU9lLEVBQUU7SUFFMUNoQyxnREFBU0EsQ0FBQztRQUNSLCtCQUErQjtRQUMvQm9CLFVBQVU0QjtRQUNWeEIsWUFBWXdCLFdBQVdWLE9BQU8sQ0FBQ0csUUFBUSxDQUFDQyxXQUFXO1FBRW5ELDBDQUEwQztRQUMxQyxNQUFNTyxjQUFjO1lBQ2xCVixRQUFRO1lBQ1JDLFNBQVM7WUFDVEMsVUFBVTtRQUNaO1FBQ0FYLFlBQVltQixXQUFXLENBQUM1QixXQUFXO0lBQ3JDLEdBQUc7UUFBQ0E7S0FBVztJQUVmLE1BQU02QixtQkFBbUIsQ0FBQ0M7UUFDeEIzQixZQUFZMkI7SUFDZDtJQUVBLE1BQU1DLGdCQUFnQixPQUFPRDtRQUMzQiwwQkFBMEI7UUFDMUIsSUFBSTtZQUNGLCtEQUErRDtZQUMvRCxNQUFNRSxhQUFjO1lBS3BCLDhDQUE4QztZQUM5QyxJQUFJbEMsVUFBVWtDLFdBQVdDLElBQUksT0FBT25DLE9BQU9tQixPQUFPLENBQUNHLFFBQVEsQ0FBQ0UsY0FBYyxDQUFDVyxJQUFJLElBQUk7Z0JBQ2pGNUIsZUFBZTtnQkFDZlgsdURBQUtBLENBQUN3QyxPQUFPLENBQUM7WUFDaEIsT0FBTztnQkFDTHhDLHVEQUFLQSxDQUFDeUMsS0FBSyxDQUFDO1lBQ2Q7UUFDRixFQUFFLE9BQU9BLE9BQU87WUFDZHpDLHVEQUFLQSxDQUFDeUMsS0FBSyxDQUFDO1FBQ2Q7SUFDRjtJQUVBLE1BQU1DLG1CQUFtQjtRQUN2QixJQUFJdEMsbUJBQUFBLDZCQUFBQSxPQUFRMEIsWUFBWSxFQUFFO1lBQ3hCM0IsT0FBT3dDLElBQUksQ0FBQyxZQUFnQyxPQUFwQnZDLE9BQU8wQixZQUFZO1FBQzdDO0lBQ0Y7SUFFQSxNQUFNYyxtQkFBbUI7UUFDdkIsSUFBSXhDLG1CQUFBQSw2QkFBQUEsT0FBUTJCLFlBQVksRUFBRTtZQUN4QjVCLE9BQU93QyxJQUFJLENBQUMsWUFBZ0MsT0FBcEJ2QyxPQUFPMkIsWUFBWTtRQUM3QztJQUNGO0lBRUEsSUFBSSxDQUFDM0IsUUFBUTtRQUNYLHFCQUNFLDhEQUFDeUM7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7Ozs7Ozs7Ozs7O0lBR3JCO0lBRUEscUJBQ0UsOERBQUNEO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDQztnQkFBSUQsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDaEQsa0RBQUlBO3dDQUFDa0QsTUFBSzt3Q0FBYUYsV0FBVTs7MERBQ2hDLDhEQUFDakQsMEpBQVNBO2dEQUFDaUQsV0FBVTs7Ozs7OzRDQUFpQjs7Ozs7OztrREFHeEMsOERBQUNsRCwwSkFBSUE7d0NBQUNrRCxXQUFVOzs7Ozs7a0RBQ2hCLDhEQUFDRzt3Q0FBS0gsV0FBVTtrREFBdUM7Ozs7Ozs7Ozs7OzswQ0FFekQsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzRDQUF3Qjs0Q0FDMUJoQzs0Q0FBUzs7Ozs7OztrREFFdEIsOERBQUMrQjt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ0Q7NENBQ0NDLFdBQVU7NENBQ1ZJLE9BQU87Z0RBQUVDLE9BQU8sR0FBWSxPQUFUckMsVUFBUzs0Q0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVEzQyw4REFBQytCO2dCQUFJQyxXQUFVOztrQ0FFYiw4REFBQzFELGlEQUFNQSxDQUFDeUQsR0FBRzt3QkFDVEMsV0FBVTt3QkFDVk0sU0FBUzs0QkFBRUMsU0FBUzs0QkFBR0MsR0FBRzt3QkFBRzt3QkFDN0JDLFNBQVM7NEJBQUVGLFNBQVM7NEJBQUdDLEdBQUc7d0JBQUU7d0JBQzVCRSxZQUFZOzRCQUFFbEMsVUFBVTt3QkFBSTs7MENBRTVCLDhEQUFDdUI7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNEOztzREFDQyw4REFBQ1k7NENBQUdYLFdBQVU7c0RBQXlDMUMsT0FBT2UsS0FBSzs7Ozs7O3NEQUNuRSw4REFBQ3VDOzRDQUFFWixXQUFVO3NEQUFzQjFDLE9BQU9nQixXQUFXOzs7Ozs7c0RBQ3JELDhEQUFDeUI7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRztvREFBS0gsV0FBVyxrQ0FJaEIsT0FIQzFDLE9BQU9pQixVQUFVLEtBQUssYUFBYSxnQ0FDbkNqQixPQUFPaUIsVUFBVSxLQUFLLGlCQUFpQixrQ0FDdkM7OERBRUNqQixPQUFPaUIsVUFBVTs7Ozs7OzhEQUVwQiw4REFBQzRCO29EQUFLSCxXQUFVOztzRUFDZCw4REFBQ3BELDBKQUFLQTs0REFBQ29ELFdBQVU7Ozs7Ozt3REFDaEIxQyxPQUFPa0IsUUFBUTs7Ozs7OztnREFFakJaLDZCQUNDLDhEQUFDdUM7b0RBQUtILFdBQVU7O3NFQUNkLDhEQUFDdEQsMkpBQVdBOzREQUFDc0QsV0FBVTs7Ozs7O3dEQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQVNsRCw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNDO29DQUFJRCxXQUFVOzhDQUNaO3dDQUNDOzRDQUFFN0IsSUFBSTs0Q0FBVTBDLE9BQU87NENBQVVDLE1BQU1yRSwySkFBUUE7d0NBQUM7d0NBQ2hEOzRDQUFFMEIsSUFBSTs0Q0FBVzBDLE9BQU87NENBQVdDLE1BQU1qRSwySkFBSUE7d0NBQUM7d0NBQzlDOzRDQUFFc0IsSUFBSTs0Q0FBWTBDLE9BQU87NENBQVlDLE1BQU1uRSwySkFBTUE7d0NBQUM7cUNBQ25ELENBQUNvRSxHQUFHLENBQUMsQ0FBQ0Msb0JBQ0wsOERBQUNDOzRDQUVDQyxTQUFTLElBQU16RCxjQUFjdUQsSUFBSTdDLEVBQUU7NENBQ25DNkIsV0FBVyxnRkFJVixPQUhDeEMsZUFBZXdELElBQUk3QyxFQUFFLEdBQ2pCLHdDQUNBOzs4REFHTiw4REFBQzZDLElBQUlGLElBQUk7b0RBQUNkLFdBQVU7Ozs7OztnREFDbkJnQixJQUFJSCxLQUFLOzsyQ0FUTEcsSUFBSTdDLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FpQnJCLDhEQUFDN0IsaURBQU1BLENBQUN5RCxHQUFHO3dCQUVUTyxTQUFTOzRCQUFFQyxTQUFTOzRCQUFHWSxHQUFHO3dCQUFHO3dCQUM3QlYsU0FBUzs0QkFBRUYsU0FBUzs0QkFBR1ksR0FBRzt3QkFBRTt3QkFDNUJULFlBQVk7NEJBQUVsQyxVQUFVO3dCQUFJOzs0QkFFM0JoQixlQUFlLDBCQUNkLDhEQUFDdUM7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSXFCLHlCQUF5Qjs0Q0FBRUMsUUFBUS9ELE9BQU9tQixPQUFPLENBQUNDLE1BQU0sQ0FBQzRDLE9BQU8sQ0FBQyxPQUFPO3dDQUFROzs7Ozs7Ozs7Ozs7Ozs7OzRCQUsxRjlELGVBQWUsMkJBQ2QsOERBQUN1QztnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDdUI7NENBQUd2QixXQUFVO3NEQUEyQzs7Ozs7O3NEQUN6RCw4REFBQy9DLDhEQUFVQTs0Q0FDVHVFLGFBQWFsRSxPQUFPbUIsT0FBTyxDQUFDRSxPQUFPOzRDQUNuQzhDLFVBQVU7NENBQ1ZDLFFBQU87NENBQ1BDLFlBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7OzRCQU1uQm5FLGVBQWUsNEJBQ2QsOERBQUN1QztnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDdUI7d0RBQUd2QixXQUFVO2tFQUFzQzs7Ozs7O2tFQUNwRCw4REFBQ2lCO3dEQUNDQyxTQUFTLElBQU1uRCxhQUFhLENBQUNEO3dEQUM3QmtDLFdBQVU7a0VBRVRsQyxZQUFZLGVBQWU7Ozs7Ozs7Ozs7OzswREFHaEMsOERBQUNpQztnREFBSUMsV0FBVTswREFDYiw0RUFBQ0Q7b0RBQUlxQix5QkFBeUI7d0RBQUVDLFFBQVEvRCxPQUFPbUIsT0FBTyxDQUFDRyxRQUFRLENBQUNOLFdBQVcsQ0FBQ2dELE9BQU8sQ0FBQyxPQUFPO29EQUFROzs7Ozs7Ozs7Ozs0Q0FHcEd4RCwyQkFDQyw4REFBQ2lDO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQzRCO3dEQUFHNUIsV0FBVTtrRUFBbUM7Ozs7OztrRUFDakQsOERBQUM2Qjt3REFBRzdCLFdBQVU7a0VBQ1gxQyxPQUFPbUIsT0FBTyxDQUFDRyxRQUFRLENBQUNHLEtBQUssQ0FBQ2dDLEdBQUcsQ0FBQyxDQUFDZSxNQUFNQyxzQkFDeEMsOERBQUNDOztvRUFBZTtvRUFBR0Y7OytEQUFWQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFPbkIsOERBQUM5RSw4REFBVUE7d0NBQ1R1RSxhQUFhbEUsT0FBT21CLE9BQU8sQ0FBQ0csUUFBUSxDQUFDQyxXQUFXO3dDQUNoRG9ELGNBQWM1Qzt3Q0FDZDZDLE9BQU8zQzt3Q0FDUG1DLFFBQU87d0NBQ1BDLFlBQVk7d0NBQ1o3QyxnQkFBZ0J4QixPQUFPbUIsT0FBTyxDQUFDRyxRQUFRLENBQUNFLGNBQWM7Ozs7Ozs7Ozs7Ozs7dUJBN0R2RHRCOzs7OztrQ0FvRVAsOERBQUN1Qzt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNpQjtnQ0FDQ0MsU0FBU3BCO2dDQUNUcUMsVUFBVSxDQUFDN0UsT0FBTzJCLFlBQVk7Z0NBQzlCZSxXQUFVOztrREFFViw4REFBQ3pELDJKQUFXQTt3Q0FBQ3lELFdBQVU7Ozs7OztvQ0FBaUI7Ozs7Ozs7MENBSTFDLDhEQUFDRDtnQ0FBSUMsV0FBVTs7b0NBQ1p4QyxlQUFlLDRCQUNkLDhEQUFDeUQ7d0NBQ0NDLFNBQVM7NENBQ1AsTUFBTWtCLFVBQVU1RSxlQUFlLFdBQVcsWUFBWTs0Q0FDdERDLGNBQWMyRTt3Q0FDaEI7d0NBQ0FwQyxXQUFVOzs0Q0FDWDswREFFQyw4REFBQ3hELDJKQUFZQTtnREFBQ3dELFdBQVU7Ozs7Ozs7Ozs7OztvQ0FJM0J4QyxlQUFlLGNBQWNJLDZCQUM1Qiw4REFBQ3FEO3dDQUNDQyxTQUFTdEI7d0NBQ1R1QyxVQUFVLENBQUM3RSxPQUFPMEIsWUFBWTt3Q0FDOUJnQixXQUFVOzs0Q0FDWDswREFFQyw4REFBQ3hELDJKQUFZQTtnREFBQ3dELFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVF4QztHQWpsRHdCN0M7O1FBQ1BmLHNEQUFTQTtRQUNUQyxzREFBU0E7OztLQUZGYyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL2xlc3NvbnMvW2lkXS9wYWdlLnRzeD9hZWM5Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyB1c2VQYXJhbXMsIHVzZVJvdXRlciB9IGZyb20gJ25leHQvbmF2aWdhdGlvbidcbmltcG9ydCB7IG1vdGlvbiB9IGZyb20gJ2ZyYW1lci1tb3Rpb24nXG5pbXBvcnQgeyBcbiAgQ2hldnJvbkxlZnQsIFxuICBDaGV2cm9uUmlnaHQsIFxuICBCb29rT3BlbiwgXG4gIENoZWNrQ2lyY2xlLCBcbiAgUGxheSwgXG4gIFRhcmdldCxcbiAgQ2xvY2ssXG4gIFN0YXIsXG4gIENvZGUsXG4gIEFycm93TGVmdFxufSBmcm9tICdsdWNpZGUtcmVhY3QnXG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnXG5pbXBvcnQgQ29kZUVkaXRvciBmcm9tICdAL2NvbXBvbmVudHMvQ29kZUVkaXRvcidcbmltcG9ydCB0b2FzdCBmcm9tICdyZWFjdC1ob3QtdG9hc3QnXG5cbmludGVyZmFjZSBMZXNzb24ge1xuICBpZDogc3RyaW5nXG4gIHRpdGxlOiBzdHJpbmdcbiAgZGVzY3JpcHRpb246IHN0cmluZ1xuICBkaWZmaWN1bHR5OiAnQmVnaW5uZXInIHwgJ0ludGVybWVkaWF0ZScgfCAnQWR2YW5jZWQnXG4gIGR1cmF0aW9uOiBzdHJpbmdcbiAgY29udGVudDoge1xuICAgIHRoZW9yeTogc3RyaW5nXG4gICAgZXhhbXBsZTogc3RyaW5nXG4gICAgZXhlcmNpc2U6IHtcbiAgICAgIGRlc2NyaXB0aW9uOiBzdHJpbmdcbiAgICAgIHN0YXJ0ZXJDb2RlOiBzdHJpbmdcbiAgICAgIGV4cGVjdGVkT3V0cHV0OiBzdHJpbmdcbiAgICAgIGhpbnRzOiBzdHJpbmdbXVxuICAgIH1cbiAgfVxuICBuZXh0TGVzc29uSWQ/OiBzdHJpbmdcbiAgcHJldkxlc3NvbklkPzogc3RyaW5nXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExlc3NvblBhZ2UoKSB7XG4gIGNvbnN0IHBhcmFtcyA9IHVzZVBhcmFtcygpXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpXG4gIGNvbnN0IFtsZXNzb24sIHNldExlc3Nvbl0gPSB1c2VTdGF0ZTxMZXNzb24gfCBudWxsPihudWxsKVxuICBjb25zdCBbY3VycmVudFRhYiwgc2V0Q3VycmVudFRhYl0gPSB1c2VTdGF0ZTwndGhlb3J5JyB8ICdleGFtcGxlJyB8ICdleGVyY2lzZSc+KCd0aGVvcnknKVxuICBjb25zdCBbdXNlckNvZGUsIHNldFVzZXJDb2RlXSA9IHVzZVN0YXRlKCcnKVxuICBjb25zdCBbaXNDb21wbGV0ZWQsIHNldElzQ29tcGxldGVkXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbc2hvd0hpbnRzLCBzZXRTaG93SGludHNdID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtwcm9ncmVzcywgc2V0UHJvZ3Jlc3NdID0gdXNlU3RhdGUoMClcblxuICAvLyBGdW5jdGlvbiB0byBnZXQgbGVzc29uIGRhdGEgYmFzZWQgb24gSURcbiAgY29uc3QgZ2V0TGVzc29uRGF0YSA9IChpZDogc3RyaW5nKTogTGVzc29uID0+IHtcbiAgICBjb25zdCBsZXNzb25zOiB7IFtrZXk6IHN0cmluZ106IExlc3NvbiB9ID0ge1xuICAgICAgJzEnOiB7XG4gICAgICAgIGlkOiAnMScsXG4gICAgICAgIHRpdGxlOiAnSmF2YSBCYXNpY3M6IFZhcmlhYmxlcyBhbmQgRGF0YSBUeXBlcycsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnTGVhcm4gYWJvdXQgdmFyaWFibGVzLCBwcmltaXRpdmUgZGF0YSB0eXBlcywgYW5kIGhvdyB0byBkZWNsYXJlIGFuZCB1c2UgdGhlbSBpbiBKYXZhLicsXG4gICAgICAgIGRpZmZpY3VsdHk6ICdCZWdpbm5lcicsXG4gICAgICAgIGR1cmF0aW9uOiAnMzAgbWluJyxcbiAgICAgICAgY29udGVudDoge1xuICAgICAgICAgIHRoZW9yeTogYFxuIyBWYXJpYWJsZXMgYW5kIERhdGEgVHlwZXMgaW4gSmF2YVxuXG4jIyBXaGF0IGFyZSBWYXJpYWJsZXM/XG5WYXJpYWJsZXMgYXJlIGNvbnRhaW5lcnMgdGhhdCBzdG9yZSBkYXRhIHZhbHVlcy4gSW4gSmF2YSwgZXZlcnkgdmFyaWFibGUgaGFzIGEgc3BlY2lmaWMgdHlwZSB0aGF0IGRldGVybWluZXMgd2hhdCBraW5kIG9mIGRhdGEgaXQgY2FuIGhvbGQuXG5cbiMjIFByaW1pdGl2ZSBEYXRhIFR5cGVzXG5KYXZhIGhhcyA4IHByaW1pdGl2ZSBkYXRhIHR5cGVzOlxuXG4jIyMgTnVtZXJpYyBUeXBlczpcbi0gKipieXRlKio6IDgtYml0IHNpZ25lZCBpbnRlZ2VyICgtMTI4IHRvIDEyNylcbi0gKipzaG9ydCoqOiAxNi1iaXQgc2lnbmVkIGludGVnZXIgKC0zMiw3NjggdG8gMzIsNzY3KVxuLSAqKmludCoqOiAzMi1iaXQgc2lnbmVkIGludGVnZXIgKC0yXjMxIHRvIDJeMzEtMSlcbi0gKipsb25nKio6IDY0LWJpdCBzaWduZWQgaW50ZWdlciAoLTJeNjMgdG8gMl42My0xKVxuLSAqKmZsb2F0Kio6IDMyLWJpdCBmbG9hdGluZyBwb2ludFxuLSAqKmRvdWJsZSoqOiA2NC1iaXQgZmxvYXRpbmcgcG9pbnRcblxuIyMjIE90aGVyIFR5cGVzOlxuLSAqKmNoYXIqKjogMTYtYml0IFVuaWNvZGUgY2hhcmFjdGVyXG4tICoqYm9vbGVhbioqOiB0cnVlIG9yIGZhbHNlXG5cbiMjIFZhcmlhYmxlIERlY2xhcmF0aW9uXG5UbyBkZWNsYXJlIGEgdmFyaWFibGUgaW4gSmF2YTpcblxcYFxcYFxcYGphdmFcbmRhdGFUeXBlIHZhcmlhYmxlTmFtZSA9IHZhbHVlO1xuXFxgXFxgXFxgXG5cbiMjIE5hbWluZyBSdWxlc1xuLSBNdXN0IHN0YXJ0IHdpdGggYSBsZXR0ZXIsIHVuZGVyc2NvcmUsIG9yIGRvbGxhciBzaWduXG4tIENhbm5vdCBzdGFydCB3aXRoIGEgbnVtYmVyXG4tIENhc2Utc2Vuc2l0aXZlXG4tIENhbm5vdCB1c2UgSmF2YSBrZXl3b3Jkc1xuLSBVc2UgY2FtZWxDYXNlIGNvbnZlbnRpb25cbiAgICAgICAgICBgLFxuICAgICAgICAgIGV4YW1wbGU6IGBwdWJsaWMgY2xhc3MgVmFyaWFibGVFeGFtcGxlIHtcbiAgICBwdWJsaWMgc3RhdGljIHZvaWQgbWFpbihTdHJpbmdbXSBhcmdzKSB7XG4gICAgICAgIC8vIEludGVnZXIgdmFyaWFibGVzXG4gICAgICAgIGludCBhZ2UgPSAyNTtcbiAgICAgICAgbG9uZyBwb3B1bGF0aW9uID0gNzgwMDAwMDAwMEw7XG5cbiAgICAgICAgLy8gRmxvYXRpbmcgcG9pbnQgdmFyaWFibGVzXG4gICAgICAgIGRvdWJsZSBwcmljZSA9IDE5Ljk5O1xuICAgICAgICBmbG9hdCB0ZW1wZXJhdHVyZSA9IDk4LjZmO1xuXG4gICAgICAgIC8vIENoYXJhY3RlciBhbmQgYm9vbGVhblxuICAgICAgICBjaGFyIGdyYWRlID0gJ0EnO1xuICAgICAgICBib29sZWFuIGlzU3R1ZGVudCA9IHRydWU7XG5cbiAgICAgICAgLy8gU3RyaW5nIChyZWZlcmVuY2UgdHlwZSlcbiAgICAgICAgU3RyaW5nIG5hbWUgPSBcIkpvaG4gRG9lXCI7XG5cbiAgICAgICAgLy8gUHJpbnQgYWxsIHZhcmlhYmxlc1xuICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCJOYW1lOiBcIiArIG5hbWUpO1xuICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCJBZ2U6IFwiICsgYWdlKTtcbiAgICAgICAgU3lzdGVtLm91dC5wcmludGxuKFwiR3JhZGU6IFwiICsgZ3JhZGUpO1xuICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCJJcyBTdHVkZW50OiBcIiArIGlzU3R1ZGVudCk7XG4gICAgICAgIFN5c3RlbS5vdXQucHJpbnRsbihcIlByaWNlOiAkXCIgKyBwcmljZSk7XG4gICAgfVxufWAsXG4gICAgICAgICAgZXhlcmNpc2U6IHtcbiAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBgQ3JlYXRlIGEgcHJvZ3JhbSB0aGF0IGRlY2xhcmVzIHZhcmlhYmxlcyBmb3IgYSBzdHVkZW50J3MgaW5mb3JtYXRpb24gYW5kIHByaW50cyB0aGVtIG91dC5cblxuKipSZXF1aXJlbWVudHM6KipcbjEuIERlY2xhcmUgYSBTdHJpbmcgdmFyaWFibGUgZm9yIHRoZSBzdHVkZW50J3MgbmFtZVxuMi4gRGVjbGFyZSBhbiBpbnQgdmFyaWFibGUgZm9yIHRoZSBzdHVkZW50J3MgYWdlXG4zLiBEZWNsYXJlIGEgZG91YmxlIHZhcmlhYmxlIGZvciB0aGUgc3R1ZGVudCdzIEdQQVxuNC4gRGVjbGFyZSBhIGJvb2xlYW4gdmFyaWFibGUgZm9yIGVucm9sbG1lbnQgc3RhdHVzXG41LiBQcmludCBhbGwgdGhlIGluZm9ybWF0aW9uIGluIGEgZm9ybWF0dGVkIHdheVxuXG4qKkV4cGVjdGVkIGZvcm1hdDoqKlxuU3R1ZGVudDogW25hbWVdXG5BZ2U6IFthZ2VdXG5HUEE6IFtncGFdXG5FbnJvbGxlZDogW3N0YXR1c11gLFxuICAgICAgICAgICAgc3RhcnRlckNvZGU6IGBwdWJsaWMgY2xhc3MgU3R1ZGVudEluZm8ge1xuICAgIHB1YmxpYyBzdGF0aWMgdm9pZCBtYWluKFN0cmluZ1tdIGFyZ3MpIHtcbiAgICAgICAgLy8gVE9ETzogRGVjbGFyZSB2YXJpYWJsZXMgZm9yIHN0dWRlbnQgaW5mb3JtYXRpb25cblxuXG4gICAgICAgIC8vIFRPRE86IFByaW50IHRoZSBzdHVkZW50IGluZm9ybWF0aW9uXG5cbiAgICB9XG59YCxcbiAgICAgICAgICAgIGV4cGVjdGVkT3V0cHV0OiBgU3R1ZGVudDogQWxpY2UgSm9obnNvblxuQWdlOiAyMFxuR1BBOiAzLjhcbkVucm9sbGVkOiB0cnVlYCxcbiAgICAgICAgICAgIGhpbnRzOiBbXG4gICAgICAgICAgICAgIFwiVXNlIFN0cmluZyBmb3IgdGhlIHN0dWRlbnQncyBuYW1lXCIsXG4gICAgICAgICAgICAgIFwiVXNlIGludCBmb3IgYWdlLCBkb3VibGUgZm9yIEdQQSwgYW5kIGJvb2xlYW4gZm9yIGVucm9sbG1lbnQgc3RhdHVzXCIsXG4gICAgICAgICAgICAgIFwiVXNlIFN5c3RlbS5vdXQucHJpbnRsbigpIHRvIHByaW50IGVhY2ggbGluZVwiLFxuICAgICAgICAgICAgICBcIllvdSBjYW4gY29uY2F0ZW5hdGUgc3RyaW5ncyB1c2luZyB0aGUgKyBvcGVyYXRvclwiXG4gICAgICAgICAgICBdXG4gICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBuZXh0TGVzc29uSWQ6ICcyJyxcbiAgICAgICAgcHJldkxlc3NvbklkOiB1bmRlZmluZWRcbiAgICAgIH0sXG4gICAgICAnMic6IHtcbiAgICAgICAgaWQ6ICcyJyxcbiAgICAgICAgdGl0bGU6ICdDb250cm9sIFN0cnVjdHVyZXM6IElmIFN0YXRlbWVudHMgYW5kIExvb3BzJyxcbiAgICAgICAgZGVzY3JpcHRpb246ICdNYXN0ZXIgY29uZGl0aW9uYWwgc3RhdGVtZW50cyBhbmQgbG9vcHMgdG8gY29udHJvbCBwcm9ncmFtIGZsb3cuJyxcbiAgICAgICAgZGlmZmljdWx0eTogJ0JlZ2lubmVyJyxcbiAgICAgICAgZHVyYXRpb246ICc0NSBtaW4nLFxuICAgICAgICBjb250ZW50OiB7XG4gICAgICAgICAgdGhlb3J5OiBgXG4jIENvbnRyb2wgU3RydWN0dXJlcyBpbiBKYXZhXG5cbkNvbnRyb2wgc3RydWN0dXJlcyBhbGxvdyB5b3UgdG8gY29udHJvbCB0aGUgZmxvdyBvZiBleGVjdXRpb24gaW4geW91ciBKYXZhIHByb2dyYW1zLiBUaGV5IGluY2x1ZGUgY29uZGl0aW9uYWwgc3RhdGVtZW50cyBhbmQgbG9vcHMuXG5cbiMjIElmIFN0YXRlbWVudHNcbklmIHN0YXRlbWVudHMgZXhlY3V0ZSBjb2RlIGJhc2VkIG9uIGJvb2xlYW4gY29uZGl0aW9ucy5cblxuIyMjIEJhc2ljIElmIFN0YXRlbWVudDpcblxcYFxcYFxcYGphdmFcbmlmIChjb25kaXRpb24pIHtcbiAgICAvLyBjb2RlIHRvIGV4ZWN1dGUgaWYgY29uZGl0aW9uIGlzIHRydWVcbn1cblxcYFxcYFxcYFxuXG4jIyMgSWYtRWxzZSBTdGF0ZW1lbnQ6XG5cXGBcXGBcXGBqYXZhXG5pZiAoY29uZGl0aW9uKSB7XG4gICAgLy8gY29kZSBpZiB0cnVlXG59IGVsc2Uge1xuICAgIC8vIGNvZGUgaWYgZmFsc2Vcbn1cblxcYFxcYFxcYFxuXG4jIyMgSWYtRWxzZSBJZi1FbHNlIENoYWluOlxuXFxgXFxgXFxgamF2YVxuaWYgKGNvbmRpdGlvbjEpIHtcbiAgICAvLyBjb2RlIGlmIGNvbmRpdGlvbjEgaXMgdHJ1ZVxufSBlbHNlIGlmIChjb25kaXRpb24yKSB7XG4gICAgLy8gY29kZSBpZiBjb25kaXRpb24yIGlzIHRydWVcbn0gZWxzZSB7XG4gICAgLy8gY29kZSBpZiBhbGwgY29uZGl0aW9ucyBhcmUgZmFsc2Vcbn1cblxcYFxcYFxcYFxuXG4jIyBGb3IgTG9vcHNcbkZvciBsb29wcyByZXBlYXQgY29kZSBhIHNwZWNpZmljIG51bWJlciBvZiB0aW1lcy5cblxuIyMjIEJhc2ljIEZvciBMb29wOlxuXFxgXFxgXFxgamF2YVxuZm9yIChpbml0aWFsaXphdGlvbjsgY29uZGl0aW9uOyBpbmNyZW1lbnQpIHtcbiAgICAvLyBjb2RlIHRvIHJlcGVhdFxufVxuXFxgXFxgXFxgXG5cbiMjIyBFbmhhbmNlZCBGb3IgTG9vcCAoZm9yIGFycmF5cyk6XG5cXGBcXGBcXGBqYXZhXG5mb3IgKGRhdGFUeXBlIHZhcmlhYmxlIDogYXJyYXkpIHtcbiAgICAvLyBjb2RlIHVzaW5nIHZhcmlhYmxlXG59XG5cXGBcXGBcXGBcblxuIyMgV2hpbGUgTG9vcHNcbldoaWxlIGxvb3BzIHJlcGVhdCBjb2RlIHdoaWxlIGEgY29uZGl0aW9uIGlzIHRydWUuXG5cbiMjIyBXaGlsZSBMb29wOlxuXFxgXFxgXFxgamF2YVxud2hpbGUgKGNvbmRpdGlvbikge1xuICAgIC8vIGNvZGUgdG8gcmVwZWF0XG4gICAgLy8gZG9uJ3QgZm9yZ2V0IHRvIHVwZGF0ZSBjb25kaXRpb24hXG59XG5cXGBcXGBcXGBcblxuIyMjIERvLVdoaWxlIExvb3A6XG5cXGBcXGBcXGBqYXZhXG5kbyB7XG4gICAgLy8gY29kZSB0byByZXBlYXQgYXQgbGVhc3Qgb25jZVxufSB3aGlsZSAoY29uZGl0aW9uKTtcblxcYFxcYFxcYFxuXG4jIyBTd2l0Y2ggQ2FzZXNcblN3aXRjaCBzdGF0ZW1lbnRzIHByb3ZpZGUgYW4gYWx0ZXJuYXRpdmUgdG8gbXVsdGlwbGUgaWYtZWxzZSBzdGF0ZW1lbnRzLlxuXG5cXGBcXGBcXGBqYXZhXG5zd2l0Y2ggKHZhcmlhYmxlKSB7XG4gICAgY2FzZSB2YWx1ZTE6XG4gICAgICAgIC8vIGNvZGUgZm9yIHZhbHVlMVxuICAgICAgICBicmVhaztcbiAgICBjYXNlIHZhbHVlMjpcbiAgICAgICAgLy8gY29kZSBmb3IgdmFsdWUyXG4gICAgICAgIGJyZWFrO1xuICAgIGRlZmF1bHQ6XG4gICAgICAgIC8vIGRlZmF1bHQgY29kZVxuICAgICAgICBicmVhaztcbn1cblxcYFxcYFxcYFxuICAgICAgICAgIGAsXG4gICAgICAgICAgZXhhbXBsZTogYHB1YmxpYyBjbGFzcyBDb250cm9sU3RydWN0dXJlc0RlbW8ge1xuICAgIHB1YmxpYyBzdGF0aWMgdm9pZCBtYWluKFN0cmluZ1tdIGFyZ3MpIHtcbiAgICAgICAgLy8gSWYgU3RhdGVtZW50cyBFeGFtcGxlXG4gICAgICAgIGludCBzY29yZSA9IDg1O1xuICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCI9PT0gSWYgU3RhdGVtZW50cyA9PT1cIik7XG5cbiAgICAgICAgaWYgKHNjb3JlID49IDkwKSB7XG4gICAgICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCJHcmFkZTogQSAtIEV4Y2VsbGVudCFcIik7XG4gICAgICAgIH0gZWxzZSBpZiAoc2NvcmUgPj0gODApIHtcbiAgICAgICAgICAgIFN5c3RlbS5vdXQucHJpbnRsbihcIkdyYWRlOiBCIC0gR29vZCBqb2IhXCIpO1xuICAgICAgICB9IGVsc2UgaWYgKHNjb3JlID49IDcwKSB7XG4gICAgICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCJHcmFkZTogQyAtIEF2ZXJhZ2VcIik7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCJHcmFkZTogRiAtIE5lZWQgaW1wcm92ZW1lbnRcIik7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBGb3IgTG9vcCBFeGFtcGxlXG4gICAgICAgIFN5c3RlbS5vdXQucHJpbnRsbihcIlxcXFxuPT09IEZvciBMb29wID09PVwiKTtcbiAgICAgICAgU3lzdGVtLm91dC5wcmludGxuKFwiQ291bnRpbmcgZnJvbSAxIHRvIDU6XCIpO1xuICAgICAgICBmb3IgKGludCBpID0gMTsgaSA8PSA1OyBpKyspIHtcbiAgICAgICAgICAgIFN5c3RlbS5vdXQucHJpbnRsbihcIkNvdW50OiBcIiArIGkpO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gV2hpbGUgTG9vcCBFeGFtcGxlXG4gICAgICAgIFN5c3RlbS5vdXQucHJpbnRsbihcIlxcXFxuPT09IFdoaWxlIExvb3AgPT09XCIpO1xuICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCJDb3VudGRvd246XCIpO1xuICAgICAgICBpbnQgY291bnRkb3duID0gMztcbiAgICAgICAgd2hpbGUgKGNvdW50ZG93biA+IDApIHtcbiAgICAgICAgICAgIFN5c3RlbS5vdXQucHJpbnRsbihjb3VudGRvd24gKyBcIi4uLlwiKTtcbiAgICAgICAgICAgIGNvdW50ZG93bi0tO1xuICAgICAgICB9XG4gICAgICAgIFN5c3RlbS5vdXQucHJpbnRsbihcIkJsYXN0IG9mZiFcIik7XG5cbiAgICAgICAgLy8gU3dpdGNoIENhc2UgRXhhbXBsZVxuICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCJcXFxcbj09PSBTd2l0Y2ggQ2FzZSA9PT1cIik7XG4gICAgICAgIGludCBkYXlPZldlZWsgPSAzO1xuICAgICAgICBzd2l0Y2ggKGRheU9mV2Vlaykge1xuICAgICAgICAgICAgY2FzZSAxOlxuICAgICAgICAgICAgICAgIFN5c3RlbS5vdXQucHJpbnRsbihcIk1vbmRheVwiKTtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIGNhc2UgMjpcbiAgICAgICAgICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCJUdWVzZGF5XCIpO1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgY2FzZSAzOlxuICAgICAgICAgICAgICAgIFN5c3RlbS5vdXQucHJpbnRsbihcIldlZG5lc2RheVwiKTtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIGNhc2UgNDpcbiAgICAgICAgICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCJUaHVyc2RheVwiKTtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIGNhc2UgNTpcbiAgICAgICAgICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCJGcmlkYXlcIik7XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgICAgIFN5c3RlbS5vdXQucHJpbnRsbihcIldlZWtlbmQhXCIpO1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICB9XG4gICAgfVxufWAsXG4gICAgICAgICAgZXhlcmNpc2U6IHtcbiAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBgQ3JlYXRlIGEgcHJvZ3JhbSB0aGF0IGRlbW9uc3RyYXRlcyBhbGwgZm91ciBjb250cm9sIHN0cnVjdHVyZSB0b3BpY3M6IElmIFN0YXRlbWVudHMsIEZvciBMb29wcywgV2hpbGUgTG9vcHMsIGFuZCBTd2l0Y2ggQ2FzZXMuXG5cbioqUmVxdWlyZW1lbnRzOioqXG4xLiAqKklmIFN0YXRlbWVudHMqKjogQ2hlY2sgaWYgYSBzdHVkZW50J3MgZ3JhZGUgKDAtMTAwKSBhbmQgcHJpbnQgdGhlIGxldHRlciBncmFkZSAoQSwgQiwgQywgRCwgRilcbjIuICoqRm9yIExvb3BzKio6IFByaW50IG11bHRpcGxpY2F0aW9uIHRhYmxlIGZvciBudW1iZXIgNSAoNXgxIHRvIDV4MTApXG4zLiAqKldoaWxlIExvb3BzKio6IFByaW50IG51bWJlcnMgZnJvbSAxMCBkb3duIHRvIDFcbjQuICoqU3dpdGNoIENhc2VzKio6IEdpdmVuIGEgbW9udGggbnVtYmVyICgxLTEyKSwgcHJpbnQgdGhlIHNlYXNvblxuXG4qKkV4cGVjdGVkIG91dHB1dCBmb3JtYXQ6KipcbkdyYWRlOiBbbGV0dGVyIGdyYWRlXVxuPT09IE11bHRpcGxpY2F0aW9uIFRhYmxlID09PVxuNSB4IDEgPSA1XG41IHggMiA9IDEwXG4uLi5cbj09PSBDb3VudGRvd24gPT09XG4xMFxuOVxuLi4uXG4xXG5TZWFzb246IFtzZWFzb24gbmFtZV1gLFxuICAgICAgICAgICAgc3RhcnRlckNvZGU6IGBwdWJsaWMgY2xhc3MgQ29udHJvbFN0cnVjdHVyZXNFeGVyY2lzZSB7XG4gICAgcHVibGljIHN0YXRpYyB2b2lkIG1haW4oU3RyaW5nW10gYXJncykge1xuICAgICAgICAvLyBUZXN0IHZhbHVlc1xuICAgICAgICBpbnQgZ3JhZGUgPSA4NztcbiAgICAgICAgaW50IHRhYmxlTnVtYmVyID0gNTtcbiAgICAgICAgaW50IG1vbnRoID0gNjtcblxuICAgICAgICAvLyBUT0RPOiAxLiBJZiBTdGF0ZW1lbnRzIC0gQ2hlY2sgZ3JhZGUgYW5kIHByaW50IGxldHRlciBncmFkZVxuICAgICAgICAvLyBBOiA5MC0xMDAsIEI6IDgwLTg5LCBDOiA3MC03OSwgRDogNjAtNjksIEY6IGJlbG93IDYwXG5cblxuICAgICAgICAvLyBUT0RPOiAyLiBGb3IgTG9vcCAtIFByaW50IG11bHRpcGxpY2F0aW9uIHRhYmxlIGZvciB0YWJsZU51bWJlclxuICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCI9PT0gTXVsdGlwbGljYXRpb24gVGFibGUgPT09XCIpO1xuXG5cbiAgICAgICAgLy8gVE9ETzogMy4gV2hpbGUgTG9vcCAtIFByaW50IGNvdW50ZG93biBmcm9tIDEwIHRvIDFcbiAgICAgICAgU3lzdGVtLm91dC5wcmludGxuKFwiPT09IENvdW50ZG93biA9PT1cIik7XG5cblxuICAgICAgICAvLyBUT0RPOiA0LiBTd2l0Y2ggQ2FzZSAtIFByaW50IHNlYXNvbiBiYXNlZCBvbiBtb250aFxuICAgICAgICAvLyBTcHJpbmc6IDMsNCw1ICBTdW1tZXI6IDYsNyw4ICBGYWxsOiA5LDEwLDExICBXaW50ZXI6IDEyLDEsMlxuXG4gICAgfVxufWAsXG4gICAgICAgICAgICBleHBlY3RlZE91dHB1dDogYEdyYWRlOiBCXG49PT0gTXVsdGlwbGljYXRpb24gVGFibGUgPT09XG41IHggMSA9IDVcbjUgeCAyID0gMTBcbjUgeCAzID0gMTVcbjUgeCA0ID0gMjBcbjUgeCA1ID0gMjVcbjUgeCA2ID0gMzBcbjUgeCA3ID0gMzVcbjUgeCA4ID0gNDBcbjUgeCA5ID0gNDVcbjUgeCAxMCA9IDUwXG49PT0gQ291bnRkb3duID09PVxuMTBcbjlcbjhcbjdcbjZcbjVcbjRcbjNcbjJcbjFcblNlYXNvbjogU3VtbWVyYCxcbiAgICAgICAgICAgIGhpbnRzOiBbXG4gICAgICAgICAgICAgIFwiRm9yIGlmIHN0YXRlbWVudHM6IFVzZSA+PSBmb3IgZ3JhZGUgcmFuZ2VzICg5MCwgODAsIDcwLCA2MClcIixcbiAgICAgICAgICAgICAgXCJGb3IgbXVsdGlwbGljYXRpb246IFVzZSBmb3IoaW50IGkgPSAxOyBpIDw9IDEwOyBpKyspXCIsXG4gICAgICAgICAgICAgIFwiRm9yIGNvdW50ZG93bjogVXNlIHdoaWxlIGxvb3Agd2l0aCBpbnQgY291bnRlciA9IDEwOyBjb3VudGVyID49IDE7IGNvdW50ZXItLVwiLFxuICAgICAgICAgICAgICBcIkZvciBzd2l0Y2g6IFVzZSBjYXNlcyAzLDQsNSBmb3IgU3ByaW5nOyA2LDcsOCBmb3IgU3VtbWVyLCBldGMuXCJcbiAgICAgICAgICAgIF1cbiAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIG5leHRMZXNzb25JZDogJzMnLFxuICAgICAgICBwcmV2TGVzc29uSWQ6ICcxJ1xuICAgICAgfSxcbiAgICAgICczJzoge1xuICAgICAgICBpZDogJzMnLFxuICAgICAgICB0aXRsZTogJ01ldGhvZHMgYW5kIEZ1bmN0aW9ucycsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnTGVhcm4gaG93IHRvIGNyZWF0ZSByZXVzYWJsZSBjb2RlIHdpdGggbWV0aG9kcyBhbmQgdW5kZXJzdGFuZCBwYXJhbWV0ZXJzLicsXG4gICAgICAgIGRpZmZpY3VsdHk6ICdCZWdpbm5lcicsXG4gICAgICAgIGR1cmF0aW9uOiAnNDAgbWluJyxcbiAgICAgICAgY29udGVudDoge1xuICAgICAgICAgIHRoZW9yeTogYFxuIyBNZXRob2RzIGFuZCBGdW5jdGlvbnMgaW4gSmF2YVxuXG5NZXRob2RzIGFyZSBibG9ja3Mgb2YgY29kZSB0aGF0IHBlcmZvcm0gc3BlY2lmaWMgdGFza3MgYW5kIGNhbiBiZSByZXVzZWQgdGhyb3VnaG91dCB5b3VyIHByb2dyYW0uXG5cbiMjIE1ldGhvZCBEZWNsYXJhdGlvblxuVGhlIGJhc2ljIHN5bnRheCBmb3IgZGVjbGFyaW5nIGEgbWV0aG9kOlxuXG5cXGBcXGBcXGBqYXZhXG5hY2Nlc3NNb2RpZmllciByZXR1cm5UeXBlIG1ldGhvZE5hbWUocGFyYW1ldGVycykge1xuICAgIC8vIG1ldGhvZCBib2R5XG4gICAgcmV0dXJuIHZhbHVlOyAvLyBpZiByZXR1cm5UeXBlIGlzIG5vdCB2b2lkXG59XG5cXGBcXGBcXGBcblxuIyMjIENvbXBvbmVudHM6XG4tICoqQWNjZXNzIE1vZGlmaWVyKio6IHB1YmxpYywgcHJpdmF0ZSwgcHJvdGVjdGVkXG4tICoqUmV0dXJuIFR5cGUqKjogdm9pZCwgaW50LCBTdHJpbmcsIGV0Yy5cbi0gKipNZXRob2QgTmFtZSoqOiBmb2xsb3dzIGNhbWVsQ2FzZSBjb252ZW50aW9uXG4tICoqUGFyYW1ldGVycyoqOiBpbnB1dCB2YWx1ZXMgKG9wdGlvbmFsKVxuXG4jIyBNZXRob2QgRGVjbGFyYXRpb24gRXhhbXBsZXNcblxuIyMjIFZvaWQgTWV0aG9kIChubyByZXR1cm4gdmFsdWUpOlxuXFxgXFxgXFxgamF2YVxucHVibGljIHZvaWQgcHJpbnRNZXNzYWdlKCkge1xuICAgIFN5c3RlbS5vdXQucHJpbnRsbihcIkhlbGxvIGZyb20gbWV0aG9kIVwiKTtcbn1cblxcYFxcYFxcYFxuXG4jIyMgTWV0aG9kIHdpdGggUmV0dXJuIFZhbHVlOlxuXFxgXFxgXFxgamF2YVxucHVibGljIGludCBhZGROdW1iZXJzKGludCBhLCBpbnQgYikge1xuICAgIHJldHVybiBhICsgYjtcbn1cblxcYFxcYFxcYFxuXG4jIyBQYXJhbWV0ZXJzXG5QYXJhbWV0ZXJzIGFsbG93IHlvdSB0byBwYXNzIGRhdGEgaW50byBtZXRob2RzLlxuXG4jIyMgU2luZ2xlIFBhcmFtZXRlcjpcblxcYFxcYFxcYGphdmFcbnB1YmxpYyB2b2lkIGdyZWV0VXNlcihTdHJpbmcgbmFtZSkge1xuICAgIFN5c3RlbS5vdXQucHJpbnRsbihcIkhlbGxvLCBcIiArIG5hbWUgKyBcIiFcIik7XG59XG5cXGBcXGBcXGBcblxuIyMjIE11bHRpcGxlIFBhcmFtZXRlcnM6XG5cXGBcXGBcXGBqYXZhXG5wdWJsaWMgZG91YmxlIGNhbGN1bGF0ZUFyZWEoZG91YmxlIGxlbmd0aCwgZG91YmxlIHdpZHRoKSB7XG4gICAgcmV0dXJuIGxlbmd0aCAqIHdpZHRoO1xufVxuXFxgXFxgXFxgXG5cbiMjIFJldHVybiBUeXBlc1xuTWV0aG9kcyBjYW4gcmV0dXJuIGRpZmZlcmVudCB0eXBlcyBvZiBkYXRhLlxuXG4jIyMgQ29tbW9uIFJldHVybiBUeXBlczpcbi0gKip2b2lkKio6IE5vIHJldHVybiB2YWx1ZVxuLSAqKmludCoqOiBJbnRlZ2VyIG51bWJlcnNcbi0gKipkb3VibGUqKjogRGVjaW1hbCBudW1iZXJzXG4tICoqU3RyaW5nKio6IFRleHRcbi0gKipib29sZWFuKio6IHRydWUvZmFsc2VcblxuIyMgTWV0aG9kIE92ZXJsb2FkaW5nXG5Zb3UgY2FuIGhhdmUgbXVsdGlwbGUgbWV0aG9kcyB3aXRoIHRoZSBzYW1lIG5hbWUgYnV0IGRpZmZlcmVudCBwYXJhbWV0ZXJzLlxuXG5cXGBcXGBcXGBqYXZhXG5wdWJsaWMgaW50IGFkZChpbnQgYSwgaW50IGIpIHtcbiAgICByZXR1cm4gYSArIGI7XG59XG5cbnB1YmxpYyBkb3VibGUgYWRkKGRvdWJsZSBhLCBkb3VibGUgYikge1xuICAgIHJldHVybiBhICsgYjtcbn1cblxucHVibGljIGludCBhZGQoaW50IGEsIGludCBiLCBpbnQgYykge1xuICAgIHJldHVybiBhICsgYiArIGM7XG59XG5cXGBcXGBcXGBcbiAgICAgICAgICBgLFxuICAgICAgICAgIGV4YW1wbGU6IGBwdWJsaWMgY2xhc3MgTWV0aG9kc0RlbW8ge1xuXG4gICAgLy8gTWV0aG9kIERlY2xhcmF0aW9uIC0gdm9pZCBtZXRob2Qgd2l0aCBubyBwYXJhbWV0ZXJzXG4gICAgcHVibGljIHN0YXRpYyB2b2lkIHByaW50V2VsY29tZSgpIHtcbiAgICAgICAgU3lzdGVtLm91dC5wcmludGxuKFwiV2VsY29tZSB0byBKYXZhIE1ldGhvZHMhXCIpO1xuICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCI9PT09PT09PT09PT09PT09PT09PT09PT09XCIpO1xuICAgIH1cblxuICAgIC8vIE1ldGhvZCB3aXRoIFBhcmFtZXRlcnMgLSBzaW5nbGUgcGFyYW1ldGVyXG4gICAgcHVibGljIHN0YXRpYyB2b2lkIGdyZWV0VXNlcihTdHJpbmcgdXNlck5hbWUpIHtcbiAgICAgICAgU3lzdGVtLm91dC5wcmludGxuKFwiSGVsbG8sIFwiICsgdXNlck5hbWUgKyBcIiEgTmljZSB0byBtZWV0IHlvdS5cIik7XG4gICAgfVxuXG4gICAgLy8gTWV0aG9kIHdpdGggUmV0dXJuIFR5cGUgLSByZXR1cm5zIGFuIGludGVnZXJcbiAgICBwdWJsaWMgc3RhdGljIGludCBhZGRUd29OdW1iZXJzKGludCBudW0xLCBpbnQgbnVtMikge1xuICAgICAgICBpbnQgc3VtID0gbnVtMSArIG51bTI7XG4gICAgICAgIHJldHVybiBzdW07XG4gICAgfVxuXG4gICAgLy8gTWV0aG9kIHdpdGggbXVsdGlwbGUgcGFyYW1ldGVycyBhbmQgcmV0dXJuIHR5cGVcbiAgICBwdWJsaWMgc3RhdGljIGRvdWJsZSBjYWxjdWxhdGVSZWN0YW5nbGVBcmVhKGRvdWJsZSBsZW5ndGgsIGRvdWJsZSB3aWR0aCkge1xuICAgICAgICByZXR1cm4gbGVuZ3RoICogd2lkdGg7XG4gICAgfVxuXG4gICAgLy8gTWV0aG9kIE92ZXJsb2FkaW5nIC0gc2FtZSBuYW1lLCBkaWZmZXJlbnQgcGFyYW1ldGVyc1xuICAgIHB1YmxpYyBzdGF0aWMgaW50IG11bHRpcGx5KGludCBhLCBpbnQgYikge1xuICAgICAgICByZXR1cm4gYSAqIGI7XG4gICAgfVxuXG4gICAgcHVibGljIHN0YXRpYyBkb3VibGUgbXVsdGlwbHkoZG91YmxlIGEsIGRvdWJsZSBiKSB7XG4gICAgICAgIHJldHVybiBhICogYjtcbiAgICB9XG5cbiAgICBwdWJsaWMgc3RhdGljIGludCBtdWx0aXBseShpbnQgYSwgaW50IGIsIGludCBjKSB7XG4gICAgICAgIHJldHVybiBhICogYiAqIGM7XG4gICAgfVxuXG4gICAgcHVibGljIHN0YXRpYyB2b2lkIG1haW4oU3RyaW5nW10gYXJncykge1xuICAgICAgICAvLyBDYWxsaW5nIG1ldGhvZHNcbiAgICAgICAgcHJpbnRXZWxjb21lKCk7XG5cbiAgICAgICAgZ3JlZXRVc2VyKFwiQWxpY2VcIik7XG4gICAgICAgIGdyZWV0VXNlcihcIkJvYlwiKTtcblxuICAgICAgICBpbnQgcmVzdWx0ID0gYWRkVHdvTnVtYmVycygxNSwgMjUpO1xuICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCIxNSArIDI1ID0gXCIgKyByZXN1bHQpO1xuXG4gICAgICAgIGRvdWJsZSBhcmVhID0gY2FsY3VsYXRlUmVjdGFuZ2xlQXJlYSg1LjUsIDMuMik7XG4gICAgICAgIFN5c3RlbS5vdXQucHJpbnRsbihcIlJlY3RhbmdsZSBhcmVhOiBcIiArIGFyZWEpO1xuXG4gICAgICAgIC8vIE1ldGhvZCBvdmVybG9hZGluZyBleGFtcGxlc1xuICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCIyICogMyA9IFwiICsgbXVsdGlwbHkoMiwgMykpO1xuICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCIyLjUgKiAzLjAgPSBcIiArIG11bHRpcGx5KDIuNSwgMy4wKSk7XG4gICAgICAgIFN5c3RlbS5vdXQucHJpbnRsbihcIjIgKiAzICogNCA9IFwiICsgbXVsdGlwbHkoMiwgMywgNCkpO1xuICAgIH1cbn1gLFxuICAgICAgICAgIGV4ZXJjaXNlOiB7XG4gICAgICAgICAgICBkZXNjcmlwdGlvbjogYENyZWF0ZSBhIHByb2dyYW0gdGhhdCBkZW1vbnN0cmF0ZXMgYWxsIGZvdXIgbWV0aG9kIHRvcGljczogTWV0aG9kIERlY2xhcmF0aW9uLCBQYXJhbWV0ZXJzLCBSZXR1cm4gVHlwZXMsIGFuZCBNZXRob2QgT3ZlcmxvYWRpbmcuXG5cbioqUmVxdWlyZW1lbnRzOioqXG4xLiAqKk1ldGhvZCBEZWNsYXJhdGlvbioqOiBDcmVhdGUgYSB2b2lkIG1ldGhvZCB0aGF0IHByaW50cyBhIHdlbGNvbWUgbWVzc2FnZVxuMi4gKipQYXJhbWV0ZXJzKio6IENyZWF0ZSBhIG1ldGhvZCB0aGF0IHRha2VzIGEgbmFtZSBhbmQgYWdlLCBwcmludHMgYSBwZXJzb25hbGl6ZWQgbWVzc2FnZVxuMy4gKipSZXR1cm4gVHlwZXMqKjogQ3JlYXRlIGEgbWV0aG9kIHRoYXQgY2FsY3VsYXRlcyBhbmQgcmV0dXJucyB0aGUgc3F1YXJlIG9mIGEgbnVtYmVyXG40LiAqKk1ldGhvZCBPdmVybG9hZGluZyoqOiBDcmVhdGUgdGhyZWUgdmVyc2lvbnMgb2YgYSBcImNhbGN1bGF0ZVwiIG1ldGhvZDpcbiAgIC0gT25lIHRoYXQgYWRkcyB0d28gaW50ZWdlcnNcbiAgIC0gT25lIHRoYXQgYWRkcyB0d28gZG91Ymxlc1xuICAgLSBPbmUgdGhhdCBhZGRzIHRocmVlIGludGVnZXJzXG5cbioqRXhwZWN0ZWQgb3V0cHV0IGZvcm1hdDoqKlxuV2VsY29tZSB0byB0aGUgTWV0aG9kIEV4ZXJjaXNlIVxuSGVsbG8gSm9obiwgeW91IGFyZSAyNSB5ZWFycyBvbGQuXG5UaGUgc3F1YXJlIG9mIDcgaXM6IDQ5XG5TdW0gb2YgNSBhbmQgMzogOFxuU3VtIG9mIDIuNSBhbmQgMy43OiA2LjJcblN1bSBvZiAxLCAyLCBhbmQgMzogNmAsXG4gICAgICAgICAgICBzdGFydGVyQ29kZTogYHB1YmxpYyBjbGFzcyBNZXRob2RzRXhlcmNpc2Uge1xuXG4gICAgLy8gVE9ETzogMS4gTWV0aG9kIERlY2xhcmF0aW9uIC0gQ3JlYXRlIGEgdm9pZCBtZXRob2QgY2FsbGVkIHByaW50V2VsY29tZSgpXG5cblxuICAgIC8vIFRPRE86IDIuIFBhcmFtZXRlcnMgLSBDcmVhdGUgYSBtZXRob2QgY2FsbGVkIHByaW50UGVyc29uSW5mbyhTdHJpbmcgbmFtZSwgaW50IGFnZSlcblxuXG4gICAgLy8gVE9ETzogMy4gUmV0dXJuIFR5cGVzIC0gQ3JlYXRlIGEgbWV0aG9kIGNhbGxlZCBjYWxjdWxhdGVTcXVhcmUoaW50IG51bWJlcikgdGhhdCByZXR1cm5zIGludFxuXG5cbiAgICAvLyBUT0RPOiA0LiBNZXRob2QgT3ZlcmxvYWRpbmcgLSBDcmVhdGUgdGhyZWUgY2FsY3VsYXRlIG1ldGhvZHM6XG4gICAgLy8gLSBjYWxjdWxhdGUoaW50IGEsIGludCBiKSByZXR1cm5zIGludFxuICAgIC8vIC0gY2FsY3VsYXRlKGRvdWJsZSBhLCBkb3VibGUgYikgcmV0dXJucyBkb3VibGVcbiAgICAvLyAtIGNhbGN1bGF0ZShpbnQgYSwgaW50IGIsIGludCBjKSByZXR1cm5zIGludFxuXG5cblxuXG4gICAgcHVibGljIHN0YXRpYyB2b2lkIG1haW4oU3RyaW5nW10gYXJncykge1xuICAgICAgICAvLyBUT0RPOiBDYWxsIGFsbCB5b3VyIG1ldGhvZHMgaGVyZSB3aXRoIHRoZXNlIHZhbHVlczpcbiAgICAgICAgLy8gcHJpbnRXZWxjb21lKClcbiAgICAgICAgLy8gcHJpbnRQZXJzb25JbmZvKFwiSm9oblwiLCAyNSlcbiAgICAgICAgLy8gY2FsY3VsYXRlU3F1YXJlKDcpXG4gICAgICAgIC8vIGNhbGN1bGF0ZSg1LCAzKVxuICAgICAgICAvLyBjYWxjdWxhdGUoMi41LCAzLjcpXG4gICAgICAgIC8vIGNhbGN1bGF0ZSgxLCAyLCAzKVxuXG4gICAgfVxufWAsXG4gICAgICAgICAgICBleHBlY3RlZE91dHB1dDogYFdlbGNvbWUgdG8gdGhlIE1ldGhvZCBFeGVyY2lzZSFcbkhlbGxvIEpvaG4sIHlvdSBhcmUgMjUgeWVhcnMgb2xkLlxuVGhlIHNxdWFyZSBvZiA3IGlzOiA0OVxuU3VtIG9mIDUgYW5kIDM6IDhcblN1bSBvZiAyLjUgYW5kIDMuNzogNi4yXG5TdW0gb2YgMSwgMiwgYW5kIDM6IDZgLFxuICAgICAgICAgICAgaGludHM6IFtcbiAgICAgICAgICAgICAgXCJVc2UgJ3B1YmxpYyBzdGF0aWMgdm9pZCcgZm9yIG1ldGhvZHMgdGhhdCBkb24ndCByZXR1cm4gdmFsdWVzXCIsXG4gICAgICAgICAgICAgIFwiVXNlICdwdWJsaWMgc3RhdGljIGludCcgZm9yIG1ldGhvZHMgdGhhdCByZXR1cm4gaW50ZWdlcnNcIixcbiAgICAgICAgICAgICAgXCJNZXRob2Qgb3ZlcmxvYWRpbmcgbWVhbnMgc2FtZSBuYW1lLCBkaWZmZXJlbnQgcGFyYW1ldGVyIHR5cGVzIG9yIGNvdW50c1wiLFxuICAgICAgICAgICAgICBcIkRvbid0IGZvcmdldCB0byBjYWxsIGFsbCBtZXRob2RzIGluIG1haW4oKSBhbmQgcHJpbnQgdGhlIHJlc3VsdHNcIlxuICAgICAgICAgICAgXVxuICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgbmV4dExlc3NvbklkOiAnNCcsXG4gICAgICAgIHByZXZMZXNzb25JZDogJzInXG4gICAgICB9LFxuICAgICAgJzQnOiB7XG4gICAgICAgIGlkOiAnNCcsXG4gICAgICAgIHRpdGxlOiAnT2JqZWN0LU9yaWVudGVkIFByb2dyYW1taW5nOiBDbGFzc2VzIGFuZCBPYmplY3RzJyxcbiAgICAgICAgZGVzY3JpcHRpb246ICdJbnRyb2R1Y3Rpb24gdG8gT09QIGNvbmNlcHRzIHdpdGggY2xhc3Nlcywgb2JqZWN0cywgYW5kIGVuY2Fwc3VsYXRpb24uJyxcbiAgICAgICAgZGlmZmljdWx0eTogJ0ludGVybWVkaWF0ZScsXG4gICAgICAgIGR1cmF0aW9uOiAnNjAgbWluJyxcbiAgICAgICAgY29udGVudDoge1xuICAgICAgICAgIHRoZW9yeTogYFxuIyBPYmplY3QtT3JpZW50ZWQgUHJvZ3JhbW1pbmcgaW4gSmF2YVxuXG5PYmplY3QtT3JpZW50ZWQgUHJvZ3JhbW1pbmcgKE9PUCkgaXMgYSBwcm9ncmFtbWluZyBwYXJhZGlnbSBiYXNlZCBvbiB0aGUgY29uY2VwdCBvZiBcIm9iamVjdHNcIiB3aGljaCBjb250YWluIGRhdGEgYW5kIGNvZGUuXG5cbiMjIENsYXNzZXNcbkEgY2xhc3MgaXMgYSBibHVlcHJpbnQgb3IgdGVtcGxhdGUgZm9yIGNyZWF0aW5nIG9iamVjdHMuIEl0IGRlZmluZXMgdGhlIHByb3BlcnRpZXMgYW5kIGJlaGF2aW9ycyB0aGF0IG9iamVjdHMgb2YgdGhhdCB0eXBlIHdpbGwgaGF2ZS5cblxuIyMjIENsYXNzIERlY2xhcmF0aW9uOlxuXFxgXFxgXFxgamF2YVxucHVibGljIGNsYXNzIENsYXNzTmFtZSB7XG4gICAgLy8gZmllbGRzIChhdHRyaWJ1dGVzKVxuICAgIC8vIGNvbnN0cnVjdG9yc1xuICAgIC8vIG1ldGhvZHNcbn1cblxcYFxcYFxcYFxuXG4jIyMgRXhhbXBsZSBDbGFzczpcblxcYFxcYFxcYGphdmFcbnB1YmxpYyBjbGFzcyBDYXIge1xuICAgIC8vIEZpZWxkcyAoYXR0cmlidXRlcylcbiAgICBwcml2YXRlIFN0cmluZyBicmFuZDtcbiAgICBwcml2YXRlIFN0cmluZyBtb2RlbDtcbiAgICBwcml2YXRlIGludCB5ZWFyO1xuXG4gICAgLy8gQ29uc3RydWN0b3JcbiAgICBwdWJsaWMgQ2FyKFN0cmluZyBicmFuZCwgU3RyaW5nIG1vZGVsLCBpbnQgeWVhcikge1xuICAgICAgICB0aGlzLmJyYW5kID0gYnJhbmQ7XG4gICAgICAgIHRoaXMubW9kZWwgPSBtb2RlbDtcbiAgICAgICAgdGhpcy55ZWFyID0geWVhcjtcbiAgICB9XG5cbiAgICAvLyBNZXRob2RzXG4gICAgcHVibGljIHZvaWQgc3RhcnRFbmdpbmUoKSB7XG4gICAgICAgIFN5c3RlbS5vdXQucHJpbnRsbihcIkVuZ2luZSBzdGFydGVkIVwiKTtcbiAgICB9XG59XG5cXGBcXGBcXGBcblxuIyMgT2JqZWN0c1xuQW4gb2JqZWN0IGlzIGFuIGluc3RhbmNlIG9mIGEgY2xhc3MuIFlvdSBjcmVhdGUgb2JqZWN0cyB1c2luZyB0aGUgXFxgbmV3XFxgIGtleXdvcmQuXG5cblxcYFxcYFxcYGphdmFcbkNhciBteUNhciA9IG5ldyBDYXIoXCJUb3lvdGFcIiwgXCJDYW1yeVwiLCAyMDIzKTtcblxcYFxcYFxcYFxuXG4jIyBDb25zdHJ1Y3RvcnNcbkNvbnN0cnVjdG9ycyBhcmUgc3BlY2lhbCBtZXRob2RzIHVzZWQgdG8gaW5pdGlhbGl6ZSBvYmplY3RzIHdoZW4gdGhleSBhcmUgY3JlYXRlZC5cblxuIyMjIERlZmF1bHQgQ29uc3RydWN0b3I6XG5cXGBcXGBcXGBqYXZhXG5wdWJsaWMgQ2FyKCkge1xuICAgIC8vIGRlZmF1bHQgdmFsdWVzXG59XG5cXGBcXGBcXGBcblxuIyMjIFBhcmFtZXRlcml6ZWQgQ29uc3RydWN0b3I6XG5cXGBcXGBcXGBqYXZhXG5wdWJsaWMgQ2FyKFN0cmluZyBicmFuZCwgU3RyaW5nIG1vZGVsLCBpbnQgeWVhcikge1xuICAgIHRoaXMuYnJhbmQgPSBicmFuZDtcbiAgICB0aGlzLm1vZGVsID0gbW9kZWw7XG4gICAgdGhpcy55ZWFyID0geWVhcjtcbn1cblxcYFxcYFxcYFxuXG4jIyBFbmNhcHN1bGF0aW9uXG5FbmNhcHN1bGF0aW9uIGlzIHRoZSBwcmFjdGljZSBvZiBrZWVwaW5nIGZpZWxkcyBwcml2YXRlIGFuZCBwcm92aWRpbmcgcHVibGljIG1ldGhvZHMgdG8gYWNjZXNzIHRoZW0uXG5cbiMjIyBQcml2YXRlIEZpZWxkcyB3aXRoIFB1YmxpYyBNZXRob2RzOlxuXFxgXFxgXFxgamF2YVxucHJpdmF0ZSBTdHJpbmcgbmFtZTtcblxucHVibGljIFN0cmluZyBnZXROYW1lKCkge1xuICAgIHJldHVybiBuYW1lO1xufVxuXG5wdWJsaWMgdm9pZCBzZXROYW1lKFN0cmluZyBuYW1lKSB7XG4gICAgdGhpcy5uYW1lID0gbmFtZTtcbn1cblxcYFxcYFxcYFxuICAgICAgICAgIGAsXG4gICAgICAgICAgZXhhbXBsZTogYC8vIFN0dWRlbnQgY2xhc3MgZGVtb25zdHJhdGluZyBPT1AgY29uY2VwdHNcbmNsYXNzIFN0dWRlbnQge1xuICAgIC8vIFByaXZhdGUgZmllbGRzIChFbmNhcHN1bGF0aW9uKVxuICAgIHByaXZhdGUgU3RyaW5nIG5hbWU7XG4gICAgcHJpdmF0ZSBpbnQgYWdlO1xuICAgIHByaXZhdGUgU3RyaW5nIHN0dWRlbnRJZDtcbiAgICBwcml2YXRlIGRvdWJsZSBncGE7XG5cbiAgICAvLyBEZWZhdWx0IENvbnN0cnVjdG9yXG4gICAgcHVibGljIFN0dWRlbnQoKSB7XG4gICAgICAgIHRoaXMubmFtZSA9IFwiVW5rbm93blwiO1xuICAgICAgICB0aGlzLmFnZSA9IDA7XG4gICAgICAgIHRoaXMuc3R1ZGVudElkID0gXCIwMDAwMDBcIjtcbiAgICAgICAgdGhpcy5ncGEgPSAwLjA7XG4gICAgfVxuXG4gICAgLy8gUGFyYW1ldGVyaXplZCBDb25zdHJ1Y3RvclxuICAgIHB1YmxpYyBTdHVkZW50KFN0cmluZyBuYW1lLCBpbnQgYWdlLCBTdHJpbmcgc3R1ZGVudElkLCBkb3VibGUgZ3BhKSB7XG4gICAgICAgIHRoaXMubmFtZSA9IG5hbWU7XG4gICAgICAgIHRoaXMuYWdlID0gYWdlO1xuICAgICAgICB0aGlzLnN0dWRlbnRJZCA9IHN0dWRlbnRJZDtcbiAgICAgICAgdGhpcy5ncGEgPSBncGE7XG4gICAgfVxuXG4gICAgLy8gR2V0dGVyIG1ldGhvZHMgKEVuY2Fwc3VsYXRpb24pXG4gICAgcHVibGljIFN0cmluZyBnZXROYW1lKCkge1xuICAgICAgICByZXR1cm4gbmFtZTtcbiAgICB9XG5cbiAgICBwdWJsaWMgaW50IGdldEFnZSgpIHtcbiAgICAgICAgcmV0dXJuIGFnZTtcbiAgICB9XG5cbiAgICBwdWJsaWMgU3RyaW5nIGdldFN0dWRlbnRJZCgpIHtcbiAgICAgICAgcmV0dXJuIHN0dWRlbnRJZDtcbiAgICB9XG5cbiAgICBwdWJsaWMgZG91YmxlIGdldEdwYSgpIHtcbiAgICAgICAgcmV0dXJuIGdwYTtcbiAgICB9XG5cbiAgICAvLyBTZXR0ZXIgbWV0aG9kcyAoRW5jYXBzdWxhdGlvbilcbiAgICBwdWJsaWMgdm9pZCBzZXROYW1lKFN0cmluZyBuYW1lKSB7XG4gICAgICAgIHRoaXMubmFtZSA9IG5hbWU7XG4gICAgfVxuXG4gICAgcHVibGljIHZvaWQgc2V0QWdlKGludCBhZ2UpIHtcbiAgICAgICAgaWYgKGFnZSA+IDApIHtcbiAgICAgICAgICAgIHRoaXMuYWdlID0gYWdlO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgcHVibGljIHZvaWQgc2V0R3BhKGRvdWJsZSBncGEpIHtcbiAgICAgICAgaWYgKGdwYSA+PSAwLjAgJiYgZ3BhIDw9IDQuMCkge1xuICAgICAgICAgICAgdGhpcy5ncGEgPSBncGE7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBNZXRob2QgdG8gZGlzcGxheSBzdHVkZW50IGluZm9ybWF0aW9uXG4gICAgcHVibGljIHZvaWQgZGlzcGxheUluZm8oKSB7XG4gICAgICAgIFN5c3RlbS5vdXQucHJpbnRsbihcIlN0dWRlbnQgSW5mb3JtYXRpb246XCIpO1xuICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCJOYW1lOiBcIiArIG5hbWUpO1xuICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCJBZ2U6IFwiICsgYWdlKTtcbiAgICAgICAgU3lzdGVtLm91dC5wcmludGxuKFwiU3R1ZGVudCBJRDogXCIgKyBzdHVkZW50SWQpO1xuICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCJHUEE6IFwiICsgZ3BhKTtcbiAgICB9XG5cbiAgICAvLyBNZXRob2QgdG8gY2hlY2sgaWYgc3R1ZGVudCBpcyBvbiBob25vciByb2xsXG4gICAgcHVibGljIGJvb2xlYW4gaXNIb25vclJvbGwoKSB7XG4gICAgICAgIHJldHVybiBncGEgPj0gMy41O1xuICAgIH1cbn1cblxucHVibGljIGNsYXNzIE9PUERlbW8ge1xuICAgIHB1YmxpYyBzdGF0aWMgdm9pZCBtYWluKFN0cmluZ1tdIGFyZ3MpIHtcbiAgICAgICAgLy8gQ3JlYXRpbmcgb2JqZWN0cyB1c2luZyBkaWZmZXJlbnQgY29uc3RydWN0b3JzXG4gICAgICAgIFN0dWRlbnQgc3R1ZGVudDEgPSBuZXcgU3R1ZGVudCgpO1xuICAgICAgICBTdHVkZW50IHN0dWRlbnQyID0gbmV3IFN0dWRlbnQoXCJBbGljZSBKb2huc29uXCIsIDIwLCBcIlNUVTAwMVwiLCAzLjgpO1xuXG4gICAgICAgIFN5c3RlbS5vdXQucHJpbnRsbihcIj09PSBTdHVkZW50IDEgKERlZmF1bHQgQ29uc3RydWN0b3IpID09PVwiKTtcbiAgICAgICAgc3R1ZGVudDEuZGlzcGxheUluZm8oKTtcblxuICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCJcXFxcbj09PSBTdHVkZW50IDIgKFBhcmFtZXRlcml6ZWQgQ29uc3RydWN0b3IpID09PVwiKTtcbiAgICAgICAgc3R1ZGVudDIuZGlzcGxheUluZm8oKTtcblxuICAgICAgICAvLyBVc2luZyBzZXR0ZXIgbWV0aG9kcyB0byBtb2RpZnkgc3R1ZGVudDFcbiAgICAgICAgc3R1ZGVudDEuc2V0TmFtZShcIkJvYiBTbWl0aFwiKTtcbiAgICAgICAgc3R1ZGVudDEuc2V0QWdlKDE5KTtcbiAgICAgICAgc3R1ZGVudDEuc2V0R3BhKDMuMik7XG5cbiAgICAgICAgU3lzdGVtLm91dC5wcmludGxuKFwiXFxcXG49PT0gU3R1ZGVudCAxIChBZnRlciBtb2RpZmljYXRpb25zKSA9PT1cIik7XG4gICAgICAgIHN0dWRlbnQxLmRpc3BsYXlJbmZvKCk7XG5cbiAgICAgICAgLy8gVXNpbmcgbWV0aG9kc1xuICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCJcXFxcbj09PSBIb25vciBSb2xsIFN0YXR1cyA9PT1cIik7XG4gICAgICAgIFN5c3RlbS5vdXQucHJpbnRsbihzdHVkZW50MS5nZXROYW1lKCkgKyBcIiBob25vciByb2xsOiBcIiArIHN0dWRlbnQxLmlzSG9ub3JSb2xsKCkpO1xuICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oc3R1ZGVudDIuZ2V0TmFtZSgpICsgXCIgaG9ub3Igcm9sbDogXCIgKyBzdHVkZW50Mi5pc0hvbm9yUm9sbCgpKTtcbiAgICB9XG59YCxcbiAgICAgICAgICBleGVyY2lzZToge1xuICAgICAgICAgICAgZGVzY3JpcHRpb246IGBDcmVhdGUgYSBCb29rIGNsYXNzIHRoYXQgZGVtb25zdHJhdGVzIGFsbCBmb3VyIE9PUCB0b3BpY3M6IENsYXNzZXMsIE9iamVjdHMsIENvbnN0cnVjdG9ycywgYW5kIEVuY2Fwc3VsYXRpb24uXG5cbioqUmVxdWlyZW1lbnRzOioqXG4xLiAqKkNsYXNzZXMqKjogQ3JlYXRlIGEgQm9vayBjbGFzcyB3aXRoIHByaXZhdGUgZmllbGRzOiB0aXRsZSwgYXV0aG9yLCBwYWdlcywgcHJpY2VcbjIuICoqT2JqZWN0cyoqOiBDcmVhdGUgdHdvIEJvb2sgb2JqZWN0cyBpbiBtYWluIG1ldGhvZFxuMy4gKipDb25zdHJ1Y3RvcnMqKjogSW1wbGVtZW50IGJvdGggZGVmYXVsdCBhbmQgcGFyYW1ldGVyaXplZCBjb25zdHJ1Y3RvcnNcbjQuICoqRW5jYXBzdWxhdGlvbioqOiBDcmVhdGUgZ2V0dGVyIGFuZCBzZXR0ZXIgbWV0aG9kcyBmb3IgYWxsIGZpZWxkcywgcGx1cyBhIGRpc3BsYXlJbmZvKCkgbWV0aG9kXG5cbioqRXhwZWN0ZWQgb3V0cHV0IGZvcm1hdDoqKlxuPT09IEJvb2sgMSAoRGVmYXVsdCBDb25zdHJ1Y3RvcikgPT09XG5UaXRsZTogVW5rbm93blxuQXV0aG9yOiBVbmtub3duXG5QYWdlczogMFxuUHJpY2U6ICQwLjBcblxuPT09IEJvb2sgMiAoUGFyYW1ldGVyaXplZCBDb25zdHJ1Y3RvcikgPT09XG5UaXRsZTogSmF2YSBQcm9ncmFtbWluZ1xuQXV0aG9yOiBKb2huIERvZVxuUGFnZXM6IDUwMFxuUHJpY2U6ICQ0OS45OVxuXG49PT0gQm9vayAxIChBZnRlciBtb2RpZmljYXRpb25zKSA9PT1cblRpdGxlOiBQeXRob24gQmFzaWNzXG5BdXRob3I6IEphbmUgU21pdGhcblBhZ2VzOiAzMDBcblByaWNlOiAkMjkuOTlgLFxuICAgICAgICAgICAgc3RhcnRlckNvZGU6IGAvLyBUT0RPOiBDcmVhdGUgQm9vayBjbGFzcyBoZXJlXG5jbGFzcyBCb29rIHtcbiAgICAvLyBUT0RPOiAxLiBDbGFzc2VzIC0gQWRkIHByaXZhdGUgZmllbGRzOiB0aXRsZSwgYXV0aG9yLCBwYWdlcywgcHJpY2VcblxuXG4gICAgLy8gVE9ETzogMi4gQ29uc3RydWN0b3JzIC0gQ3JlYXRlIGRlZmF1bHQgY29uc3RydWN0b3JcblxuXG4gICAgLy8gVE9ETzogMi4gQ29uc3RydWN0b3JzIC0gQ3JlYXRlIHBhcmFtZXRlcml6ZWQgY29uc3RydWN0b3JcblxuXG4gICAgLy8gVE9ETzogMy4gRW5jYXBzdWxhdGlvbiAtIENyZWF0ZSBnZXR0ZXIgbWV0aG9kc1xuXG5cblxuXG5cbiAgICAvLyBUT0RPOiAzLiBFbmNhcHN1bGF0aW9uIC0gQ3JlYXRlIHNldHRlciBtZXRob2RzXG5cblxuXG5cblxuICAgIC8vIFRPRE86IENyZWF0ZSBkaXNwbGF5SW5mbygpIG1ldGhvZFxuXG59XG5cbnB1YmxpYyBjbGFzcyBCb29rRGVtbyB7XG4gICAgcHVibGljIHN0YXRpYyB2b2lkIG1haW4oU3RyaW5nW10gYXJncykge1xuICAgICAgICAvLyBUT0RPOiA0LiBPYmplY3RzIC0gQ3JlYXRlIHR3byBCb29rIG9iamVjdHNcbiAgICAgICAgLy8gYm9vazEgdXNpbmcgZGVmYXVsdCBjb25zdHJ1Y3RvclxuICAgICAgICAvLyBib29rMiB1c2luZyBwYXJhbWV0ZXJpemVkIGNvbnN0cnVjdG9yIHdpdGggdmFsdWVzOlxuICAgICAgICAvLyBcIkphdmEgUHJvZ3JhbW1pbmdcIiwgXCJKb2huIERvZVwiLCA1MDAsIDQ5Ljk5XG5cblxuICAgICAgICAvLyBUT0RPOiBEaXNwbGF5IGJvdGggYm9va3NcblxuXG4gICAgICAgIC8vIFRPRE86IE1vZGlmeSBib29rMSB1c2luZyBzZXR0ZXJzOlxuICAgICAgICAvLyB0aXRsZTogXCJQeXRob24gQmFzaWNzXCIsIGF1dGhvcjogXCJKYW5lIFNtaXRoXCIsIHBhZ2VzOiAzMDAsIHByaWNlOiAyOS45OVxuXG5cbiAgICAgICAgLy8gVE9ETzogRGlzcGxheSBib29rMSBhZ2FpblxuXG4gICAgfVxufWAsXG4gICAgICAgICAgICBleHBlY3RlZE91dHB1dDogYD09PSBCb29rIDEgKERlZmF1bHQgQ29uc3RydWN0b3IpID09PVxuVGl0bGU6IFVua25vd25cbkF1dGhvcjogVW5rbm93blxuUGFnZXM6IDBcblByaWNlOiAkMC4wXG5cbj09PSBCb29rIDIgKFBhcmFtZXRlcml6ZWQgQ29uc3RydWN0b3IpID09PVxuVGl0bGU6IEphdmEgUHJvZ3JhbW1pbmdcbkF1dGhvcjogSm9obiBEb2VcblBhZ2VzOiA1MDBcblByaWNlOiAkNDkuOTlcblxuPT09IEJvb2sgMSAoQWZ0ZXIgbW9kaWZpY2F0aW9ucykgPT09XG5UaXRsZTogUHl0aG9uIEJhc2ljc1xuQXV0aG9yOiBKYW5lIFNtaXRoXG5QYWdlczogMzAwXG5QcmljZTogJDI5Ljk5YCxcbiAgICAgICAgICAgIGhpbnRzOiBbXG4gICAgICAgICAgICAgIFwiVXNlIHByaXZhdGUgZmllbGRzIGFuZCBwdWJsaWMgbWV0aG9kcyBmb3IgZW5jYXBzdWxhdGlvblwiLFxuICAgICAgICAgICAgICBcIkRlZmF1bHQgY29uc3RydWN0b3Igc2hvdWxkIHNldCBkZWZhdWx0IHZhbHVlcyBsaWtlICdVbmtub3duJyBhbmQgMFwiLFxuICAgICAgICAgICAgICBcIlBhcmFtZXRlcml6ZWQgY29uc3RydWN0b3Igc2hvdWxkIGFjY2VwdCBhbGwgZm91ciBwYXJhbWV0ZXJzXCIsXG4gICAgICAgICAgICAgIFwiR2V0dGVyIG1ldGhvZHMgcmV0dXJuIGZpZWxkIHZhbHVlcywgc2V0dGVyIG1ldGhvZHMgdXBkYXRlIHRoZW1cIlxuICAgICAgICAgICAgXVxuICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgbmV4dExlc3NvbklkOiAnNScsXG4gICAgICAgIHByZXZMZXNzb25JZDogJzMnXG4gICAgICB9LFxuICAgICAgJzUnOiB7XG4gICAgICAgIGlkOiAnNScsXG4gICAgICAgIHRpdGxlOiAnQXJyYXlzIGFuZCBDb2xsZWN0aW9ucycsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnV29yayB3aXRoIGFycmF5cyBhbmQgSmF2YSBjb2xsZWN0aW9ucyBsaWtlIEFycmF5TGlzdCBhbmQgSGFzaE1hcC4nLFxuICAgICAgICBkaWZmaWN1bHR5OiAnSW50ZXJtZWRpYXRlJyxcbiAgICAgICAgZHVyYXRpb246ICc1MCBtaW4nLFxuICAgICAgICBjb250ZW50OiB7XG4gICAgICAgICAgdGhlb3J5OiBgXG4jIEFycmF5cyBhbmQgQ29sbGVjdGlvbnMgaW4gSmF2YVxuXG5BcnJheXMgYW5kIENvbGxlY3Rpb25zIGFyZSB1c2VkIHRvIHN0b3JlIG11bHRpcGxlIHZhbHVlcyBpbiBKYXZhLlxuXG4jIyBBcnJheXNcbkFycmF5cyBzdG9yZSBtdWx0aXBsZSB2YWx1ZXMgb2YgdGhlIHNhbWUgdHlwZSBpbiBhIGZpeGVkLXNpemUgc2VxdWVudGlhbCBjb2xsZWN0aW9uLlxuXG4jIyMgQXJyYXkgRGVjbGFyYXRpb24gYW5kIEluaXRpYWxpemF0aW9uOlxuXFxgXFxgXFxgamF2YVxuLy8gRGVjbGFyYXRpb25cbmludFtdIG51bWJlcnM7XG5TdHJpbmdbXSBuYW1lcztcblxuLy8gSW5pdGlhbGl6YXRpb25cbmludFtdIG51bWJlcnMgPSBuZXcgaW50WzVdOyAgLy8gQXJyYXkgb2YgNSBpbnRlZ2Vyc1xuU3RyaW5nW10gbmFtZXMgPSB7XCJBbGljZVwiLCBcIkJvYlwiLCBcIkNoYXJsaWVcIn07ICAvLyBBcnJheSB3aXRoIHZhbHVlc1xuXFxgXFxgXFxgXG5cbiMjIyBBY2Nlc3NpbmcgQXJyYXkgRWxlbWVudHM6XG5cXGBcXGBcXGBqYXZhXG5pbnRbXSBudW1iZXJzID0gezEwLCAyMCwgMzAsIDQwLCA1MH07XG5TeXN0ZW0ub3V0LnByaW50bG4obnVtYmVyc1swXSk7ICAvLyBQcmludHMgMTBcbm51bWJlcnNbMV0gPSAyNTsgIC8vIENoYW5nZXMgc2Vjb25kIGVsZW1lbnQgdG8gMjVcblxcYFxcYFxcYFxuXG4jIyMgQXJyYXkgUHJvcGVydGllczpcblxcYFxcYFxcYGphdmFcbmludFtdIG51bWJlcnMgPSB7MSwgMiwgMywgNCwgNX07XG5TeXN0ZW0ub3V0LnByaW50bG4obnVtYmVycy5sZW5ndGgpOyAgLy8gUHJpbnRzIDVcblxcYFxcYFxcYFxuXG4jIyBBcnJheUxpc3RcbkFycmF5TGlzdCBpcyBhIHJlc2l6YWJsZSBhcnJheSBpbXBsZW1lbnRhdGlvbiB0aGF0IGNhbiBncm93IGFuZCBzaHJpbmsgZHluYW1pY2FsbHkuXG5cbiMjIyBBcnJheUxpc3QgRGVjbGFyYXRpb24gYW5kIEluaXRpYWxpemF0aW9uOlxuXFxgXFxgXFxgamF2YVxuaW1wb3J0IGphdmEudXRpbC5BcnJheUxpc3Q7XG5cbkFycmF5TGlzdDxTdHJpbmc+IG5hbWVzID0gbmV3IEFycmF5TGlzdDw+KCk7XG5BcnJheUxpc3Q8SW50ZWdlcj4gbnVtYmVycyA9IG5ldyBBcnJheUxpc3Q8PigpO1xuXFxgXFxgXFxgXG5cbiMjIyBBcnJheUxpc3QgTWV0aG9kczpcblxcYFxcYFxcYGphdmFcbkFycmF5TGlzdDxTdHJpbmc+IGZydWl0cyA9IG5ldyBBcnJheUxpc3Q8PigpO1xuZnJ1aXRzLmFkZChcIkFwcGxlXCIpOyAgICAgICAgLy8gQWRkIGVsZW1lbnRcbmZydWl0cy5hZGQoXCJCYW5hbmFcIik7XG5mcnVpdHMuZ2V0KDApOyAgICAgICAgICAgICAvLyBHZXQgZWxlbWVudCBhdCBpbmRleCAwXG5mcnVpdHMuc2V0KDAsIFwiT3JhbmdlXCIpOyAgIC8vIFJlcGxhY2UgZWxlbWVudCBhdCBpbmRleCAwXG5mcnVpdHMucmVtb3ZlKDEpOyAgICAgICAgICAvLyBSZW1vdmUgZWxlbWVudCBhdCBpbmRleCAxXG5mcnVpdHMuc2l6ZSgpOyAgICAgICAgICAgICAvLyBHZXQgc2l6ZVxuXFxgXFxgXFxgXG5cbiMjIEhhc2hNYXBcbkhhc2hNYXAgc3RvcmVzIGtleS12YWx1ZSBwYWlycyBhbmQgYWxsb3dzIGZhc3QgbG9va3VwIGJ5IGtleS5cblxuIyMjIEhhc2hNYXAgRGVjbGFyYXRpb24gYW5kIFVzYWdlOlxuXFxgXFxgXFxgamF2YVxuaW1wb3J0IGphdmEudXRpbC5IYXNoTWFwO1xuXG5IYXNoTWFwPFN0cmluZywgSW50ZWdlcj4gYWdlcyA9IG5ldyBIYXNoTWFwPD4oKTtcbmFnZXMucHV0KFwiQWxpY2VcIiwgMjUpOyAgICAgLy8gQWRkIGtleS12YWx1ZSBwYWlyXG5hZ2VzLnB1dChcIkJvYlwiLCAzMCk7XG5hZ2VzLmdldChcIkFsaWNlXCIpOyAgICAgICAgIC8vIEdldCB2YWx1ZSBieSBrZXkgKHJldHVybnMgMjUpXG5hZ2VzLnJlbW92ZShcIkJvYlwiKTsgICAgICAgIC8vIFJlbW92ZSBrZXktdmFsdWUgcGFpclxuXFxgXFxgXFxgXG5cbiMjIEl0ZXJhdGlvblxuWW91IGNhbiBpdGVyYXRlIHRocm91Z2ggYXJyYXlzIGFuZCBjb2xsZWN0aW9ucyB1c2luZyBsb29wcy5cblxuIyMjIEZvci1lYWNoIExvb3A6XG5cXGBcXGBcXGBqYXZhXG4vLyBBcnJheXNcbmludFtdIG51bWJlcnMgPSB7MSwgMiwgMywgNCwgNX07XG5mb3IgKGludCBudW0gOiBudW1iZXJzKSB7XG4gICAgU3lzdGVtLm91dC5wcmludGxuKG51bSk7XG59XG5cbi8vIEFycmF5TGlzdFxuQXJyYXlMaXN0PFN0cmluZz4gbmFtZXMgPSBuZXcgQXJyYXlMaXN0PD4oKTtcbmZvciAoU3RyaW5nIG5hbWUgOiBuYW1lcykge1xuICAgIFN5c3RlbS5vdXQucHJpbnRsbihuYW1lKTtcbn1cblxcYFxcYFxcYFxuICAgICAgICAgIGAsXG4gICAgICAgICAgZXhhbXBsZTogYGltcG9ydCBqYXZhLnV0aWwuQXJyYXlMaXN0O1xuaW1wb3J0IGphdmEudXRpbC5IYXNoTWFwO1xuXG5wdWJsaWMgY2xhc3MgQXJyYXlzQ29sbGVjdGlvbnNEZW1vIHtcbiAgICBwdWJsaWMgc3RhdGljIHZvaWQgbWFpbihTdHJpbmdbXSBhcmdzKSB7XG5cbiAgICAgICAgLy8gPT09IEFSUkFZUyA9PT1cbiAgICAgICAgU3lzdGVtLm91dC5wcmludGxuKFwiPT09IEFycmF5cyBEZW1vID09PVwiKTtcblxuICAgICAgICAvLyBBcnJheSBkZWNsYXJhdGlvbiBhbmQgaW5pdGlhbGl6YXRpb25cbiAgICAgICAgaW50W10gc2NvcmVzID0gezg1LCA5MiwgNzgsIDk2LCA4OH07XG4gICAgICAgIFN0cmluZ1tdIHN1YmplY3RzID0gbmV3IFN0cmluZ1szXTtcbiAgICAgICAgc3ViamVjdHNbMF0gPSBcIk1hdGhcIjtcbiAgICAgICAgc3ViamVjdHNbMV0gPSBcIlNjaWVuY2VcIjtcbiAgICAgICAgc3ViamVjdHNbMl0gPSBcIkVuZ2xpc2hcIjtcblxuICAgICAgICAvLyBBY2Nlc3NpbmcgYW5kIG1vZGlmeWluZyBhcnJheXNcbiAgICAgICAgU3lzdGVtLm91dC5wcmludGxuKFwiRmlyc3Qgc2NvcmU6IFwiICsgc2NvcmVzWzBdKTtcbiAgICAgICAgU3lzdGVtLm91dC5wcmludGxuKFwiQXJyYXkgbGVuZ3RoOiBcIiArIHNjb3Jlcy5sZW5ndGgpO1xuICAgICAgICBzY29yZXNbMF0gPSA5MDsgIC8vIE1vZGlmeSBmaXJzdCBlbGVtZW50XG5cbiAgICAgICAgLy8gSXRlcmF0aW9uIHRocm91Z2ggYXJyYXlcbiAgICAgICAgU3lzdGVtLm91dC5wcmludGxuKFwiQWxsIHNjb3JlczpcIik7XG4gICAgICAgIGZvciAoaW50IHNjb3JlIDogc2NvcmVzKSB7XG4gICAgICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCJTY29yZTogXCIgKyBzY29yZSk7XG4gICAgICAgIH1cblxuICAgICAgICAvLyA9PT0gQVJSQVlMSVNUID09PVxuICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCJcXFxcbj09PSBBcnJheUxpc3QgRGVtbyA9PT1cIik7XG5cbiAgICAgICAgQXJyYXlMaXN0PFN0cmluZz4gc3R1ZGVudHMgPSBuZXcgQXJyYXlMaXN0PD4oKTtcblxuICAgICAgICAvLyBBZGRpbmcgZWxlbWVudHNcbiAgICAgICAgc3R1ZGVudHMuYWRkKFwiQWxpY2VcIik7XG4gICAgICAgIHN0dWRlbnRzLmFkZChcIkJvYlwiKTtcbiAgICAgICAgc3R1ZGVudHMuYWRkKFwiQ2hhcmxpZVwiKTtcbiAgICAgICAgc3R1ZGVudHMuYWRkKFwiRGlhbmFcIik7XG5cbiAgICAgICAgU3lzdGVtLm91dC5wcmludGxuKFwiTnVtYmVyIG9mIHN0dWRlbnRzOiBcIiArIHN0dWRlbnRzLnNpemUoKSk7XG4gICAgICAgIFN5c3RlbS5vdXQucHJpbnRsbihcIkZpcnN0IHN0dWRlbnQ6IFwiICsgc3R1ZGVudHMuZ2V0KDApKTtcblxuICAgICAgICAvLyBNb2RpZnlpbmcgQXJyYXlMaXN0XG4gICAgICAgIHN0dWRlbnRzLnNldCgxLCBcIlJvYmVydFwiKTsgIC8vIENoYW5nZSBCb2IgdG8gUm9iZXJ0XG4gICAgICAgIHN0dWRlbnRzLnJlbW92ZShcIkNoYXJsaWVcIik7IC8vIFJlbW92ZSBDaGFybGllXG5cbiAgICAgICAgLy8gSXRlcmF0aW9uIHRocm91Z2ggQXJyYXlMaXN0XG4gICAgICAgIFN5c3RlbS5vdXQucHJpbnRsbihcIkN1cnJlbnQgc3R1ZGVudHM6XCIpO1xuICAgICAgICBmb3IgKFN0cmluZyBzdHVkZW50IDogc3R1ZGVudHMpIHtcbiAgICAgICAgICAgIFN5c3RlbS5vdXQucHJpbnRsbihcIlN0dWRlbnQ6IFwiICsgc3R1ZGVudCk7XG4gICAgICAgIH1cblxuICAgICAgICAvLyA9PT0gSEFTSE1BUCA9PT1cbiAgICAgICAgU3lzdGVtLm91dC5wcmludGxuKFwiXFxcXG49PT0gSGFzaE1hcCBEZW1vID09PVwiKTtcblxuICAgICAgICBIYXNoTWFwPFN0cmluZywgSW50ZWdlcj4gc3R1ZGVudEdyYWRlcyA9IG5ldyBIYXNoTWFwPD4oKTtcblxuICAgICAgICAvLyBBZGRpbmcga2V5LXZhbHVlIHBhaXJzXG4gICAgICAgIHN0dWRlbnRHcmFkZXMucHV0KFwiQWxpY2VcIiwgOTUpO1xuICAgICAgICBzdHVkZW50R3JhZGVzLnB1dChcIlJvYmVydFwiLCA4Nyk7XG4gICAgICAgIHN0dWRlbnRHcmFkZXMucHV0KFwiRGlhbmFcIiwgOTIpO1xuXG4gICAgICAgIC8vIEFjY2Vzc2luZyB2YWx1ZXNcbiAgICAgICAgU3lzdGVtLm91dC5wcmludGxuKFwiQWxpY2UncyBncmFkZTogXCIgKyBzdHVkZW50R3JhZGVzLmdldChcIkFsaWNlXCIpKTtcbiAgICAgICAgU3lzdGVtLm91dC5wcmludGxuKFwiTnVtYmVyIG9mIGdyYWRlczogXCIgKyBzdHVkZW50R3JhZGVzLnNpemUoKSk7XG5cbiAgICAgICAgLy8gSXRlcmF0aW9uIHRocm91Z2ggSGFzaE1hcFxuICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCJBbGwgZ3JhZGVzOlwiKTtcbiAgICAgICAgZm9yIChTdHJpbmcgbmFtZSA6IHN0dWRlbnRHcmFkZXMua2V5U2V0KCkpIHtcbiAgICAgICAgICAgIFN5c3RlbS5vdXQucHJpbnRsbihuYW1lICsgXCI6IFwiICsgc3R1ZGVudEdyYWRlcy5nZXQobmFtZSkpO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gQ2hlY2sgaWYga2V5IGV4aXN0c1xuICAgICAgICBpZiAoc3R1ZGVudEdyYWRlcy5jb250YWluc0tleShcIkFsaWNlXCIpKSB7XG4gICAgICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCJBbGljZSdzIGdyYWRlIGZvdW5kIVwiKTtcbiAgICAgICAgfVxuICAgIH1cbn1gLFxuICAgICAgICAgIGV4ZXJjaXNlOiB7XG4gICAgICAgICAgICBkZXNjcmlwdGlvbjogYENyZWF0ZSBhIHByb2dyYW0gdGhhdCBkZW1vbnN0cmF0ZXMgYWxsIGZvdXIgY29sbGVjdGlvbiB0b3BpY3M6IEFycmF5cywgQXJyYXlMaXN0LCBIYXNoTWFwLCBhbmQgSXRlcmF0aW9uLlxuXG4qKlJlcXVpcmVtZW50czoqKlxuMS4gKipBcnJheXMqKjogQ3JlYXRlIGFuIGFycmF5IG9mIDUgaW50ZWdlcnMsIGRpc3BsYXkgdGhlbSwgYW5kIGNhbGN1bGF0ZSB0aGVpciBzdW1cbjIuICoqQXJyYXlMaXN0Kio6IENyZWF0ZSBhbiBBcnJheUxpc3Qgb2YgZnJ1aXRzLCBhZGQgNCBmcnVpdHMsIHJlbW92ZSBvbmUsIGFuZCBkaXNwbGF5IHRoZSBsaXN0XG4zLiAqKkhhc2hNYXAqKjogQ3JlYXRlIGEgSGFzaE1hcCBvZiBjb3VudHJpZXMgYW5kIHRoZWlyIGNhcGl0YWxzLCBhZGQgMyBwYWlycywgYW5kIGRpc3BsYXkgdGhlbVxuNC4gKipJdGVyYXRpb24qKjogVXNlIGZvci1lYWNoIGxvb3BzIHRvIGl0ZXJhdGUgdGhyb3VnaCBhbGwgY29sbGVjdGlvbnNcblxuKipFeHBlY3RlZCBvdXRwdXQgZm9ybWF0OioqXG49PT0gQXJyYXlzID09PVxuTnVtYmVyczogMTAgMjAgMzAgNDAgNTBcblN1bTogMTUwXG5cbj09PSBBcnJheUxpc3QgPT09XG5GcnVpdHM6IFtBcHBsZSwgQmFuYW5hLCBPcmFuZ2VdXG5cbj09PSBIYXNoTWFwID09PVxuQ291bnRyaWVzIGFuZCBDYXBpdGFsczpcblVTQTogV2FzaGluZ3RvbiBEQ1xuRnJhbmNlOiBQYXJpc1xuSmFwYW46IFRva3lvYCxcbiAgICAgICAgICAgIHN0YXJ0ZXJDb2RlOiBgaW1wb3J0IGphdmEudXRpbC5BcnJheUxpc3Q7XG5pbXBvcnQgamF2YS51dGlsLkhhc2hNYXA7XG5cbnB1YmxpYyBjbGFzcyBDb2xsZWN0aW9uc0V4ZXJjaXNlIHtcbiAgICBwdWJsaWMgc3RhdGljIHZvaWQgbWFpbihTdHJpbmdbXSBhcmdzKSB7XG5cbiAgICAgICAgLy8gVE9ETzogMS4gQXJyYXlzIC0gQ3JlYXRlIGFycmF5IHdpdGggdmFsdWVzIHsxMCwgMjAsIDMwLCA0MCwgNTB9XG4gICAgICAgIFN5c3RlbS5vdXQucHJpbnRsbihcIj09PSBBcnJheXMgPT09XCIpO1xuXG5cbiAgICAgICAgLy8gVE9ETzogRGlzcGxheSBhcnJheSBlbGVtZW50cyBhbmQgY2FsY3VsYXRlIHN1bSB1c2luZyBpdGVyYXRpb25cblxuXG4gICAgICAgIC8vIFRPRE86IDIuIEFycmF5TGlzdCAtIENyZWF0ZSBBcnJheUxpc3Qgb2YgZnJ1aXRzXG4gICAgICAgIFN5c3RlbS5vdXQucHJpbnRsbihcIlxcXFxuPT09IEFycmF5TGlzdCA9PT1cIik7XG5cblxuICAgICAgICAvLyBUT0RPOiBBZGQgZnJ1aXRzOiBcIkFwcGxlXCIsIFwiQmFuYW5hXCIsIFwiQ2hlcnJ5XCIsIFwiT3JhbmdlXCJcbiAgICAgICAgLy8gVE9ETzogUmVtb3ZlIFwiQ2hlcnJ5XCJcbiAgICAgICAgLy8gVE9ETzogRGlzcGxheSByZW1haW5pbmcgZnJ1aXRzXG5cblxuICAgICAgICAvLyBUT0RPOiAzLiBIYXNoTWFwIC0gQ3JlYXRlIEhhc2hNYXAgb2YgY291bnRyaWVzIGFuZCBjYXBpdGFsc1xuICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCJcXFxcbj09PSBIYXNoTWFwID09PVwiKTtcblxuXG4gICAgICAgIC8vIFRPRE86IEFkZCBwYWlyczogXCJVU0FcIi0+XCJXYXNoaW5ndG9uIERDXCIsIFwiRnJhbmNlXCItPlwiUGFyaXNcIiwgXCJKYXBhblwiLT5cIlRva3lvXCJcbiAgICAgICAgLy8gVE9ETzogRGlzcGxheSBhbGwgcGFpcnMgdXNpbmcgaXRlcmF0aW9uXG5cbiAgICB9XG59YCxcbiAgICAgICAgICAgIGV4cGVjdGVkT3V0cHV0OiBgPT09IEFycmF5cyA9PT1cbk51bWJlcnM6IDEwIDIwIDMwIDQwIDUwXG5TdW06IDE1MFxuXG49PT0gQXJyYXlMaXN0ID09PVxuRnJ1aXRzOiBbQXBwbGUsIEJhbmFuYSwgT3JhbmdlXVxuXG49PT0gSGFzaE1hcCA9PT1cbkNvdW50cmllcyBhbmQgQ2FwaXRhbHM6XG5VU0E6IFdhc2hpbmd0b24gRENcbkZyYW5jZTogUGFyaXNcbkphcGFuOiBUb2t5b2AsXG4gICAgICAgICAgICBoaW50czogW1xuICAgICAgICAgICAgICBcIlVzZSBmb3ItZWFjaCBsb29wOiBmb3IoaW50IG51bSA6IGFycmF5KSB0byBpdGVyYXRlIGFycmF5c1wiLFxuICAgICAgICAgICAgICBcIkFycmF5TGlzdCBtZXRob2RzOiBhZGQoKSwgcmVtb3ZlKCksIHRvU3RyaW5nKCkgZm9yIGRpc3BsYXlcIixcbiAgICAgICAgICAgICAgXCJIYXNoTWFwIG1ldGhvZHM6IHB1dCgpLCBrZXlTZXQoKSBmb3IgaXRlcmF0aW9uXCIsXG4gICAgICAgICAgICAgIFwiQ2FsY3VsYXRlIHN1bSBieSBhZGRpbmcgZWFjaCBhcnJheSBlbGVtZW50IGluIHRoZSBsb29wXCJcbiAgICAgICAgICAgIF1cbiAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIG5leHRMZXNzb25JZDogJzYnLFxuICAgICAgICBwcmV2TGVzc29uSWQ6ICc0J1xuICAgICAgfSxcbiAgICAgICc2Jzoge1xuICAgICAgICBpZDogJzYnLFxuICAgICAgICB0aXRsZTogJ0V4Y2VwdGlvbiBIYW5kbGluZycsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnTGVhcm4gdG8gaGFuZGxlIGVycm9ycyBncmFjZWZ1bGx5IHdpdGggdHJ5LWNhdGNoIGJsb2NrcyBhbmQgY3VzdG9tIGV4Y2VwdGlvbnMuJyxcbiAgICAgICAgZGlmZmljdWx0eTogJ0FkdmFuY2VkJyxcbiAgICAgICAgZHVyYXRpb246ICc0NSBtaW4nLFxuICAgICAgICBjb250ZW50OiB7XG4gICAgICAgICAgdGhlb3J5OiBgXG4jIEV4Y2VwdGlvbiBIYW5kbGluZyBpbiBKYXZhXG5cbkV4Y2VwdGlvbiBoYW5kbGluZyBhbGxvd3MgeW91IHRvIG1hbmFnZSBydW50aW1lIGVycm9ycyBncmFjZWZ1bGx5IGFuZCBwcmV2ZW50IHlvdXIgcHJvZ3JhbSBmcm9tIGNyYXNoaW5nLlxuXG4jIyBUcnktQ2F0Y2ggQmxvY2tzXG5UaGUgYmFzaWMgc3RydWN0dXJlIGZvciBoYW5kbGluZyBleGNlcHRpb25zOlxuXG5cXGBcXGBcXGBqYXZhXG50cnkge1xuICAgIC8vIENvZGUgdGhhdCBtaWdodCB0aHJvdyBhbiBleGNlcHRpb25cbn0gY2F0Y2ggKEV4Y2VwdGlvblR5cGUgZSkge1xuICAgIC8vIEhhbmRsZSB0aGUgZXhjZXB0aW9uXG59XG5cXGBcXGBcXGBcblxuIyMjIEV4YW1wbGU6XG5cXGBcXGBcXGBqYXZhXG50cnkge1xuICAgIGludCByZXN1bHQgPSAxMCAvIDA7ICAvLyBUaGlzIHdpbGwgdGhyb3cgQXJpdGhtZXRpY0V4Y2VwdGlvblxufSBjYXRjaCAoQXJpdGhtZXRpY0V4Y2VwdGlvbiBlKSB7XG4gICAgU3lzdGVtLm91dC5wcmludGxuKFwiQ2Fubm90IGRpdmlkZSBieSB6ZXJvIVwiKTtcbn1cblxcYFxcYFxcYFxuXG4jIyBGaW5hbGx5IEJsb2NrXG5UaGUgZmluYWxseSBibG9jayBhbHdheXMgZXhlY3V0ZXMsIHdoZXRoZXIgYW4gZXhjZXB0aW9uIG9jY3VycyBvciBub3Q6XG5cblxcYFxcYFxcYGphdmFcbnRyeSB7XG4gICAgLy8gcmlza3kgY29kZVxufSBjYXRjaCAoRXhjZXB0aW9uIGUpIHtcbiAgICAvLyBoYW5kbGUgZXhjZXB0aW9uXG59IGZpbmFsbHkge1xuICAgIC8vIGNsZWFudXAgY29kZSAtIGFsd2F5cyBydW5zXG4gICAgU3lzdGVtLm91dC5wcmludGxuKFwiQ2xlYW51cCBjb21wbGV0ZWRcIik7XG59XG5cXGBcXGBcXGBcblxuIyMgQ3VzdG9tIEV4Y2VwdGlvbnNcbllvdSBjYW4gY3JlYXRlIHlvdXIgb3duIGV4Y2VwdGlvbiBjbGFzc2VzOlxuXG5cXGBcXGBcXGBqYXZhXG5jbGFzcyBDdXN0b21FeGNlcHRpb24gZXh0ZW5kcyBFeGNlcHRpb24ge1xuICAgIHB1YmxpYyBDdXN0b21FeGNlcHRpb24oU3RyaW5nIG1lc3NhZ2UpIHtcbiAgICAgICAgc3VwZXIobWVzc2FnZSk7XG4gICAgfVxufVxuXFxgXFxgXFxgXG5cbiMjIFRocm93cyBLZXl3b3JkXG5Vc2UgdGhyb3dzIHRvIGRlY2xhcmUgdGhhdCBhIG1ldGhvZCBtaWdodCB0aHJvdyBhbiBleGNlcHRpb246XG5cblxcYFxcYFxcYGphdmFcbnB1YmxpYyB2b2lkIHJpc2t5TWV0aG9kKCkgdGhyb3dzIElPRXhjZXB0aW9uIHtcbiAgICAvLyBjb2RlIHRoYXQgbWlnaHQgdGhyb3cgSU9FeGNlcHRpb25cbn1cblxcYFxcYFxcYFxuXG4jIyMgQ2FsbGluZyBtZXRob2RzIHRoYXQgdGhyb3cgZXhjZXB0aW9uczpcblxcYFxcYFxcYGphdmFcbnRyeSB7XG4gICAgcmlza3lNZXRob2QoKTtcbn0gY2F0Y2ggKElPRXhjZXB0aW9uIGUpIHtcbiAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCJJTyBFcnJvcjogXCIgKyBlLmdldE1lc3NhZ2UoKSk7XG59XG5cXGBcXGBcXGBcblxuIyMgQ29tbW9uIEV4Y2VwdGlvbiBUeXBlc1xuLSAqKkFyaXRobWV0aWNFeGNlcHRpb24qKjogRGl2aXNpb24gYnkgemVyb1xuLSAqKk51bGxQb2ludGVyRXhjZXB0aW9uKio6IFVzaW5nIG51bGwgcmVmZXJlbmNlXG4tICoqQXJyYXlJbmRleE91dE9mQm91bmRzRXhjZXB0aW9uKio6IEludmFsaWQgYXJyYXkgaW5kZXhcbi0gKipOdW1iZXJGb3JtYXRFeGNlcHRpb24qKjogSW52YWxpZCBudW1iZXIgY29udmVyc2lvblxuLSAqKklPRXhjZXB0aW9uKio6IElucHV0L291dHB1dCBvcGVyYXRpb25zXG4tICoqRmlsZU5vdEZvdW5kRXhjZXB0aW9uKio6IEZpbGUgbm90IGZvdW5kXG5cbiMjIEJlc3QgUHJhY3RpY2VzXG4xLiBDYXRjaCBzcGVjaWZpYyBleGNlcHRpb25zIHJhdGhlciB0aGFuIGdlbmVyaWMgRXhjZXB0aW9uXG4yLiBBbHdheXMgY2xlYW4gdXAgcmVzb3VyY2VzIGluIGZpbmFsbHkgYmxvY2tcbjMuIERvbid0IGlnbm9yZSBleGNlcHRpb25zIC0gYXQgbGVhc3QgbG9nIHRoZW1cbjQuIFVzZSBtZWFuaW5nZnVsIGVycm9yIG1lc3NhZ2VzXG4gICAgICAgICAgYCxcbiAgICAgICAgICBleGFtcGxlOiBgLy8gQ3VzdG9tIEV4Y2VwdGlvbiBjbGFzc1xuY2xhc3MgSW52YWxpZEFnZUV4Y2VwdGlvbiBleHRlbmRzIEV4Y2VwdGlvbiB7XG4gICAgcHVibGljIEludmFsaWRBZ2VFeGNlcHRpb24oU3RyaW5nIG1lc3NhZ2UpIHtcbiAgICAgICAgc3VwZXIobWVzc2FnZSk7XG4gICAgfVxufVxuXG5wdWJsaWMgY2xhc3MgRXhjZXB0aW9uSGFuZGxpbmdEZW1vIHtcblxuICAgIC8vIE1ldGhvZCB0aGF0IHRocm93cyBjdXN0b20gZXhjZXB0aW9uXG4gICAgcHVibGljIHN0YXRpYyB2b2lkIHZhbGlkYXRlQWdlKGludCBhZ2UpIHRocm93cyBJbnZhbGlkQWdlRXhjZXB0aW9uIHtcbiAgICAgICAgaWYgKGFnZSA8IDAgfHwgYWdlID4gMTUwKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgSW52YWxpZEFnZUV4Y2VwdGlvbihcIkFnZSBtdXN0IGJlIGJldHdlZW4gMCBhbmQgMTUwLiBHb3Q6IFwiICsgYWdlKTtcbiAgICAgICAgfVxuICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCJWYWxpZCBhZ2U6IFwiICsgYWdlKTtcbiAgICB9XG5cbiAgICAvLyBNZXRob2QgZGVtb25zdHJhdGluZyBkaWZmZXJlbnQgZXhjZXB0aW9uIHR5cGVzXG4gICAgcHVibGljIHN0YXRpYyB2b2lkIGRlbW9uc3RyYXRlRXhjZXB0aW9ucygpIHtcblxuICAgICAgICAvLyBUcnktQ2F0Y2ggd2l0aCBBcml0aG1ldGljRXhjZXB0aW9uXG4gICAgICAgIFN5c3RlbS5vdXQucHJpbnRsbihcIj09PSBBcml0aG1ldGljRXhjZXB0aW9uIERlbW8gPT09XCIpO1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgaW50IHJlc3VsdCA9IDEwIC8gMDtcbiAgICAgICAgICAgIFN5c3RlbS5vdXQucHJpbnRsbihcIlJlc3VsdDogXCIgKyByZXN1bHQpO1xuICAgICAgICB9IGNhdGNoIChBcml0aG1ldGljRXhjZXB0aW9uIGUpIHtcbiAgICAgICAgICAgIFN5c3RlbS5vdXQucHJpbnRsbihcIkVycm9yOiBDYW5ub3QgZGl2aWRlIGJ5IHplcm8hXCIpO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gVHJ5LUNhdGNoIHdpdGggQXJyYXlJbmRleE91dE9mQm91bmRzRXhjZXB0aW9uXG4gICAgICAgIFN5c3RlbS5vdXQucHJpbnRsbihcIlxcXFxuPT09IEFycmF5SW5kZXhPdXRPZkJvdW5kc0V4Y2VwdGlvbiBEZW1vID09PVwiKTtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGludFtdIG51bWJlcnMgPSB7MSwgMiwgM307XG4gICAgICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCJFbGVtZW50IGF0IGluZGV4IDU6IFwiICsgbnVtYmVyc1s1XSk7XG4gICAgICAgIH0gY2F0Y2ggKEFycmF5SW5kZXhPdXRPZkJvdW5kc0V4Y2VwdGlvbiBlKSB7XG4gICAgICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCJFcnJvcjogQXJyYXkgaW5kZXggb3V0IG9mIGJvdW5kcyFcIik7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBUcnktQ2F0Y2ggd2l0aCBOdW1iZXJGb3JtYXRFeGNlcHRpb25cbiAgICAgICAgU3lzdGVtLm91dC5wcmludGxuKFwiXFxcXG49PT0gTnVtYmVyRm9ybWF0RXhjZXB0aW9uIERlbW8gPT09XCIpO1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgU3RyaW5nIHRleHQgPSBcImFiY1wiO1xuICAgICAgICAgICAgaW50IG51bWJlciA9IEludGVnZXIucGFyc2VJbnQodGV4dCk7XG4gICAgICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCJOdW1iZXI6IFwiICsgbnVtYmVyKTtcbiAgICAgICAgfSBjYXRjaCAoTnVtYmVyRm9ybWF0RXhjZXB0aW9uIGUpIHtcbiAgICAgICAgICAgIFN5c3RlbS5vdXQucHJpbnRsbihcIkVycm9yOiBDYW5ub3QgY29udmVydCAnXCIgKyBlLmdldE1lc3NhZ2UoKS5zcGxpdChcIlxcXCJcIilbMV0gKyBcIicgdG8gbnVtYmVyIVwiKTtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIHB1YmxpYyBzdGF0aWMgdm9pZCBtYWluKFN0cmluZ1tdIGFyZ3MpIHtcblxuICAgICAgICAvLyBEZW1vbnN0cmF0ZSBjb21tb24gZXhjZXB0aW9uc1xuICAgICAgICBkZW1vbnN0cmF0ZUV4Y2VwdGlvbnMoKTtcblxuICAgICAgICAvLyBDdXN0b20gRXhjZXB0aW9uIHdpdGggVGhyb3dzXG4gICAgICAgIFN5c3RlbS5vdXQucHJpbnRsbihcIlxcXFxuPT09IEN1c3RvbSBFeGNlcHRpb24gRGVtbyA9PT1cIik7XG5cbiAgICAgICAgaW50W10gdGVzdEFnZXMgPSB7MjUsIC01LCAyMDAsIDMwfTtcblxuICAgICAgICBmb3IgKGludCBhZ2UgOiB0ZXN0QWdlcykge1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICB2YWxpZGF0ZUFnZShhZ2UpO1xuICAgICAgICAgICAgfSBjYXRjaCAoSW52YWxpZEFnZUV4Y2VwdGlvbiBlKSB7XG4gICAgICAgICAgICAgICAgU3lzdGVtLm91dC5wcmludGxuKFwiQ3VzdG9tIEVycm9yOiBcIiArIGUuZ2V0TWVzc2FnZSgpKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIC8vIEZpbmFsbHkgQmxvY2sgRGVtb1xuICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCJcXFxcbj09PSBGaW5hbGx5IEJsb2NrIERlbW8gPT09XCIpO1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgU3lzdGVtLm91dC5wcmludGxuKFwiRXhlY3V0aW5nIHJpc2t5IG9wZXJhdGlvbi4uLlwiKTtcbiAgICAgICAgICAgIGludCByZXN1bHQgPSAxMCAvIDI7ICAvLyBUaGlzIHdvcmtzIGZpbmVcbiAgICAgICAgICAgIFN5c3RlbS5vdXQucHJpbnRsbihcIlJlc3VsdDogXCIgKyByZXN1bHQpO1xuICAgICAgICB9IGNhdGNoIChFeGNlcHRpb24gZSkge1xuICAgICAgICAgICAgU3lzdGVtLm91dC5wcmludGxuKFwiRXhjZXB0aW9uIGNhdWdodDogXCIgKyBlLmdldE1lc3NhZ2UoKSk7XG4gICAgICAgIH0gZmluYWxseSB7XG4gICAgICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCJGaW5hbGx5IGJsb2NrOiBDbGVhbnVwIGNvbXBsZXRlZCFcIik7XG4gICAgICAgIH1cblxuICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCJcXFxcblByb2dyYW0gY29tcGxldGVkIHN1Y2Nlc3NmdWxseSFcIik7XG4gICAgfVxufWAsXG4gICAgICAgICAgZXhlcmNpc2U6IHtcbiAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBgQ3JlYXRlIGEgcHJvZ3JhbSB0aGF0IGRlbW9uc3RyYXRlcyBhbGwgZm91ciBleGNlcHRpb24gaGFuZGxpbmcgdG9waWNzOiBUcnktQ2F0Y2gsIEZpbmFsbHkgQmxvY2ssIEN1c3RvbSBFeGNlcHRpb25zLCBhbmQgVGhyb3dzLlxuXG4qKlJlcXVpcmVtZW50czoqKlxuMS4gKipUcnktQ2F0Y2gqKjogSGFuZGxlIGRpdmlzaW9uIGJ5IHplcm8gYW5kIGFycmF5IGluZGV4IG91dCBvZiBib3VuZHNcbjIuICoqRmluYWxseSBCbG9jayoqOiBVc2UgZmluYWxseSB0byBwcmludCBjbGVhbnVwIG1lc3NhZ2VcbjMuICoqQ3VzdG9tIEV4Y2VwdGlvbnMqKjogQ3JlYXRlIEludmFsaWRTY29yZUV4Y2VwdGlvbiBmb3Igc2NvcmVzIG91dHNpZGUgMC0xMDAgcmFuZ2VcbjQuICoqVGhyb3dzKio6IENyZWF0ZSBhIG1ldGhvZCB0aGF0IHRocm93cyB0aGUgY3VzdG9tIGV4Y2VwdGlvblxuXG4qKkV4cGVjdGVkIG91dHB1dCBmb3JtYXQ6Kipcbj09PSBUcnktQ2F0Y2ggRGVtbyA9PT1cbkVycm9yOiBEaXZpc2lvbiBieSB6ZXJvIVxuRXJyb3I6IEFycmF5IGluZGV4IG91dCBvZiBib3VuZHMhXG5cbj09PSBDdXN0b20gRXhjZXB0aW9uIERlbW8gPT09XG5WYWxpZCBzY29yZTogODVcbkVycm9yOiBTY29yZSBtdXN0IGJlIGJldHdlZW4gMCBhbmQgMTAwLiBHb3Q6IDE1MFxuXG49PT0gRmluYWxseSBCbG9jayBEZW1vID09PVxuUHJvY2Vzc2luZy4uLlxuRmluYWxseTogQ2xlYW51cCBjb21wbGV0ZWQhYCxcbiAgICAgICAgICAgIHN0YXJ0ZXJDb2RlOiBgLy8gVE9ETzogMS4gQ3VzdG9tIEV4Y2VwdGlvbnMgLSBDcmVhdGUgSW52YWxpZFNjb3JlRXhjZXB0aW9uIGNsYXNzXG5cblxucHVibGljIGNsYXNzIEV4Y2VwdGlvbkV4ZXJjaXNlIHtcblxuICAgIC8vIFRPRE86IDIuIFRocm93cyAtIENyZWF0ZSB2YWxpZGF0ZVNjb3JlIG1ldGhvZCB0aGF0IHRocm93cyBJbnZhbGlkU2NvcmVFeGNlcHRpb25cbiAgICAvLyBNZXRob2Qgc2hvdWxkIGFjY2VwdCBpbnQgc2NvcmUgYW5kIHRocm93IGV4Y2VwdGlvbiBpZiBzY29yZSA8IDAgb3Igc2NvcmUgPiAxMDBcblxuXG4gICAgcHVibGljIHN0YXRpYyB2b2lkIG1haW4oU3RyaW5nW10gYXJncykge1xuXG4gICAgICAgIFN5c3RlbS5vdXQucHJpbnRsbihcIj09PSBUcnktQ2F0Y2ggRGVtbyA9PT1cIik7XG5cbiAgICAgICAgLy8gVE9ETzogMy4gVHJ5LUNhdGNoIC0gSGFuZGxlIGRpdmlzaW9uIGJ5IHplcm9cbiAgICAgICAgdHJ5IHtcblxuICAgICAgICB9IGNhdGNoICgpIHtcblxuICAgICAgICB9XG5cbiAgICAgICAgLy8gVE9ETzogMy4gVHJ5LUNhdGNoIC0gSGFuZGxlIGFycmF5IGluZGV4IG91dCBvZiBib3VuZHNcbiAgICAgICAgLy8gQ3JlYXRlIGFycmF5IHsxLCAyLCAzfSBhbmQgdHJ5IHRvIGFjY2VzcyBpbmRleCA1XG4gICAgICAgIHRyeSB7XG5cbiAgICAgICAgfSBjYXRjaCAoKSB7XG5cbiAgICAgICAgfVxuXG4gICAgICAgIFN5c3RlbS5vdXQucHJpbnRsbihcIlxcXFxuPT09IEN1c3RvbSBFeGNlcHRpb24gRGVtbyA9PT1cIik7XG5cbiAgICAgICAgLy8gVE9ETzogVGVzdCB2YWxpZGF0ZVNjb3JlIHdpdGggdmFsdWVzIDg1IGFuZCAxNTBcblxuXG4gICAgICAgIFN5c3RlbS5vdXQucHJpbnRsbihcIlxcXFxuPT09IEZpbmFsbHkgQmxvY2sgRGVtbyA9PT1cIik7XG5cbiAgICAgICAgLy8gVE9ETzogNC4gRmluYWxseSBCbG9jayAtIFVzZSB0cnktY2F0Y2gtZmluYWxseVxuICAgICAgICB0cnkge1xuICAgICAgICAgICAgU3lzdGVtLm91dC5wcmludGxuKFwiUHJvY2Vzc2luZy4uLlwiKTtcbiAgICAgICAgICAgIC8vIFNvbWUgb3BlcmF0aW9uXG4gICAgICAgIH0gY2F0Y2ggKEV4Y2VwdGlvbiBlKSB7XG4gICAgICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCJFcnJvciBvY2N1cnJlZFwiKTtcbiAgICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgICAgIC8vIFRPRE86IFByaW50IGNsZWFudXAgbWVzc2FnZVxuICAgICAgICB9XG4gICAgfVxufWAsXG4gICAgICAgICAgICBleHBlY3RlZE91dHB1dDogYD09PSBUcnktQ2F0Y2ggRGVtbyA9PT1cbkVycm9yOiBEaXZpc2lvbiBieSB6ZXJvIVxuRXJyb3I6IEFycmF5IGluZGV4IG91dCBvZiBib3VuZHMhXG5cbj09PSBDdXN0b20gRXhjZXB0aW9uIERlbW8gPT09XG5WYWxpZCBzY29yZTogODVcbkVycm9yOiBTY29yZSBtdXN0IGJlIGJldHdlZW4gMCBhbmQgMTAwLiBHb3Q6IDE1MFxuXG49PT0gRmluYWxseSBCbG9jayBEZW1vID09PVxuUHJvY2Vzc2luZy4uLlxuRmluYWxseTogQ2xlYW51cCBjb21wbGV0ZWQhYCxcbiAgICAgICAgICAgIGhpbnRzOiBbXG4gICAgICAgICAgICAgIFwiQ3VzdG9tIGV4Y2VwdGlvbjogY2xhc3MgSW52YWxpZFNjb3JlRXhjZXB0aW9uIGV4dGVuZHMgRXhjZXB0aW9uXCIsXG4gICAgICAgICAgICAgIFwiVGhyb3dzOiBwdWJsaWMgc3RhdGljIHZvaWQgdmFsaWRhdGVTY29yZShpbnQgc2NvcmUpIHRocm93cyBJbnZhbGlkU2NvcmVFeGNlcHRpb25cIixcbiAgICAgICAgICAgICAgXCJUcnktY2F0Y2g6IGNhdGNoIChBcml0aG1ldGljRXhjZXB0aW9uIGUpIGFuZCBjYXRjaCAoQXJyYXlJbmRleE91dE9mQm91bmRzRXhjZXB0aW9uIGUpXCIsXG4gICAgICAgICAgICAgIFwiRmluYWxseSBibG9jayBhbHdheXMgZXhlY3V0ZXMgYWZ0ZXIgdHJ5LWNhdGNoXCJcbiAgICAgICAgICAgIF1cbiAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIG5leHRMZXNzb25JZDogdW5kZWZpbmVkLFxuICAgICAgICBwcmV2TGVzc29uSWQ6ICc1J1xuICAgICAgfVxuICAgIH1cblxuICAgIHJldHVybiBsZXNzb25zW2lkXSB8fCBsZXNzb25zWycxJ10gLy8gRGVmYXVsdCB0byBsZXNzb24gMSBpZiBub3QgZm91bmRcbiAgfVxuXG4gIGNvbnN0IG1vY2tMZXNzb24gPSBnZXRMZXNzb25EYXRhKHBhcmFtcy5pZCBhcyBzdHJpbmcpXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBTaW11bGF0ZSBsb2FkaW5nIGxlc3NvbiBkYXRhXG4gICAgc2V0TGVzc29uKG1vY2tMZXNzb24pXG4gICAgc2V0VXNlckNvZGUobW9ja0xlc3Nvbi5jb250ZW50LmV4ZXJjaXNlLnN0YXJ0ZXJDb2RlKVxuICAgIFxuICAgIC8vIENhbGN1bGF0ZSBwcm9ncmVzcyBiYXNlZCBvbiBjdXJyZW50IHRhYlxuICAgIGNvbnN0IHRhYlByb2dyZXNzID0ge1xuICAgICAgdGhlb3J5OiAzMyxcbiAgICAgIGV4YW1wbGU6IDY2LFxuICAgICAgZXhlcmNpc2U6IDEwMFxuICAgIH1cbiAgICBzZXRQcm9ncmVzcyh0YWJQcm9ncmVzc1tjdXJyZW50VGFiXSlcbiAgfSwgW2N1cnJlbnRUYWJdKVxuXG4gIGNvbnN0IGhhbmRsZUNvZGVDaGFuZ2UgPSAoY29kZTogc3RyaW5nKSA9PiB7XG4gICAgc2V0VXNlckNvZGUoY29kZSlcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVJ1bkNvZGUgPSBhc3luYyAoY29kZTogc3RyaW5nKSA9PiB7XG4gICAgLy8gU2ltdWxhdGUgY29kZSBleGVjdXRpb25cbiAgICB0cnkge1xuICAgICAgLy8gSW4gYSByZWFsIGFwcCwgdGhpcyB3b3VsZCBzZW5kIHRoZSBjb2RlIHRvIGEgYmFja2VuZCBzZXJ2aWNlXG4gICAgICBjb25zdCBtb2NrT3V0cHV0ID0gYFN0dWRlbnQ6IEFsaWNlIEpvaG5zb25cbkFnZTogMjBcbkdQQTogMy44XG5FbnJvbGxlZDogdHJ1ZWBcbiAgICAgIFxuICAgICAgLy8gQ2hlY2sgaWYgdGhlIG91dHB1dCBtYXRjaGVzIGV4cGVjdGVkIG91dHB1dFxuICAgICAgaWYgKGxlc3NvbiAmJiBtb2NrT3V0cHV0LnRyaW0oKSA9PT0gbGVzc29uLmNvbnRlbnQuZXhlcmNpc2UuZXhwZWN0ZWRPdXRwdXQudHJpbSgpKSB7XG4gICAgICAgIHNldElzQ29tcGxldGVkKHRydWUpXG4gICAgICAgIHRvYXN0LnN1Y2Nlc3MoJ0NvbmdyYXR1bGF0aW9ucyEgRXhlcmNpc2UgY29tcGxldGVkIHN1Y2Nlc3NmdWxseSEnKVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdG9hc3QuZXJyb3IoJ091dHB1dCBkb2VzblxcJ3QgbWF0Y2ggZXhwZWN0ZWQgcmVzdWx0LiBLZWVwIHRyeWluZyEnKVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICB0b2FzdC5lcnJvcignRXJyb3IgZXhlY3V0aW5nIGNvZGUnKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGhhbmRsZU5leHRMZXNzb24gPSAoKSA9PiB7XG4gICAgaWYgKGxlc3Nvbj8ubmV4dExlc3NvbklkKSB7XG4gICAgICByb3V0ZXIucHVzaChgL2xlc3NvbnMvJHtsZXNzb24ubmV4dExlc3NvbklkfWApXG4gICAgfVxuICB9XG5cbiAgY29uc3QgaGFuZGxlUHJldkxlc3NvbiA9ICgpID0+IHtcbiAgICBpZiAobGVzc29uPy5wcmV2TGVzc29uSWQpIHtcbiAgICAgIHJvdXRlci5wdXNoKGAvbGVzc29ucy8ke2xlc3Nvbi5wcmV2TGVzc29uSWR9YClcbiAgICB9XG4gIH1cblxuICBpZiAoIWxlc3Nvbikge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmF5LTUwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTEyIHctMTIgYm9yZGVyLWItMiBib3JkZXItcHJpbWFyeS02MDBcIj48L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIClcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MFwiPlxuICAgICAgey8qIE5hdmlnYXRpb24gKi99XG4gICAgICA8bmF2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHNoYWRvdy1zbSBib3JkZXItYlwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgaC0xNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2Rhc2hib2FyZFwiIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHRleHQtZ3JheS02MDAgaG92ZXI6dGV4dC1wcmltYXJ5LTYwMCB0cmFuc2l0aW9uLWNvbG9ycyBtci00XCI+XG4gICAgICAgICAgICAgICAgPEFycm93TGVmdCBjbGFzc05hbWU9XCJ3LTUgaC01IG1yLTFcIiAvPlxuICAgICAgICAgICAgICAgIEJhY2sgdG8gRGFzaGJvYXJkXG4gICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgPENvZGUgY2xhc3NOYW1lPVwidy04IGgtOCB0ZXh0LXByaW1hcnktNjAwXCIgLz5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWwtMiB0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+SmF2YUxlYXJuPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgIFByb2dyZXNzOiB7cHJvZ3Jlc3N9JVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTMyIGJnLWdyYXktMjAwIHJvdW5kZWQtZnVsbCBoLTJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IFxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctcHJpbWFyeS02MDAgaC0yIHJvdW5kZWQtZnVsbCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDBcIlxuICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6IGAke3Byb2dyZXNzfSVgIH19XG4gICAgICAgICAgICAgICAgPjwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvbmF2PlxuXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04IHB5LThcIj5cbiAgICAgICAgey8qIExlc3NvbiBIZWFkZXIgKi99XG4gICAgICAgIDxtb3Rpb24uZGl2IFxuICAgICAgICAgIGNsYXNzTmFtZT1cIm1iLThcIlxuICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjYgfX1cbiAgICAgICAgPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTRcIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtYi0yXCI+e2xlc3Nvbi50aXRsZX08L2gxPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG1iLTRcIj57bGVzc29uLmRlc2NyaXB0aW9ufTwvcD5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTQgdGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgcHgtMiBweS0xIHJvdW5kZWQtZnVsbCB0ZXh0LXhzICR7XG4gICAgICAgICAgICAgICAgICBsZXNzb24uZGlmZmljdWx0eSA9PT0gJ0JlZ2lubmVyJyA/ICdiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi04MDAnIDpcbiAgICAgICAgICAgICAgICAgIGxlc3Nvbi5kaWZmaWN1bHR5ID09PSAnSW50ZXJtZWRpYXRlJyA/ICdiZy15ZWxsb3ctMTAwIHRleHQteWVsbG93LTgwMCcgOlxuICAgICAgICAgICAgICAgICAgJ2JnLXJlZC0xMDAgdGV4dC1yZWQtODAwJ1xuICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgIHtsZXNzb24uZGlmZmljdWx0eX1cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxDbG9jayBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTFcIiAvPlxuICAgICAgICAgICAgICAgICAge2xlc3Nvbi5kdXJhdGlvbn1cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAge2lzQ29tcGxldGVkICYmIChcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHRleHQtZ3JlZW4tNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgICBDb21wbGV0ZWRcbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIFRhYiBOYXZpZ2F0aW9uICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgICA8bmF2IGNsYXNzTmFtZT1cIi1tYi1weCBmbGV4IHNwYWNlLXgtOFwiPlxuICAgICAgICAgICAgICB7W1xuICAgICAgICAgICAgICAgIHsgaWQ6ICd0aGVvcnknLCBsYWJlbDogJ1RoZW9yeScsIGljb246IEJvb2tPcGVuIH0sXG4gICAgICAgICAgICAgICAgeyBpZDogJ2V4YW1wbGUnLCBsYWJlbDogJ0V4YW1wbGUnLCBpY29uOiBTdGFyIH0sXG4gICAgICAgICAgICAgICAgeyBpZDogJ2V4ZXJjaXNlJywgbGFiZWw6ICdFeGVyY2lzZScsIGljb246IFRhcmdldCB9XG4gICAgICAgICAgICAgIF0ubWFwKCh0YWIpID0+IChcbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBrZXk9e3RhYi5pZH1cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEN1cnJlbnRUYWIodGFiLmlkIGFzIGFueSl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBweS0yIHB4LTEgYm9yZGVyLWItMiBmb250LW1lZGl1bSB0ZXh0LXNtIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgICAgIGN1cnJlbnRUYWIgPT09IHRhYi5pZFxuICAgICAgICAgICAgICAgICAgICAgID8gJ2JvcmRlci1wcmltYXJ5LTUwMCB0ZXh0LXByaW1hcnktNjAwJ1xuICAgICAgICAgICAgICAgICAgICAgIDogJ2JvcmRlci10cmFuc3BhcmVudCB0ZXh0LWdyYXktNTAwIGhvdmVyOnRleHQtZ3JheS03MDAgaG92ZXI6Ym9yZGVyLWdyYXktMzAwJ1xuICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPHRhYi5pY29uIGNsYXNzTmFtZT1cInctNCBoLTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICB7dGFiLmxhYmVsfVxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvbmF2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgey8qIENvbnRlbnQgKi99XG4gICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAga2V5PXtjdXJyZW50VGFifVxuICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeDogMjAgfX1cbiAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHg6IDAgfX1cbiAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjQgfX1cbiAgICAgICAgPlxuICAgICAgICAgIHtjdXJyZW50VGFiID09PSAndGhlb3J5JyAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmQgcC04XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHJvc2UgbWF4LXctbm9uZVwiPlxuICAgICAgICAgICAgICAgIDxkaXYgZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUw9e3sgX19odG1sOiBsZXNzb24uY29udGVudC50aGVvcnkucmVwbGFjZSgvXFxuL2csICc8YnI+JykgfX0gLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuXG4gICAgICAgICAge2N1cnJlbnRUYWIgPT09ICdleGFtcGxlJyAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmQgcC02XCI+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTRcIj5FeGFtcGxlIENvZGU8L2gzPlxuICAgICAgICAgICAgICAgIDxDb2RlRWRpdG9yXG4gICAgICAgICAgICAgICAgICBpbml0aWFsQ29kZT17bGVzc29uLmNvbnRlbnQuZXhhbXBsZX1cbiAgICAgICAgICAgICAgICAgIHJlYWRPbmx5PXt0cnVlfVxuICAgICAgICAgICAgICAgICAgaGVpZ2h0PVwiNTAwcHhcIlxuICAgICAgICAgICAgICAgICAgc2hvd091dHB1dD17dHJ1ZX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7Y3VycmVudFRhYiA9PT0gJ2V4ZXJjaXNlJyAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmQgcC02XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNFwiPlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwXCI+UHJhY3RpY2UgRXhlcmNpc2U8L2gzPlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93SGludHMoIXNob3dIaW50cyl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJ0bi1zZWNvbmRhcnkgdGV4dC1zbVwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIHtzaG93SGludHMgPyAnSGlkZSBIaW50cycgOiAnU2hvdyBIaW50cyd9XG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInByb3NlIG1heC13LW5vbmUgbWItNlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBkYW5nZXJvdXNseVNldElubmVySFRNTD17eyBfX2h0bWw6IGxlc3Nvbi5jb250ZW50LmV4ZXJjaXNlLmRlc2NyaXB0aW9uLnJlcGxhY2UoL1xcbi9nLCAnPGJyPicpIH19IC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAge3Nob3dIaW50cyAmJiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXllbGxvdy01MCBib3JkZXIgYm9yZGVyLXllbGxvdy0yMDAgcm91bmRlZC1sZyBwLTQgbWItNlwiPlxuICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC15ZWxsb3ctODAwIG1iLTJcIj5IaW50czo8L2g0PlxuICAgICAgICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwidGV4dC15ZWxsb3ctNzAwIHRleHQtc20gc3BhY2UteS0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2xlc3Nvbi5jb250ZW50LmV4ZXJjaXNlLmhpbnRzLm1hcCgoaGludCwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxsaSBrZXk9e2luZGV4fT7igKIge2hpbnR9PC9saT5cbiAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxDb2RlRWRpdG9yXG4gICAgICAgICAgICAgICAgaW5pdGlhbENvZGU9e2xlc3Nvbi5jb250ZW50LmV4ZXJjaXNlLnN0YXJ0ZXJDb2RlfVxuICAgICAgICAgICAgICAgIG9uQ29kZUNoYW5nZT17aGFuZGxlQ29kZUNoYW5nZX1cbiAgICAgICAgICAgICAgICBvblJ1bj17aGFuZGxlUnVuQ29kZX1cbiAgICAgICAgICAgICAgICBoZWlnaHQ9XCI0MDBweFwiXG4gICAgICAgICAgICAgICAgc2hvd091dHB1dD17dHJ1ZX1cbiAgICAgICAgICAgICAgICBleHBlY3RlZE91dHB1dD17bGVzc29uLmNvbnRlbnQuZXhlcmNpc2UuZXhwZWN0ZWRPdXRwdXR9XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgey8qIE5hdmlnYXRpb24gQnV0dG9ucyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgbXQtOFwiPlxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVByZXZMZXNzb259XG4gICAgICAgICAgICBkaXNhYmxlZD17IWxlc3Nvbi5wcmV2TGVzc29uSWR9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBidG4tc2Vjb25kYXJ5IGRpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8Q2hldnJvbkxlZnQgY2xhc3NOYW1lPVwidy00IGgtNCBtci0xXCIgLz5cbiAgICAgICAgICAgIFByZXZpb3VzIExlc3NvblxuICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAge2N1cnJlbnRUYWIgIT09ICdleGVyY2lzZScgJiYgKFxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgY29uc3QgbmV4dFRhYiA9IGN1cnJlbnRUYWIgPT09ICd0aGVvcnknID8gJ2V4YW1wbGUnIDogJ2V4ZXJjaXNlJ1xuICAgICAgICAgICAgICAgICAgc2V0Q3VycmVudFRhYihuZXh0VGFiKVxuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYnRuLXByaW1hcnlcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgQ29udGludWVcbiAgICAgICAgICAgICAgICA8Q2hldnJvblJpZ2h0IGNsYXNzTmFtZT1cInctNCBoLTQgbWwtMVwiIC8+XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIFxuICAgICAgICAgICAge2N1cnJlbnRUYWIgPT09ICdleGVyY2lzZScgJiYgaXNDb21wbGV0ZWQgJiYgKFxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlTmV4dExlc3Nvbn1cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17IWxlc3Nvbi5uZXh0TGVzc29uSWR9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgYnRuLXByaW1hcnkgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgTmV4dCBMZXNzb25cbiAgICAgICAgICAgICAgICA8Q2hldnJvblJpZ2h0IGNsYXNzTmFtZT1cInctNCBoLTQgbWwtMVwiIC8+XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUGFyYW1zIiwidXNlUm91dGVyIiwibW90aW9uIiwiQ2hldnJvbkxlZnQiLCJDaGV2cm9uUmlnaHQiLCJCb29rT3BlbiIsIkNoZWNrQ2lyY2xlIiwiVGFyZ2V0IiwiQ2xvY2siLCJTdGFyIiwiQ29kZSIsIkFycm93TGVmdCIsIkxpbmsiLCJDb2RlRWRpdG9yIiwidG9hc3QiLCJMZXNzb25QYWdlIiwicGFyYW1zIiwicm91dGVyIiwibGVzc29uIiwic2V0TGVzc29uIiwiY3VycmVudFRhYiIsInNldEN1cnJlbnRUYWIiLCJ1c2VyQ29kZSIsInNldFVzZXJDb2RlIiwiaXNDb21wbGV0ZWQiLCJzZXRJc0NvbXBsZXRlZCIsInNob3dIaW50cyIsInNldFNob3dIaW50cyIsInByb2dyZXNzIiwic2V0UHJvZ3Jlc3MiLCJnZXRMZXNzb25EYXRhIiwiaWQiLCJsZXNzb25zIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImRpZmZpY3VsdHkiLCJkdXJhdGlvbiIsImNvbnRlbnQiLCJ0aGVvcnkiLCJleGFtcGxlIiwiZXhlcmNpc2UiLCJzdGFydGVyQ29kZSIsImV4cGVjdGVkT3V0cHV0IiwiaGludHMiLCJuZXh0TGVzc29uSWQiLCJwcmV2TGVzc29uSWQiLCJ1bmRlZmluZWQiLCJtb2NrTGVzc29uIiwidGFiUHJvZ3Jlc3MiLCJoYW5kbGVDb2RlQ2hhbmdlIiwiY29kZSIsImhhbmRsZVJ1bkNvZGUiLCJtb2NrT3V0cHV0IiwidHJpbSIsInN1Y2Nlc3MiLCJlcnJvciIsImhhbmRsZU5leHRMZXNzb24iLCJwdXNoIiwiaGFuZGxlUHJldkxlc3NvbiIsImRpdiIsImNsYXNzTmFtZSIsIm5hdiIsImhyZWYiLCJzcGFuIiwic3R5bGUiLCJ3aWR0aCIsImluaXRpYWwiLCJvcGFjaXR5IiwieSIsImFuaW1hdGUiLCJ0cmFuc2l0aW9uIiwiaDEiLCJwIiwibGFiZWwiLCJpY29uIiwibWFwIiwidGFiIiwiYnV0dG9uIiwib25DbGljayIsIngiLCJkYW5nZXJvdXNseVNldElubmVySFRNTCIsIl9faHRtbCIsInJlcGxhY2UiLCJoMyIsImluaXRpYWxDb2RlIiwicmVhZE9ubHkiLCJoZWlnaHQiLCJzaG93T3V0cHV0IiwiaDQiLCJ1bCIsImhpbnQiLCJpbmRleCIsImxpIiwib25Db2RlQ2hhbmdlIiwib25SdW4iLCJkaXNhYmxlZCIsIm5leHRUYWIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/lessons/[id]/page.tsx\n"));

/***/ })

});