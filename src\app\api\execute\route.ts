import { NextRequest, NextResponse } from 'next/server'
import { exec } from 'child_process'
import { promisify } from 'util'
import { writeFile, unlink, mkdir } from 'fs/promises'
import path from 'path'

const execAsync = promisify(exec)

export async function POST(request: NextRequest) {
  console.log('🔍 Code execution API called')

  try {
    const { code, language = 'java' } = await request.json()
    console.log('📝 Code received:', code.substring(0, 100) + '...')

    if (!code) {
      console.log('❌ No code provided')
      return NextResponse.json(
        { success: false, error: 'No code provided' },
        { status: 400 }
      )
    }

    if (language !== 'java') {
      console.log('❌ Unsupported language:', language)
      return NextResponse.json(
        { success: false, error: 'Only Java is supported currently' },
        { status: 400 }
      )
    }

    // Create temp directory if it doesn't exist
    const tempDir = path.join(process.cwd(), 'temp')
    try {
      await mkdir(tempDir, { recursive: true })
    } catch (error) {
      // Directory might already exist
    }

    const sessionId = Date.now().toString()
    const fileName = `Main_${sessionId}.java`
    const filePath = path.join(tempDir, fileName)
    const classPath = path.join(tempDir, `Main_${sessionId}.class`)

    try {
      // Write Java code to file
      await writeFile(filePath, code)

      // Try to compile and execute
      try {
        console.log('☕ Checking Java availability...')
        // Check if Java is available
        await execAsync('java -version')
        await execAsync('javac -version')
        console.log('✅ Java is available')

        // Compile Java code
        console.log('🔨 Compiling Java code...')
        const compileCommand = `javac "${filePath}"`
        const { stderr: compileError } = await execAsync(compileCommand)

        if (compileError && !compileError.includes('Note:')) {
          console.log('❌ Compilation error:', compileError)
          return NextResponse.json({
            success: false,
            error: `Compilation Error: ${compileError}`
          })
        }
        console.log('✅ Compilation successful')

        // Execute Java code with timeout
        console.log('🚀 Executing Java code...')
        const executeCommand = process.platform === 'win32'
          ? `cd "${tempDir}" && timeout 10 java Main_${sessionId}`
          : `cd "${tempDir}" && timeout 10s java Main_${sessionId}`

        const { stdout, stderr } = await execAsync(executeCommand)

        if (stderr && !stderr.includes('Note:')) {
          console.log('❌ Runtime error:', stderr)
          return NextResponse.json({
            success: false,
            error: `Runtime Error: ${stderr}`
          })
        }

        console.log('✅ Execution successful:', stdout.trim())
        return NextResponse.json({
          success: true,
          output: stdout.trim() || 'Program executed successfully (no output)'
        })

      } catch (execError: any) {
        if (execError.code === 124 || execError.message.includes('timeout')) {
          return NextResponse.json({
            success: false,
            error: 'Execution timeout (10 seconds exceeded)'
          })
        }

        if (execError.stderr) {
          return NextResponse.json({
            success: false,
            error: `Error: ${execError.stderr}`
          })
        }

        // If Java is not installed, return a mock response
        if (execError.message.includes('java') || execError.code === 'ENOENT') {
          console.log('☕ Java not found, using simulation mode')

          // Enhanced simulation based on code analysis
          let simulatedOutput = ''

          // Extract System.out.println statements
          const printMatches = code.match(/System\.out\.println\s*\(\s*([^)]+)\s*\)/g)
          if (printMatches) {
            simulatedOutput = printMatches.map(match => {
              // Extract the content inside println
              const content = match.match(/System\.out\.println\s*\(\s*([^)]+)\s*\)/)[1]

              // Handle string literals
              const stringMatch = content.match(/["']([^"']+)["']/)
              if (stringMatch) {
                return stringMatch[1]
              }

              // Handle simple concatenation
              if (content.includes('+')) {
                // Simple simulation for string concatenation
                if (content.includes('"Hello') || content.includes("'Hello")) {
                  return 'Hello, World!'
                }
                return 'Concatenated output'
              }

              // Handle variables (basic simulation)
              if (content.includes('name')) return 'John Doe'
              if (content.includes('age')) return '25'
              if (content.includes('grade')) return 'A'
              if (content.includes('price')) return '$19.99'
              if (content.includes('student')) return 'true'

              return 'Output'
            }).join('\n')
          }

          // Default output if no println found
          if (!simulatedOutput) {
            simulatedOutput = 'Program executed successfully'
          }

          simulatedOutput += '\n\n🔧 Simulation Mode: Java JDK not found. Install Java 17+ for real code execution.'

          console.log('🎭 Simulated output:', simulatedOutput)
          return NextResponse.json({
            success: true,
            output: simulatedOutput
          })
        }

        throw execError
      }

    } finally {
      // Cleanup files
      try {
        await unlink(filePath).catch(() => {})
        await unlink(classPath).catch(() => {})
      } catch (error) {
        console.error('Cleanup error:', error)
      }
    }

  } catch (error) {
    console.error('Execute API error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
