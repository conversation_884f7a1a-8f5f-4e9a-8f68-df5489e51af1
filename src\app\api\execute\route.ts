import { NextRequest, NextResponse } from 'next/server'
import { exec } from 'child_process'
import { promisify } from 'util'
import { writeFile, unlink, mkdir } from 'fs/promises'
import path from 'path'

const execAsync = promisify(exec)

export async function POST(request: NextRequest) {
  try {
    const { code, language = 'java' } = await request.json()

    if (!code) {
      return NextResponse.json(
        { success: false, error: 'No code provided' },
        { status: 400 }
      )
    }

    if (language !== 'java') {
      return NextResponse.json(
        { success: false, error: 'Only Java is supported currently' },
        { status: 400 }
      )
    }

    // Create temp directory if it doesn't exist
    const tempDir = path.join(process.cwd(), 'temp')
    try {
      await mkdir(tempDir, { recursive: true })
    } catch (error) {
      // Directory might already exist
    }

    const sessionId = Date.now().toString()
    const fileName = `Main_${sessionId}.java`
    const filePath = path.join(tempDir, fileName)
    const classPath = path.join(tempDir, `Main_${sessionId}.class`)

    try {
      // Write Java code to file
      await writeFile(filePath, code)

      // Try to compile and execute
      try {
        // Check if Java is available
        await execAsync('java -version')
        await execAsync('javac -version')

        // Compile Java code
        const compileCommand = `javac "${filePath}"`
        const { stderr: compileError } = await execAsync(compileCommand)

        if (compileError && !compileError.includes('Note:')) {
          return NextResponse.json({
            success: false,
            error: `Compilation Error: ${compileError}`
          })
        }

        // Execute Java code with timeout
        const executeCommand = process.platform === 'win32'
          ? `cd "${tempDir}" && timeout 10 java Main_${sessionId}`
          : `cd "${tempDir}" && timeout 10s java Main_${sessionId}`

        const { stdout, stderr } = await execAsync(executeCommand)

        if (stderr && !stderr.includes('Note:')) {
          return NextResponse.json({
            success: false,
            error: `Runtime Error: ${stderr}`
          })
        }

        return NextResponse.json({
          success: true,
          output: stdout.trim() || 'Program executed successfully (no output)'
        })

      } catch (execError: any) {
        if (execError.code === 124 || execError.message.includes('timeout')) {
          return NextResponse.json({
            success: false,
            error: 'Execution timeout (10 seconds exceeded)'
          })
        }

        if (execError.stderr) {
          return NextResponse.json({
            success: false,
            error: `Error: ${execError.stderr}`
          })
        }

        // If Java is not installed, return a mock response
        if (execError.message.includes('java') || execError.code === 'ENOENT') {
          return NextResponse.json({
            success: true,
            output: 'Java runtime not found. This is a simulated output:\nHello, World!\n\n(Install Java JDK to execute code for real)'
          })
        }

        throw execError
      }

    } finally {
      // Cleanup files
      try {
        await unlink(filePath).catch(() => {})
        await unlink(classPath).catch(() => {})
      } catch (error) {
        console.error('Cleanup error:', error)
      }
    }

  } catch (error) {
    console.error('Execute API error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
