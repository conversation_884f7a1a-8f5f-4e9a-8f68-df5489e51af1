'use client'

import { useState } from 'react'
import SimpleCodeEditor from '@/components/SimpleCodeEditor'
import { BookOpen, Code, ArrowLeft } from 'lucide-react'
import Link from 'next/link'

export default function LessonSimplePage() {
  const [currentTab, setCurrentTab] = useState<'theory' | 'example' | 'exercise'>('exercise')

  const exerciseCode = `public class StudentInfo {
    public static void main(String[] args) {
        // TODO: Declare variables for student information
        String name = "<PERSON> Johnson";
        int age = 20;
        double gpa = 3.8;
        boolean enrolled = true;
        
        // TODO: Print the student information
        System.out.println("Student: " + name);
        System.out.println("Age: " + age);
        System.out.println("GPA: " + gpa);
        System.out.println("Enrolled: " + enrolled);
    }
}`

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/dashboard" className="flex items-center text-gray-600 hover:text-primary-600 transition-colors mr-4">
                <ArrowLeft className="w-5 h-5 mr-1" />
                Back to Dashboard
              </Link>
              <Code className="w-8 h-8 text-primary-600" />
              <span className="ml-2 text-xl font-bold text-gray-900">JavaLearn - Simple Test</span>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Simple Lesson Test</h1>
          <p className="text-gray-600">Testing code execution with simplified editor</p>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'theory', label: 'Theory', icon: BookOpen },
              { id: 'example', label: 'Example', icon: Code },
              { id: 'exercise', label: 'Exercise', icon: Code }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setCurrentTab(tab.id as any)}
                className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  currentTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="w-4 h-4 mr-2" />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        {currentTab === 'theory' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Theory: Variables and Data Types</h2>
            <div className="prose max-w-none">
              <p>Variables are containers that store data values. In Java, every variable has a specific type.</p>
              <h3>Common Data Types:</h3>
              <ul>
                <li><strong>String</strong>: Text data like "Hello World"</li>
                <li><strong>int</strong>: Integer numbers like 25</li>
                <li><strong>double</strong>: Decimal numbers like 3.8</li>
                <li><strong>boolean</strong>: true or false values</li>
              </ul>
            </div>
          </div>
        )}

        {currentTab === 'example' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Example: Variable Declaration</h2>
            <SimpleCodeEditor 
              initialCode={`public class VariableExample {
    public static void main(String[] args) {
        // Declare variables
        String name = "John Doe";
        int age = 25;
        double price = 19.99;
        boolean isStudent = true;
        
        // Print variables
        System.out.println("Name: " + name);
        System.out.println("Age: " + age);
        System.out.println("Price: $" + price);
        System.out.println("Is Student: " + isStudent);
    }
}`}
            />
          </div>
        )}

        {currentTab === 'exercise' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Exercise: Student Information</h2>
            <div className="mb-4">
              <h3 className="font-medium mb-2">Requirements:</h3>
              <ul className="list-disc list-inside text-gray-700 space-y-1">
                <li>Declare variables for student name, age, GPA, and enrollment status</li>
                <li>Print all the information in a formatted way</li>
                <li>Expected output: Student info with all details</li>
              </ul>
            </div>
            
            <SimpleCodeEditor initialCode={exerciseCode} />
            
            <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-medium text-blue-800 mb-2">Expected Output:</h4>
              <pre className="text-blue-700 text-sm">
Student: Alice Johnson
Age: 20
GPA: 3.8
Enrolled: true
              </pre>
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="bg-white rounded-lg shadow p-6 mt-6">
          <h2 className="text-xl font-semibold mb-4">Testing Instructions</h2>
          <div className="space-y-2 text-gray-700">
            <p>1. <strong>Click the "Run" button</strong> in the code editor above</p>
            <p>2. <strong>Check the output panel</strong> below the code for results</p>
            <p>3. <strong>Open browser console (F12)</strong> to see detailed logs</p>
            <p>4. <strong>Try modifying the code</strong> and running it again</p>
            <p>5. If this works, the issue is with Monaco editor. If not, it's the API.</p>
          </div>
        </div>
      </div>
    </div>
  )
}
