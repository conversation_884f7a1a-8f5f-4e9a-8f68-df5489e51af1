# Troubleshooting Guide

## Common Issues and Solutions

### 1. Viewport Metadata Warnings
**Issue**: `⚠ Unsupported metadata viewport is configured in metadata export`

**Solution**: ✅ **FIXED** - Updated to use separate `viewport` export as required by Next.js 14.

### 2. NextAuth Configuration Errors
**Issue**: `[next-auth][warn][NO_SECRET]` and OAuth provider errors

**Solution**: ✅ **FIXED** - Added proper environment configuration:
- Created `.env.local` with default values
- Made OAuth providers optional (only load if credentials are provided)
- Added demo credentials for testing

### 3. Demo Login Credentials
Use these credentials to test the application:
- **Email**: `<EMAIL>`
- **Password**: `password123`

### 4. Code Execution Issues
**Issue**: Java code execution fails

**Possible Solutions**:
1. **Install Java JDK 17+**:
   ```bash
   # Ubuntu/Debian
   sudo apt install openjdk-17-jdk
   
   # macOS (with Homebrew)
   brew install openjdk@17
   
   # Windows
   # Download from Oracle or use Chocolatey
   choco install openjdk17
   ```

2. **Verify Java Installation**:
   ```bash
   java -version
   javac -version
   ```

3. **Check Backend Server**:
   - Ensure backend is running on port 3001
   - Check server logs for errors
   - Verify temp directory permissions

### 5. Port Already in Use
**Issue**: `Error: listen EADDRINUSE: address already in use :::3000`

**Solution**:
```bash
# Kill processes using the ports
# On Windows
netstat -ano | findstr :3000
taskkill /PID <PID> /F

# On macOS/Linux
lsof -ti:3000 | xargs kill -9
lsof -ti:3001 | xargs kill -9
```

### 6. Module Not Found Errors
**Issue**: `Module not found` errors

**Solution**:
```bash
# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Or use npm ci for clean install
npm ci
```

### 7. CORS Errors
**Issue**: Cross-origin request blocked

**Solution**: ✅ **FIXED** - Updated CORS configuration in server to allow localhost and 127.0.0.1

### 8. Database Connection Issues
**Issue**: Database connection errors (for production)

**Solution**:
1. Ensure PostgreSQL is running
2. Check DATABASE_URL in environment variables
3. Run database migrations if using Prisma

### 9. Build Errors
**Issue**: Next.js build fails

**Solution**:
```bash
# Clear Next.js cache
rm -rf .next

# Rebuild
npm run build
```

### 10. Docker Issues
**Issue**: Docker container fails to start

**Solution**:
1. **Check Docker is running**:
   ```bash
   docker --version
   docker-compose --version
   ```

2. **Rebuild containers**:
   ```bash
   docker-compose down
   docker-compose build --no-cache
   docker-compose up
   ```

3. **Check logs**:
   ```bash
   docker-compose logs app
   ```

## Environment Setup Checklist

- [ ] Node.js 18+ installed
- [ ] Java JDK 17+ installed
- [ ] `.env.local` file created with proper values
- [ ] Dependencies installed (`npm install`)
- [ ] Backend server running on port 3001
- [ ] Frontend server running on port 3000

## Getting Help

1. **Check the logs**: Look at both frontend and backend console output
2. **Verify environment**: Ensure all required environment variables are set
3. **Test step by step**: Start with basic functionality and add features
4. **Check network**: Ensure no firewall blocking local ports

## Quick Reset

If everything seems broken, try this complete reset:

```bash
# Stop all servers
# Kill any running Node.js processes

# Clean everything
rm -rf node_modules package-lock.json .next server/temp
rm .env.local

# Reinstall
npm install

# Recreate environment
cp .env.local.example .env.local
# Edit .env.local with proper values

# Restart
npm run dev:server &
npm run dev
```

## Performance Tips

1. **Code Execution**: Large programs may take longer to compile/execute
2. **Memory Usage**: Monitor system resources during development
3. **File Cleanup**: Temporary Java files are automatically cleaned up
4. **Browser Cache**: Clear browser cache if seeing stale content

## Security Notes

- Demo credentials are for development only
- Change NEXTAUTH_SECRET in production
- Use environment variables for sensitive data
- Code execution is sandboxed but monitor resource usage
