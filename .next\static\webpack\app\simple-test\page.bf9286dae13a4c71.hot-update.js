"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/simple-test/page",{

/***/ "(app-pages-browser)/./src/components/SimpleCodeEditor.tsx":
/*!*********************************************!*\
  !*** ./src/components/SimpleCodeEditor.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SimpleCodeEditor; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Play_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction SimpleCodeEditor(param) {\n    let { initialCode = 'public class Test {\\n    public static void main(String[] args) {\\n        System.out.println(\"Hello, World!\");\\n    }\\n}', onRun } = param;\n    _s();\n    const [code, setCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialCode);\n    const [output, setOutput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isRunning, setIsRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleRunCode = async ()=>{\n        console.log(\"\\uD83D\\uDE80 SimpleCodeEditor: Run button clicked\");\n        if (isRunning) {\n            console.log(\"⚠️ Already running, ignoring click\");\n            return;\n        }\n        setIsRunning(true);\n        setOutput(\"Executing...\");\n        try {\n            console.log(\"\\uD83D\\uDCDD Code to execute:\", code.substring(0, 100) + \"...\");\n            // If parent provides onRun, use it\n            if (onRun) {\n                console.log(\"\\uD83D\\uDD04 Using parent onRun function\");\n                onRun(code);\n                setIsRunning(false);\n                return;\n            }\n            console.log(\"\\uD83D\\uDCE1 Making API request to /api/execute\");\n            // Add absolute URL to ensure it works\n            const apiUrl = window.location.origin + \"/api/execute\";\n            console.log(\"\\uD83D\\uDCE1 API URL:\", apiUrl);\n            const requestBody = {\n                code: code,\n                language: \"java\"\n            };\n            console.log(\"\\uD83D\\uDCE1 Request body:\", requestBody);\n            const response = await fetch(apiUrl, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestBody)\n            });\n            console.log(\"\\uD83D\\uDCE1 Response received:\", response.status, response.statusText);\n            console.log(\"\\uD83D\\uDCE1 Response headers:\", [\n                ...response.headers.entries()\n            ]);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.log(\"❌ Response not OK:\", errorText);\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(errorText));\n            }\n            const result = await response.json();\n            console.log(\"✅ API Result:\", result);\n            if (result.success) {\n                setOutput(result.output || \"No output\");\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].success(\"Code executed successfully!\");\n            } else {\n                setOutput(\"Error: \".concat(result.error));\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(\"Code execution failed!\");\n            }\n        } catch (error) {\n            console.error(\"\\uD83D\\uDCA5 Execution error:\", error);\n            const errorMessage = error instanceof Error ? error.message : \"Unknown error\";\n            setOutput(\"Error: \".concat(errorMessage));\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(\"Failed to execute code!\");\n        } finally{\n            setIsRunning(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border border-gray-300 rounded-lg overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 text-white px-4 py-2 flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium\",\n                        children: \"Simple Java Editor\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\SimpleCodeEditor.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleRunCode,\n                        disabled: isRunning,\n                        className: \"flex items-center space-x-1 px-3 py-1 rounded text-sm transition-colors \".concat(isRunning ? \"bg-gray-600 cursor-not-allowed\" : \"bg-green-600 hover:bg-green-700\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\SimpleCodeEditor.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: isRunning ? \"Running...\" : \"Run\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\SimpleCodeEditor.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\SimpleCodeEditor.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\SimpleCodeEditor.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                value: code,\n                onChange: (e)=>setCode(e.target.value),\n                className: \"w-full h-64 p-4 font-mono text-sm border-none resize-none focus:outline-none\",\n                placeholder: \"Enter your Java code here...\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\SimpleCodeEditor.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-100 px-4 py-2 border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium text-gray-700\",\n                            children: \"Output\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\SimpleCodeEditor.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\SimpleCodeEditor.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 bg-gray-900 text-green-400 font-mono text-sm min-h-[100px]\",\n                        children: output || 'Click \"Run\" to execute your code'\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\SimpleCodeEditor.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\SimpleCodeEditor.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\SimpleCodeEditor.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleCodeEditor, \"kJGLfZXebJ5bXQ/8CaX9Fl3HCwE=\");\n_c = SimpleCodeEditor;\nvar _c;\n$RefreshReg$(_c, \"SimpleCodeEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SimpleCodeEditor.tsx\n"));

/***/ })

});