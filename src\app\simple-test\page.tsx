'use client'

import { useState } from 'react'
import SimpleCodeEditor from '@/components/SimpleCodeEditor'
import { Bug } from 'lucide-react'

export default function SimpleTestPage() {
  const [apiStatus, setApiStatus] = useState<string>('Not tested')

  const testAPI = async () => {
    try {
      setApiStatus('Testing...')
      const response = await fetch('/api/test')
      const result = await response.json()
      setApiStatus(`✅ API Working: ${result.message}`)
    } catch (error) {
      setApiStatus(`❌ API Failed: ${error}`)
    }
  }

  const testExecuteAPI = async () => {
    try {
      setApiStatus('Testing execute API...')
      const response = await fetch('/api/execute')
      const result = await response.json()
      setApiStatus(`✅ Execute API Working: ${result.message}`)
    } catch (error) {
      setApiStatus(`❌ Execute API Failed: ${error}`)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center mb-8">
          <Bug className="w-8 h-8 text-red-600 mr-3" />
          <h1 className="text-3xl font-bold text-gray-900">Simple Code Execution Test</h1>
        </div>

        {/* API Status */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">API Status</h2>
          <div className="flex space-x-4 mb-4">
            <button
              onClick={testAPI}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            >
              Test Basic API
            </button>
            <button
              onClick={testExecuteAPI}
              className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
            >
              Test Execute API
            </button>
          </div>
          <div className="p-3 bg-gray-100 rounded">
            Status: {apiStatus}
          </div>
        </div>

        {/* Simple Code Editor */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Simple Code Editor Test</h2>
          <p className="text-gray-600 mb-4">
            This is a simplified version of the code editor. If this works, we know the issue is with the Monaco editor.
          </p>
          
          <SimpleCodeEditor 
            initialCode={`public class SimpleTest {
    public static void main(String[] args) {
        System.out.println("Hello from Simple Editor!");
        System.out.println("Testing basic execution");
        
        int x = 5;
        int y = 10;
        System.out.println("Sum: " + (x + y));
    }
}`}
          />
        </div>

        {/* Instructions */}
        <div className="bg-white rounded-lg shadow p-6 mt-6">
          <h2 className="text-xl font-semibold mb-4">Test Instructions</h2>
          <ol className="list-decimal list-inside space-y-2 text-gray-700">
            <li>First, test the API endpoints using the buttons above</li>
            <li>Then, try running code in the Simple Code Editor</li>
            <li>Check the browser console (F12) for detailed logs</li>
            <li>If this works, the issue is with the Monaco editor integration</li>
            <li>If this doesn't work, the issue is with the API or network</li>
          </ol>
        </div>

        {/* Direct API Test */}
        <div className="bg-white rounded-lg shadow p-6 mt-6">
          <h2 className="text-xl font-semibold mb-4">Direct API Test</h2>
          <p className="text-gray-600 mb-4">
            You can also test the API directly by visiting:
          </p>
          <ul className="list-disc list-inside space-y-1 text-blue-600">
            <li><a href="/api/test" target="_blank" className="hover:underline">/api/test</a></li>
            <li><a href="/api/execute" target="_blank" className="hover:underline">/api/execute</a></li>
            <li><a href="/test-api.html" target="_blank" className="hover:underline">/test-api.html</a></li>
          </ul>
        </div>
      </div>
    </div>
  )
}
