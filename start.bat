@echo off
echo 🚀 Starting Java Learning Platform...

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 18+ and try again.
    pause
    exit /b 1
)

REM Check if Java is installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  Java is not installed. Code execution will not work without Java JDK 17+.
    echo    You can still run the platform, but install Java for full functionality.
)

REM Check if Docker is installed
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  Docker is not installed. Using local Java execution instead.
    echo    Install Docker for enhanced security and isolation.
)

REM Create .env.local if it doesn't exist
if not exist .env.local (
    echo 📝 Creating environment configuration...
    (
        echo # NextAuth Configuration
        echo NEXTAUTH_URL=http://localhost:3000
        echo NEXTAUTH_SECRET=java-learning-platform-secret-key-for-development-only
        echo.
        echo # OAuth Providers ^(Optional - commented out to avoid errors^)
        echo # GOOGLE_CLIENT_ID=
        echo # GOOGLE_CLIENT_SECRET=
        echo # GITHUB_ID=
        echo # GITHUB_SECRET=
        echo.
        echo # Backend Server URL
        echo BACKEND_URL=http://localhost:3001
        echo.
        echo # Development Settings
        echo NODE_ENV=development
    ) > .env.local
    echo ✅ Created .env.local with default configuration
)

REM Install dependencies
echo 📦 Installing dependencies...
call npm install

REM Create temp directory for code execution
if not exist server\temp mkdir server\temp

REM Start the application
echo 🎯 Starting development servers...
echo    Frontend: http://localhost:3000
echo    Backend:  http://localhost:3001
echo.

REM Start backend server
echo Starting backend server...
start /b npm run dev:server

REM Wait a moment for backend to start
timeout /t 3 /nobreak >nul

REM Start frontend server
echo Starting frontend server...
start /b npm run dev

echo.
echo ✅ Java Learning Platform is running!
echo    Open http://localhost:3000 in your browser
echo    Press any key to stop the servers
echo.

pause

REM Kill Node.js processes (this is a simple approach)
taskkill /f /im node.exe >nul 2>&1

echo 🛑 Servers stopped.
pause
