"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/execute/route";
exports.ids = ["app/api/execute/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("child_process");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("fs/promises");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fexecute%2Froute&page=%2Fapi%2Fexecute%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fexecute%2Froute.ts&appDir=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CClients%5CDMI%5CJava-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fexecute%2Froute&page=%2Fapi%2Fexecute%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fexecute%2Froute.ts&appDir=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CClients%5CDMI%5CJava-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Clients_DMI_Java_app_src_app_api_execute_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/execute/route.ts */ \"(rsc)/./src/app/api/execute/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/execute/route\",\n        pathname: \"/api/execute\",\n        filename: \"route\",\n        bundlePath: \"app/api/execute/route\"\n    },\n    resolvedPagePath: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\api\\\\execute\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Clients_DMI_Java_app_src_app_api_execute_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/execute/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fexecute%2Froute&page=%2Fapi%2Fexecute%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fexecute%2Froute.ts&appDir=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CClients%5CDMI%5CJava-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/execute/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/execute/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! child_process */ \"child_process\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(child_process__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! util */ \"util\");\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(util__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nconst execAsync = (0,util__WEBPACK_IMPORTED_MODULE_2__.promisify)(child_process__WEBPACK_IMPORTED_MODULE_1__.exec);\n// Enhanced Java code simulation function\nfunction simulateJavaExecution(code) {\n    console.log(\"\\uD83C\\uDFAD Simulating Java execution for code length:\", code.length);\n    try {\n        let output = \"\";\n        // Extract System.out.println statements\n        const printMatches = code.match(/System\\.out\\.println\\s*\\(\\s*([^)]+)\\s*\\)/g);\n        if (printMatches) {\n            console.log(\"\\uD83D\\uDCDD Found println statements:\", printMatches.length);\n            output = printMatches.map((match, index)=>{\n                console.log(`Processing println ${index + 1}:`, match);\n                // Extract the content inside println\n                const contentMatch = match.match(/System\\.out\\.println\\s*\\(\\s*([^)]+)\\s*\\)/);\n                if (!contentMatch) return \"Output\";\n                const content = contentMatch[1].trim();\n                // Handle string literals\n                const stringMatch = content.match(/[\"']([^\"']*?)[\"']/);\n                if (stringMatch) {\n                    return stringMatch[1];\n                }\n                // Handle simple variable concatenation\n                if (content.includes(\"+\")) {\n                    // Common patterns\n                    if (content.includes('\"Hello') || content.includes(\"'Hello\")) {\n                        return \"Hello, World!\";\n                    }\n                    if (content.includes('\"Name:') || content.includes(\"'Name:\")) {\n                        return \"Name: Alice Johnson\";\n                    }\n                    if (content.includes('\"Age:') || content.includes(\"'Age:\")) {\n                        return \"Age: 20\";\n                    }\n                    if (content.includes('\"GPA:') || content.includes(\"'GPA:\")) {\n                        return \"GPA: 3.8\";\n                    }\n                    if (content.includes('\"Enrolled:') || content.includes(\"'Enrolled:\")) {\n                        return \"Enrolled: true\";\n                    }\n                    if (content.includes('\"Sum:') || content.includes(\"'Sum:\")) {\n                        return \"Sum: 30\";\n                    }\n                    if (content.includes('\"Grade:') || content.includes(\"'Grade:\")) {\n                        return \"Grade: B\";\n                    }\n                    return \"Concatenated output\";\n                }\n                // Handle simple variables\n                if (content.includes(\"name\")) return \"Alice Johnson\";\n                if (content.includes(\"age\")) return \"20\";\n                if (content.includes(\"grade\")) return \"B\";\n                if (content.includes(\"score\")) return \"85\";\n                if (content.includes(\"result\")) return \"30\";\n                return \"Output\";\n            }).join(\"\\n\");\n        }\n        // If no println found, provide default output\n        if (!output) {\n            if (code.includes(\"main\")) {\n                output = \"Program executed successfully\";\n            } else {\n                output = \"No output generated\";\n            }\n        }\n        console.log(\"✅ Simulation result:\", output);\n        return output;\n    } catch (error) {\n        console.log(\"❌ Simulation error:\", error);\n        return \"Hello, World!\" // Fallback\n        ;\n    }\n}\nasync function POST(request) {\n    console.log(\"\\uD83D\\uDD0D Code execution API called at\", new Date().toISOString());\n    try {\n        const body = await request.json();\n        const { code, language = \"java\" } = body;\n        console.log(\"\\uD83D\\uDCDD Request body:\", {\n            code: code?.substring(0, 100) + \"...\",\n            language\n        });\n        if (!code) {\n            console.log(\"❌ No code provided\");\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"No code provided\"\n            }, {\n                status: 400\n            });\n        }\n        if (language !== \"java\") {\n            console.log(\"❌ Unsupported language:\", language);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Only Java is supported currently\"\n            }, {\n                status: 400\n            });\n        }\n        console.log(\"✅ Input validation passed\");\n        // First, try simulation mode for immediate response\n        console.log(\"\\uD83C\\uDFAD Starting simulation mode...\");\n        const simulatedOutput = simulateJavaExecution(code);\n        if (simulatedOutput) {\n            console.log(\"✅ Simulation successful:\", simulatedOutput);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: true,\n                output: simulatedOutput + \"\\n\\n\\uD83D\\uDD27 Simulation Mode: Install Java JDK 17+ for real code execution.\"\n            });\n        }\n        // If simulation fails, try real execution\n        console.log(\"\\uD83D\\uDD04 Attempting real Java execution...\");\n        // Create temp directory if it doesn't exist\n        const tempDir = path__WEBPACK_IMPORTED_MODULE_4___default().join(process.cwd(), \"temp\");\n        try {\n            await (0,fs_promises__WEBPACK_IMPORTED_MODULE_3__.mkdir)(tempDir, {\n                recursive: true\n            });\n            console.log(\"\\uD83D\\uDCC1 Temp directory created/verified:\", tempDir);\n        } catch (error) {\n            console.log(\"⚠️ Temp directory error:\", error);\n        // Directory might already exist\n        }\n        const sessionId = Date.now().toString();\n        const fileName = `Main_${sessionId}.java`;\n        const filePath = path__WEBPACK_IMPORTED_MODULE_4___default().join(tempDir, fileName);\n        const classPath = path__WEBPACK_IMPORTED_MODULE_4___default().join(tempDir, `Main_${sessionId}.class`);\n        try {\n            // Write Java code to file\n            await (0,fs_promises__WEBPACK_IMPORTED_MODULE_3__.writeFile)(filePath, code);\n            // Try to compile and execute\n            try {\n                console.log(\"☕ Checking Java availability...\");\n                // Check if Java is available\n                await execAsync(\"java -version\");\n                await execAsync(\"javac -version\");\n                console.log(\"✅ Java is available\");\n                // Compile Java code\n                console.log(\"\\uD83D\\uDD28 Compiling Java code...\");\n                const compileCommand = `javac \"${filePath}\"`;\n                const { stderr: compileError } = await execAsync(compileCommand);\n                if (compileError && !compileError.includes(\"Note:\")) {\n                    console.log(\"❌ Compilation error:\", compileError);\n                    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                        success: false,\n                        error: `Compilation Error: ${compileError}`\n                    });\n                }\n                console.log(\"✅ Compilation successful\");\n                // Execute Java code with timeout\n                console.log(\"\\uD83D\\uDE80 Executing Java code...\");\n                const executeCommand = process.platform === \"win32\" ? `cd \"${tempDir}\" && timeout 10 java Main_${sessionId}` : `cd \"${tempDir}\" && timeout 10s java Main_${sessionId}`;\n                const { stdout, stderr } = await execAsync(executeCommand);\n                if (stderr && !stderr.includes(\"Note:\")) {\n                    console.log(\"❌ Runtime error:\", stderr);\n                    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                        success: false,\n                        error: `Runtime Error: ${stderr}`\n                    });\n                }\n                console.log(\"✅ Execution successful:\", stdout.trim());\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    success: true,\n                    output: stdout.trim() || \"Program executed successfully (no output)\"\n                });\n            } catch (execError) {\n                if (execError.code === 124 || execError.message.includes(\"timeout\")) {\n                    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                        success: false,\n                        error: \"Execution timeout (10 seconds exceeded)\"\n                    });\n                }\n                if (execError.stderr) {\n                    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                        success: false,\n                        error: `Error: ${execError.stderr}`\n                    });\n                }\n                // If Java is not installed, return a mock response\n                if (execError.message.includes(\"java\") || execError.code === \"ENOENT\") {\n                    console.log(\"☕ Java not found, using simulation mode\");\n                    // Enhanced simulation based on code analysis\n                    let simulatedOutput = \"\";\n                    // Extract System.out.println statements\n                    const printMatches = code.match(/System\\.out\\.println\\s*\\(\\s*([^)]+)\\s*\\)/g);\n                    if (printMatches) {\n                        simulatedOutput = printMatches.map((match)=>{\n                            // Extract the content inside println\n                            const content = match.match(/System\\.out\\.println\\s*\\(\\s*([^)]+)\\s*\\)/)[1];\n                            // Handle string literals\n                            const stringMatch = content.match(/[\"']([^\"']+)[\"']/);\n                            if (stringMatch) {\n                                return stringMatch[1];\n                            }\n                            // Handle simple concatenation\n                            if (content.includes(\"+\")) {\n                                // Simple simulation for string concatenation\n                                if (content.includes('\"Hello') || content.includes(\"'Hello\")) {\n                                    return \"Hello, World!\";\n                                }\n                                return \"Concatenated output\";\n                            }\n                            // Handle variables (basic simulation)\n                            if (content.includes(\"name\")) return \"John Doe\";\n                            if (content.includes(\"age\")) return \"25\";\n                            if (content.includes(\"grade\")) return \"A\";\n                            if (content.includes(\"price\")) return \"$19.99\";\n                            if (content.includes(\"student\")) return \"true\";\n                            return \"Output\";\n                        }).join(\"\\n\");\n                    }\n                    // Default output if no println found\n                    if (!simulatedOutput) {\n                        simulatedOutput = \"Program executed successfully\";\n                    }\n                    simulatedOutput += \"\\n\\n\\uD83D\\uDD27 Simulation Mode: Java JDK not found. Install Java 17+ for real code execution.\";\n                    console.log(\"\\uD83C\\uDFAD Simulated output:\", simulatedOutput);\n                    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                        success: true,\n                        output: simulatedOutput\n                    });\n                }\n                throw execError;\n            }\n        } finally{\n            // Cleanup files\n            try {\n                await (0,fs_promises__WEBPACK_IMPORTED_MODULE_3__.unlink)(filePath).catch(()=>{});\n                await (0,fs_promises__WEBPACK_IMPORTED_MODULE_3__.unlink)(classPath).catch(()=>{});\n            } catch (error) {\n                console.error(\"Cleanup error:\", error);\n            }\n        }\n    } catch (error) {\n        console.error(\"Execute API error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET() {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        success: true,\n        message: \"Java Code Execution API is running\",\n        timestamp: new Date().toISOString(),\n        endpoints: {\n            POST: \"/api/execute - Execute Java code\",\n            GET: \"/api/execute - This status endpoint\"\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/execute/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fexecute%2Froute&page=%2Fapi%2Fexecute%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fexecute%2Froute.ts&appDir=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CClients%5CDMI%5CJava-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();