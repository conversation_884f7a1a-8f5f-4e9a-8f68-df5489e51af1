/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/lessons/[id]/page";
exports.ids = ["app/lessons/[id]/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flessons%2F%5Bid%5D%2Fpage&page=%2Flessons%2F%5Bid%5D%2Fpage&appPaths=%2Flessons%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Flessons%2F%5Bid%5D%2Fpage.tsx&appDir=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CClients%5CDMI%5CJava-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flessons%2F%5Bid%5D%2Fpage&page=%2Flessons%2F%5Bid%5D%2Fpage&appPaths=%2Flessons%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Flessons%2F%5Bid%5D%2Fpage.tsx&appDir=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CClients%5CDMI%5CJava-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'lessons',\n        {\n        children: [\n        '[id]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/lessons/[id]/page.tsx */ \"(rsc)/./src/app/lessons/[id]/page.tsx\")), \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/lessons/[id]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/lessons/[id]/page\",\n        pathname: \"/lessons/[id]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flessons%2F%5Bid%5D%2Fpage&page=%2Flessons%2F%5Bid%5D%2Fpage&appPaths=%2Flessons%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Flessons%2F%5Bid%5D%2Fpage.tsx&appDir=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CClients%5CDMI%5CJava-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Ccomponents%5CProviders.tsx&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Ccomponents%5CProviders.tsx&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Providers.tsx */ \"(ssr)/./src/components/Providers.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q0NsaWVudHMlNUNETUklNUNKYXZhLWFwcCU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNmb250JTVDZ29vZ2xlJTVDdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPUQlM0ElNUNDbGllbnRzJTVDRE1JJTVDSmF2YS1hcHAlNUNub2RlX21vZHVsZXMlNUNyZWFjdC1ob3QtdG9hc3QlNUNkaXN0JTVDaW5kZXgubWpzJm1vZHVsZXM9RCUzQSU1Q0NsaWVudHMlNUNETUklNUNKYXZhLWFwcCU1Q3NyYyU1Q2FwcCU1Q2dsb2JhbHMuY3NzJm1vZHVsZXM9RCUzQSU1Q0NsaWVudHMlNUNETUklNUNKYXZhLWFwcCU1Q3NyYyU1Q2NvbXBvbmVudHMlNUNQcm92aWRlcnMudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzTUFBK0c7QUFDL0ciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9qYXZhLWxlYXJuaW5nLXBsYXRmb3JtLz80ZjhmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcQ2xpZW50c1xcXFxETUlcXFxcSmF2YS1hcHBcXFxcbm9kZV9tb2R1bGVzXFxcXHJlYWN0LWhvdC10b2FzdFxcXFxkaXN0XFxcXGluZGV4Lm1qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcQ2xpZW50c1xcXFxETUlcXFxcSmF2YS1hcHBcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcUHJvdmlkZXJzLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Ccomponents%5CProviders.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp%5Clessons%5C%5Bid%5D%5Cpage.tsx&server=true!":
/*!******************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp%5Clessons%5C%5Bid%5D%5Cpage.tsx&server=true! ***!
  \******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/lessons/[id]/page.tsx */ \"(ssr)/./src/app/lessons/[id]/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q0NsaWVudHMlNUNETUklNUNKYXZhLWFwcCU1Q3NyYyU1Q2FwcCU1Q2xlc3NvbnMlNUMlNUJpZCU1RCU1Q3BhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2phdmEtbGVhcm5pbmctcGxhdGZvcm0vP2VmOTgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxDbGllbnRzXFxcXERNSVxcXFxKYXZhLWFwcFxcXFxzcmNcXFxcYXBwXFxcXGxlc3NvbnNcXFxcW2lkXVxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp%5Clessons%5C%5Bid%5D%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/lessons/[id]/page.tsx":
/*!***************************************!*\
  !*** ./src/app/lessons/[id]/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LessonPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_CodeEditor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/CodeEditor */ \"(ssr)/./src/components/CodeEditor.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction LessonPage() {\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [lesson, setLesson] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"theory\");\n    const [userCode, setUserCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isCompleted, setIsCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showHints, setShowHints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Function to get lesson data based on ID\n    const getLessonData = (id)=>{\n        const lessons = {\n            \"1\": {\n                id: \"1\",\n                title: \"Java Basics: Variables and Data Types\",\n                description: \"Learn about variables, primitive data types, and how to declare and use them in Java.\",\n                difficulty: \"Beginner\",\n                duration: \"30 min\",\n                content: {\n                    theory: `\n# Variables and Data Types in Java\n\n## What are Variables?\nVariables are containers that store data values. In Java, every variable has a specific type that determines what kind of data it can hold.\n\n## Primitive Data Types\nJava has 8 primitive data types:\n\n### Numeric Types:\n- **byte**: 8-bit signed integer (-128 to 127)\n- **short**: 16-bit signed integer (-32,768 to 32,767)\n- **int**: 32-bit signed integer (-2^31 to 2^31-1)\n- **long**: 64-bit signed integer (-2^63 to 2^63-1)\n- **float**: 32-bit floating point\n- **double**: 64-bit floating point\n\n### Other Types:\n- **char**: 16-bit Unicode character\n- **boolean**: true or false\n\n## Variable Declaration\nTo declare a variable in Java:\n\\`\\`\\`java\ndataType variableName = value;\n\\`\\`\\`\n\n## Naming Rules\n- Must start with a letter, underscore, or dollar sign\n- Cannot start with a number\n- Case-sensitive\n- Cannot use Java keywords\n- Use camelCase convention\n          `,\n                    example: `public class VariableExample {\n    public static void main(String[] args) {\n        // Integer variables\n        int age = 25;\n        long population = 7800000000L;\n\n        // Floating point variables\n        double price = 19.99;\n        float temperature = 98.6f;\n\n        // Character and boolean\n        char grade = 'A';\n        boolean isStudent = true;\n\n        // String (reference type)\n        String name = \"John Doe\";\n\n        // Print all variables\n        System.out.println(\"Name: \" + name);\n        System.out.println(\"Age: \" + age);\n        System.out.println(\"Grade: \" + grade);\n        System.out.println(\"Is Student: \" + isStudent);\n        System.out.println(\"Price: $\" + price);\n    }\n}`,\n                    exercise: {\n                        description: `Create a program that declares variables for a student's information and prints them out.\n\n**Requirements:**\n1. Declare a String variable for the student's name\n2. Declare an int variable for the student's age\n3. Declare a double variable for the student's GPA\n4. Declare a boolean variable for enrollment status\n5. Print all the information in a formatted way\n\n**Expected format:**\nStudent: [name]\nAge: [age]\nGPA: [gpa]\nEnrolled: [status]`,\n                        starterCode: `public class StudentInfo {\n    public static void main(String[] args) {\n        // TODO: Declare variables for student information\n\n\n        // TODO: Print the student information\n\n    }\n}`,\n                        expectedOutput: `Student: Alice Johnson\nAge: 20\nGPA: 3.8\nEnrolled: true`,\n                        hints: [\n                            \"Use String for the student's name\",\n                            \"Use int for age, double for GPA, and boolean for enrollment status\",\n                            \"Use System.out.println() to print each line\",\n                            \"You can concatenate strings using the + operator\"\n                        ]\n                    }\n                },\n                nextLessonId: \"2\",\n                prevLessonId: undefined\n            },\n            \"2\": {\n                id: \"2\",\n                title: \"Control Structures: If Statements and Loops\",\n                description: \"Master conditional statements and loops to control program flow.\",\n                difficulty: \"Beginner\",\n                duration: \"45 min\",\n                content: {\n                    theory: `\n# Control Structures in Java\n\nControl structures allow you to control the flow of execution in your Java programs. They include conditional statements and loops.\n\n## If Statements\nIf statements execute code based on boolean conditions.\n\n### Basic If Statement:\n\\`\\`\\`java\nif (condition) {\n    // code to execute if condition is true\n}\n\\`\\`\\`\n\n### If-Else Statement:\n\\`\\`\\`java\nif (condition) {\n    // code if true\n} else {\n    // code if false\n}\n\\`\\`\\`\n\n### If-Else If-Else Chain:\n\\`\\`\\`java\nif (condition1) {\n    // code if condition1 is true\n} else if (condition2) {\n    // code if condition2 is true\n} else {\n    // code if all conditions are false\n}\n\\`\\`\\`\n\n## For Loops\nFor loops repeat code a specific number of times.\n\n### Basic For Loop:\n\\`\\`\\`java\nfor (initialization; condition; increment) {\n    // code to repeat\n}\n\\`\\`\\`\n\n### Enhanced For Loop (for arrays):\n\\`\\`\\`java\nfor (dataType variable : array) {\n    // code using variable\n}\n\\`\\`\\`\n\n## While Loops\nWhile loops repeat code while a condition is true.\n\n### While Loop:\n\\`\\`\\`java\nwhile (condition) {\n    // code to repeat\n    // don't forget to update condition!\n}\n\\`\\`\\`\n\n### Do-While Loop:\n\\`\\`\\`java\ndo {\n    // code to repeat at least once\n} while (condition);\n\\`\\`\\`\n\n## Switch Cases\nSwitch statements provide an alternative to multiple if-else statements.\n\n\\`\\`\\`java\nswitch (variable) {\n    case value1:\n        // code for value1\n        break;\n    case value2:\n        // code for value2\n        break;\n    default:\n        // default code\n        break;\n}\n\\`\\`\\`\n          `,\n                    example: `public class ControlStructuresDemo {\n    public static void main(String[] args) {\n        // If Statements Example\n        int score = 85;\n        System.out.println(\"=== If Statements ===\");\n\n        if (score >= 90) {\n            System.out.println(\"Grade: A - Excellent!\");\n        } else if (score >= 80) {\n            System.out.println(\"Grade: B - Good job!\");\n        } else if (score >= 70) {\n            System.out.println(\"Grade: C - Average\");\n        } else {\n            System.out.println(\"Grade: F - Need improvement\");\n        }\n\n        // For Loop Example\n        System.out.println(\"\\\\n=== For Loop ===\");\n        System.out.println(\"Counting from 1 to 5:\");\n        for (int i = 1; i <= 5; i++) {\n            System.out.println(\"Count: \" + i);\n        }\n\n        // While Loop Example\n        System.out.println(\"\\\\n=== While Loop ===\");\n        System.out.println(\"Countdown:\");\n        int countdown = 3;\n        while (countdown > 0) {\n            System.out.println(countdown + \"...\");\n            countdown--;\n        }\n        System.out.println(\"Blast off!\");\n\n        // Switch Case Example\n        System.out.println(\"\\\\n=== Switch Case ===\");\n        int dayOfWeek = 3;\n        switch (dayOfWeek) {\n            case 1:\n                System.out.println(\"Monday\");\n                break;\n            case 2:\n                System.out.println(\"Tuesday\");\n                break;\n            case 3:\n                System.out.println(\"Wednesday\");\n                break;\n            case 4:\n                System.out.println(\"Thursday\");\n                break;\n            case 5:\n                System.out.println(\"Friday\");\n                break;\n            default:\n                System.out.println(\"Weekend!\");\n                break;\n        }\n    }\n}`,\n                    exercise: {\n                        description: `Create a program that demonstrates all four control structure topics: If Statements, For Loops, While Loops, and Switch Cases.\n\n**Requirements:**\n1. **If Statements**: Check if a student's grade (0-100) and print the letter grade (A, B, C, D, F)\n2. **For Loops**: Print multiplication table for number 5 (5x1 to 5x10)\n3. **While Loops**: Print numbers from 10 down to 1\n4. **Switch Cases**: Given a month number (1-12), print the season\n\n**Expected output format:**\nGrade: [letter grade]\n=== Multiplication Table ===\n5 x 1 = 5\n5 x 2 = 10\n...\n=== Countdown ===\n10\n9\n...\n1\nSeason: [season name]`,\n                        starterCode: `public class ControlStructuresExercise {\n    public static void main(String[] args) {\n        // Test values\n        int grade = 87;\n        int tableNumber = 5;\n        int month = 6;\n\n        // TODO: 1. If Statements - Check grade and print letter grade\n        // A: 90-100, B: 80-89, C: 70-79, D: 60-69, F: below 60\n\n\n        // TODO: 2. For Loop - Print multiplication table for tableNumber\n        System.out.println(\"=== Multiplication Table ===\");\n\n\n        // TODO: 3. While Loop - Print countdown from 10 to 1\n        System.out.println(\"=== Countdown ===\");\n\n\n        // TODO: 4. Switch Case - Print season based on month\n        // Spring: 3,4,5  Summer: 6,7,8  Fall: 9,10,11  Winter: 12,1,2\n\n    }\n}`,\n                        expectedOutput: `Grade: B\n=== Multiplication Table ===\n5 x 1 = 5\n5 x 2 = 10\n5 x 3 = 15\n5 x 4 = 20\n5 x 5 = 25\n5 x 6 = 30\n5 x 7 = 35\n5 x 8 = 40\n5 x 9 = 45\n5 x 10 = 50\n=== Countdown ===\n10\n9\n8\n7\n6\n5\n4\n3\n2\n1\nSeason: Summer`,\n                        hints: [\n                            \"For if statements: Use >= for grade ranges (90, 80, 70, 60)\",\n                            \"For multiplication: Use for(int i = 1; i <= 10; i++)\",\n                            \"For countdown: Use while loop with int counter = 10; counter >= 1; counter--\",\n                            \"For switch: Use cases 3,4,5 for Spring; 6,7,8 for Summer, etc.\"\n                        ]\n                    }\n                },\n                nextLessonId: \"3\",\n                prevLessonId: \"1\"\n            },\n            \"3\": {\n                id: \"3\",\n                title: \"Methods and Functions\",\n                description: \"Learn how to create reusable code with methods and understand parameters.\",\n                difficulty: \"Beginner\",\n                duration: \"40 min\",\n                content: {\n                    theory: `\n# Methods and Functions in Java\n\nMethods are blocks of code that perform specific tasks and can be reused throughout your program.\n\n## Method Declaration\nThe basic syntax for declaring a method:\n\n\\`\\`\\`java\naccessModifier returnType methodName(parameters) {\n    // method body\n    return value; // if returnType is not void\n}\n\\`\\`\\`\n\n### Components:\n- **Access Modifier**: public, private, protected\n- **Return Type**: void, int, String, etc.\n- **Method Name**: follows camelCase convention\n- **Parameters**: input values (optional)\n\n## Method Declaration Examples\n\n### Void Method (no return value):\n\\`\\`\\`java\npublic void printMessage() {\n    System.out.println(\"Hello from method!\");\n}\n\\`\\`\\`\n\n### Method with Return Value:\n\\`\\`\\`java\npublic int addNumbers(int a, int b) {\n    return a + b;\n}\n\\`\\`\\`\n\n## Parameters\nParameters allow you to pass data into methods.\n\n### Single Parameter:\n\\`\\`\\`java\npublic void greetUser(String name) {\n    System.out.println(\"Hello, \" + name + \"!\");\n}\n\\`\\`\\`\n\n### Multiple Parameters:\n\\`\\`\\`java\npublic double calculateArea(double length, double width) {\n    return length * width;\n}\n\\`\\`\\`\n\n## Return Types\nMethods can return different types of data.\n\n### Common Return Types:\n- **void**: No return value\n- **int**: Integer numbers\n- **double**: Decimal numbers\n- **String**: Text\n- **boolean**: true/false\n\n## Method Overloading\nYou can have multiple methods with the same name but different parameters.\n\n\\`\\`\\`java\npublic int add(int a, int b) {\n    return a + b;\n}\n\npublic double add(double a, double b) {\n    return a + b;\n}\n\npublic int add(int a, int b, int c) {\n    return a + b + c;\n}\n\\`\\`\\`\n          `,\n                    example: `public class MethodsDemo {\n\n    // Method Declaration - void method with no parameters\n    public static void printWelcome() {\n        System.out.println(\"Welcome to Java Methods!\");\n        System.out.println(\"=========================\");\n    }\n\n    // Method with Parameters - single parameter\n    public static void greetUser(String userName) {\n        System.out.println(\"Hello, \" + userName + \"! Nice to meet you.\");\n    }\n\n    // Method with Return Type - returns an integer\n    public static int addTwoNumbers(int num1, int num2) {\n        int sum = num1 + num2;\n        return sum;\n    }\n\n    // Method with multiple parameters and return type\n    public static double calculateRectangleArea(double length, double width) {\n        return length * width;\n    }\n\n    // Method Overloading - same name, different parameters\n    public static int multiply(int a, int b) {\n        return a * b;\n    }\n\n    public static double multiply(double a, double b) {\n        return a * b;\n    }\n\n    public static int multiply(int a, int b, int c) {\n        return a * b * c;\n    }\n\n    public static void main(String[] args) {\n        // Calling methods\n        printWelcome();\n\n        greetUser(\"Alice\");\n        greetUser(\"Bob\");\n\n        int result = addTwoNumbers(15, 25);\n        System.out.println(\"15 + 25 = \" + result);\n\n        double area = calculateRectangleArea(5.5, 3.2);\n        System.out.println(\"Rectangle area: \" + area);\n\n        // Method overloading examples\n        System.out.println(\"2 * 3 = \" + multiply(2, 3));\n        System.out.println(\"2.5 * 3.0 = \" + multiply(2.5, 3.0));\n        System.out.println(\"2 * 3 * 4 = \" + multiply(2, 3, 4));\n    }\n}`,\n                    exercise: {\n                        description: `Create a program that demonstrates all four method topics: Method Declaration, Parameters, Return Types, and Method Overloading.\n\n**Requirements:**\n1. **Method Declaration**: Create a void method that prints a welcome message\n2. **Parameters**: Create a method that takes a name and age, prints a personalized message\n3. **Return Types**: Create a method that calculates and returns the square of a number\n4. **Method Overloading**: Create three versions of a \"calculate\" method:\n   - One that adds two integers\n   - One that adds two doubles\n   - One that adds three integers\n\n**Expected output format:**\nWelcome to the Method Exercise!\nHello John, you are 25 years old.\nThe square of 7 is: 49\nSum of 5 and 3: 8\nSum of 2.5 and 3.7: 6.2\nSum of 1, 2, and 3: 6`,\n                        starterCode: `public class MethodsExercise {\n\n    // TODO: 1. Method Declaration - Create a void method called printWelcome()\n\n\n    // TODO: 2. Parameters - Create a method called printPersonInfo(String name, int age)\n\n\n    // TODO: 3. Return Types - Create a method called calculateSquare(int number) that returns int\n\n\n    // TODO: 4. Method Overloading - Create three calculate methods:\n    // - calculate(int a, int b) returns int\n    // - calculate(double a, double b) returns double\n    // - calculate(int a, int b, int c) returns int\n\n\n\n\n    public static void main(String[] args) {\n        // TODO: Call all your methods here with these values:\n        // printWelcome()\n        // printPersonInfo(\"John\", 25)\n        // calculateSquare(7)\n        // calculate(5, 3)\n        // calculate(2.5, 3.7)\n        // calculate(1, 2, 3)\n\n    }\n}`,\n                        expectedOutput: `Welcome to the Method Exercise!\nHello John, you are 25 years old.\nThe square of 7 is: 49\nSum of 5 and 3: 8\nSum of 2.5 and 3.7: 6.2\nSum of 1, 2, and 3: 6`,\n                        hints: [\n                            \"Use 'public static void' for methods that don't return values\",\n                            \"Use 'public static int' for methods that return integers\",\n                            \"Method overloading means same name, different parameter types or counts\",\n                            \"Don't forget to call all methods in main() and print the results\"\n                        ]\n                    }\n                },\n                nextLessonId: \"4\",\n                prevLessonId: \"2\"\n            },\n            \"4\": {\n                id: \"4\",\n                title: \"Object-Oriented Programming: Classes and Objects\",\n                description: \"Introduction to OOP concepts with classes, objects, and encapsulation.\",\n                difficulty: \"Intermediate\",\n                duration: \"60 min\",\n                content: {\n                    theory: `\n# Object-Oriented Programming in Java\n\nObject-Oriented Programming (OOP) is a programming paradigm based on the concept of \"objects\" which contain data and code.\n\n## Classes\nA class is a blueprint or template for creating objects. It defines the properties and behaviors that objects of that type will have.\n\n### Class Declaration:\n\\`\\`\\`java\npublic class ClassName {\n    // fields (attributes)\n    // constructors\n    // methods\n}\n\\`\\`\\`\n\n### Example Class:\n\\`\\`\\`java\npublic class Car {\n    // Fields (attributes)\n    private String brand;\n    private String model;\n    private int year;\n\n    // Constructor\n    public Car(String brand, String model, int year) {\n        this.brand = brand;\n        this.model = model;\n        this.year = year;\n    }\n\n    // Methods\n    public void startEngine() {\n        System.out.println(\"Engine started!\");\n    }\n}\n\\`\\`\\`\n\n## Objects\nAn object is an instance of a class. You create objects using the \\`new\\` keyword.\n\n\\`\\`\\`java\nCar myCar = new Car(\"Toyota\", \"Camry\", 2023);\n\\`\\`\\`\n\n## Constructors\nConstructors are special methods used to initialize objects when they are created.\n\n### Default Constructor:\n\\`\\`\\`java\npublic Car() {\n    // default values\n}\n\\`\\`\\`\n\n### Parameterized Constructor:\n\\`\\`\\`java\npublic Car(String brand, String model, int year) {\n    this.brand = brand;\n    this.model = model;\n    this.year = year;\n}\n\\`\\`\\`\n\n## Encapsulation\nEncapsulation is the practice of keeping fields private and providing public methods to access them.\n\n### Private Fields with Public Methods:\n\\`\\`\\`java\nprivate String name;\n\npublic String getName() {\n    return name;\n}\n\npublic void setName(String name) {\n    this.name = name;\n}\n\\`\\`\\`\n          `,\n                    example: `// Student class demonstrating OOP concepts\nclass Student {\n    // Private fields (Encapsulation)\n    private String name;\n    private int age;\n    private String studentId;\n    private double gpa;\n\n    // Default Constructor\n    public Student() {\n        this.name = \"Unknown\";\n        this.age = 0;\n        this.studentId = \"000000\";\n        this.gpa = 0.0;\n    }\n\n    // Parameterized Constructor\n    public Student(String name, int age, String studentId, double gpa) {\n        this.name = name;\n        this.age = age;\n        this.studentId = studentId;\n        this.gpa = gpa;\n    }\n\n    // Getter methods (Encapsulation)\n    public String getName() {\n        return name;\n    }\n\n    public int getAge() {\n        return age;\n    }\n\n    public String getStudentId() {\n        return studentId;\n    }\n\n    public double getGpa() {\n        return gpa;\n    }\n\n    // Setter methods (Encapsulation)\n    public void setName(String name) {\n        this.name = name;\n    }\n\n    public void setAge(int age) {\n        if (age > 0) {\n            this.age = age;\n        }\n    }\n\n    public void setGpa(double gpa) {\n        if (gpa >= 0.0 && gpa <= 4.0) {\n            this.gpa = gpa;\n        }\n    }\n\n    // Method to display student information\n    public void displayInfo() {\n        System.out.println(\"Student Information:\");\n        System.out.println(\"Name: \" + name);\n        System.out.println(\"Age: \" + age);\n        System.out.println(\"Student ID: \" + studentId);\n        System.out.println(\"GPA: \" + gpa);\n    }\n\n    // Method to check if student is on honor roll\n    public boolean isHonorRoll() {\n        return gpa >= 3.5;\n    }\n}\n\npublic class OOPDemo {\n    public static void main(String[] args) {\n        // Creating objects using different constructors\n        Student student1 = new Student();\n        Student student2 = new Student(\"Alice Johnson\", 20, \"STU001\", 3.8);\n\n        System.out.println(\"=== Student 1 (Default Constructor) ===\");\n        student1.displayInfo();\n\n        System.out.println(\"\\\\n=== Student 2 (Parameterized Constructor) ===\");\n        student2.displayInfo();\n\n        // Using setter methods to modify student1\n        student1.setName(\"Bob Smith\");\n        student1.setAge(19);\n        student1.setGpa(3.2);\n\n        System.out.println(\"\\\\n=== Student 1 (After modifications) ===\");\n        student1.displayInfo();\n\n        // Using methods\n        System.out.println(\"\\\\n=== Honor Roll Status ===\");\n        System.out.println(student1.getName() + \" honor roll: \" + student1.isHonorRoll());\n        System.out.println(student2.getName() + \" honor roll: \" + student2.isHonorRoll());\n    }\n}`,\n                    exercise: {\n                        description: `Create a Book class that demonstrates all four OOP topics: Classes, Objects, Constructors, and Encapsulation.\n\n**Requirements:**\n1. **Classes**: Create a Book class with private fields: title, author, pages, price\n2. **Objects**: Create two Book objects in main method\n3. **Constructors**: Implement both default and parameterized constructors\n4. **Encapsulation**: Create getter and setter methods for all fields, plus a displayInfo() method\n\n**Expected output format:**\n=== Book 1 (Default Constructor) ===\nTitle: Unknown\nAuthor: Unknown\nPages: 0\nPrice: $0.0\n\n=== Book 2 (Parameterized Constructor) ===\nTitle: Java Programming\nAuthor: John Doe\nPages: 500\nPrice: $49.99\n\n=== Book 1 (After modifications) ===\nTitle: Python Basics\nAuthor: Jane Smith\nPages: 300\nPrice: $29.99`,\n                        starterCode: `// TODO: Create Book class here\nclass Book {\n    // TODO: 1. Classes - Add private fields: title, author, pages, price\n\n\n    // TODO: 2. Constructors - Create default constructor\n\n\n    // TODO: 2. Constructors - Create parameterized constructor\n\n\n    // TODO: 3. Encapsulation - Create getter methods\n\n\n\n\n\n    // TODO: 3. Encapsulation - Create setter methods\n\n\n\n\n\n    // TODO: Create displayInfo() method\n\n}\n\npublic class BookDemo {\n    public static void main(String[] args) {\n        // TODO: 4. Objects - Create two Book objects\n        // book1 using default constructor\n        // book2 using parameterized constructor with values:\n        // \"Java Programming\", \"John Doe\", 500, 49.99\n\n\n        // TODO: Display both books\n\n\n        // TODO: Modify book1 using setters:\n        // title: \"Python Basics\", author: \"Jane Smith\", pages: 300, price: 29.99\n\n\n        // TODO: Display book1 again\n\n    }\n}`,\n                        expectedOutput: `=== Book 1 (Default Constructor) ===\nTitle: Unknown\nAuthor: Unknown\nPages: 0\nPrice: $0.0\n\n=== Book 2 (Parameterized Constructor) ===\nTitle: Java Programming\nAuthor: John Doe\nPages: 500\nPrice: $49.99\n\n=== Book 1 (After modifications) ===\nTitle: Python Basics\nAuthor: Jane Smith\nPages: 300\nPrice: $29.99`,\n                        hints: [\n                            \"Use private fields and public methods for encapsulation\",\n                            \"Default constructor should set default values like 'Unknown' and 0\",\n                            \"Parameterized constructor should accept all four parameters\",\n                            \"Getter methods return field values, setter methods update them\"\n                        ]\n                    }\n                },\n                nextLessonId: \"5\",\n                prevLessonId: \"3\"\n            },\n            \"5\": {\n                id: \"5\",\n                title: \"Arrays and Collections\",\n                description: \"Work with arrays and Java collections like ArrayList and HashMap.\",\n                difficulty: \"Intermediate\",\n                duration: \"50 min\",\n                content: {\n                    theory: `\n# Arrays and Collections in Java\n\nArrays and Collections are used to store multiple values in Java.\n\n## Arrays\nArrays store multiple values of the same type in a fixed-size sequential collection.\n\n### Array Declaration and Initialization:\n\\`\\`\\`java\n// Declaration\nint[] numbers;\nString[] names;\n\n// Initialization\nint[] numbers = new int[5];  // Array of 5 integers\nString[] names = {\"Alice\", \"Bob\", \"Charlie\"};  // Array with values\n\\`\\`\\`\n\n### Accessing Array Elements:\n\\`\\`\\`java\nint[] numbers = {10, 20, 30, 40, 50};\nSystem.out.println(numbers[0]);  // Prints 10\nnumbers[1] = 25;  // Changes second element to 25\n\\`\\`\\`\n\n### Array Properties:\n\\`\\`\\`java\nint[] numbers = {1, 2, 3, 4, 5};\nSystem.out.println(numbers.length);  // Prints 5\n\\`\\`\\`\n\n## ArrayList\nArrayList is a resizable array implementation that can grow and shrink dynamically.\n\n### ArrayList Declaration and Initialization:\n\\`\\`\\`java\nimport java.util.ArrayList;\n\nArrayList<String> names = new ArrayList<>();\nArrayList<Integer> numbers = new ArrayList<>();\n\\`\\`\\`\n\n### ArrayList Methods:\n\\`\\`\\`java\nArrayList<String> fruits = new ArrayList<>();\nfruits.add(\"Apple\");        // Add element\nfruits.add(\"Banana\");\nfruits.get(0);             // Get element at index 0\nfruits.set(0, \"Orange\");   // Replace element at index 0\nfruits.remove(1);          // Remove element at index 1\nfruits.size();             // Get size\n\\`\\`\\`\n\n## HashMap\nHashMap stores key-value pairs and allows fast lookup by key.\n\n### HashMap Declaration and Usage:\n\\`\\`\\`java\nimport java.util.HashMap;\n\nHashMap<String, Integer> ages = new HashMap<>();\nages.put(\"Alice\", 25);     // Add key-value pair\nages.put(\"Bob\", 30);\nages.get(\"Alice\");         // Get value by key (returns 25)\nages.remove(\"Bob\");        // Remove key-value pair\n\\`\\`\\`\n\n## Iteration\nYou can iterate through arrays and collections using loops.\n\n### For-each Loop:\n\\`\\`\\`java\n// Arrays\nint[] numbers = {1, 2, 3, 4, 5};\nfor (int num : numbers) {\n    System.out.println(num);\n}\n\n// ArrayList\nArrayList<String> names = new ArrayList<>();\nfor (String name : names) {\n    System.out.println(name);\n}\n\\`\\`\\`\n          `,\n                    example: `import java.util.ArrayList;\nimport java.util.HashMap;\n\npublic class ArraysCollectionsDemo {\n    public static void main(String[] args) {\n\n        // === ARRAYS ===\n        System.out.println(\"=== Arrays Demo ===\");\n\n        // Array declaration and initialization\n        int[] scores = {85, 92, 78, 96, 88};\n        String[] subjects = new String[3];\n        subjects[0] = \"Math\";\n        subjects[1] = \"Science\";\n        subjects[2] = \"English\";\n\n        // Accessing and modifying arrays\n        System.out.println(\"First score: \" + scores[0]);\n        System.out.println(\"Array length: \" + scores.length);\n        scores[0] = 90;  // Modify first element\n\n        // Iteration through array\n        System.out.println(\"All scores:\");\n        for (int score : scores) {\n            System.out.println(\"Score: \" + score);\n        }\n\n        // === ARRAYLIST ===\n        System.out.println(\"\\\\n=== ArrayList Demo ===\");\n\n        ArrayList<String> students = new ArrayList<>();\n\n        // Adding elements\n        students.add(\"Alice\");\n        students.add(\"Bob\");\n        students.add(\"Charlie\");\n        students.add(\"Diana\");\n\n        System.out.println(\"Number of students: \" + students.size());\n        System.out.println(\"First student: \" + students.get(0));\n\n        // Modifying ArrayList\n        students.set(1, \"Robert\");  // Change Bob to Robert\n        students.remove(\"Charlie\"); // Remove Charlie\n\n        // Iteration through ArrayList\n        System.out.println(\"Current students:\");\n        for (String student : students) {\n            System.out.println(\"Student: \" + student);\n        }\n\n        // === HASHMAP ===\n        System.out.println(\"\\\\n=== HashMap Demo ===\");\n\n        HashMap<String, Integer> studentGrades = new HashMap<>();\n\n        // Adding key-value pairs\n        studentGrades.put(\"Alice\", 95);\n        studentGrades.put(\"Robert\", 87);\n        studentGrades.put(\"Diana\", 92);\n\n        // Accessing values\n        System.out.println(\"Alice's grade: \" + studentGrades.get(\"Alice\"));\n        System.out.println(\"Number of grades: \" + studentGrades.size());\n\n        // Iteration through HashMap\n        System.out.println(\"All grades:\");\n        for (String name : studentGrades.keySet()) {\n            System.out.println(name + \": \" + studentGrades.get(name));\n        }\n\n        // Check if key exists\n        if (studentGrades.containsKey(\"Alice\")) {\n            System.out.println(\"Alice's grade found!\");\n        }\n    }\n}`,\n                    exercise: {\n                        description: `Create a program that demonstrates all four collection topics: Arrays, ArrayList, HashMap, and Iteration.\n\n**Requirements:**\n1. **Arrays**: Create an array of 5 integers, display them, and calculate their sum\n2. **ArrayList**: Create an ArrayList of fruits, add 4 fruits, remove one, and display the list\n3. **HashMap**: Create a HashMap of countries and their capitals, add 3 pairs, and display them\n4. **Iteration**: Use for-each loops to iterate through all collections\n\n**Expected output format:**\n=== Arrays ===\nNumbers: 10 20 30 40 50\nSum: 150\n\n=== ArrayList ===\nFruits: [Apple, Banana, Orange]\n\n=== HashMap ===\nCountries and Capitals:\nUSA: Washington DC\nFrance: Paris\nJapan: Tokyo`,\n                        starterCode: `import java.util.ArrayList;\nimport java.util.HashMap;\n\npublic class CollectionsExercise {\n    public static void main(String[] args) {\n\n        // TODO: 1. Arrays - Create array with values {10, 20, 30, 40, 50}\n        System.out.println(\"=== Arrays ===\");\n\n\n        // TODO: Display array elements and calculate sum using iteration\n\n\n        // TODO: 2. ArrayList - Create ArrayList of fruits\n        System.out.println(\"\\\\n=== ArrayList ===\");\n\n\n        // TODO: Add fruits: \"Apple\", \"Banana\", \"Cherry\", \"Orange\"\n        // TODO: Remove \"Cherry\"\n        // TODO: Display remaining fruits\n\n\n        // TODO: 3. HashMap - Create HashMap of countries and capitals\n        System.out.println(\"\\\\n=== HashMap ===\");\n\n\n        // TODO: Add pairs: \"USA\"->\"Washington DC\", \"France\"->\"Paris\", \"Japan\"->\"Tokyo\"\n        // TODO: Display all pairs using iteration\n\n    }\n}`,\n                        expectedOutput: `=== Arrays ===\nNumbers: 10 20 30 40 50\nSum: 150\n\n=== ArrayList ===\nFruits: [Apple, Banana, Orange]\n\n=== HashMap ===\nCountries and Capitals:\nUSA: Washington DC\nFrance: Paris\nJapan: Tokyo`,\n                        hints: [\n                            \"Use for-each loop: for(int num : array) to iterate arrays\",\n                            \"ArrayList methods: add(), remove(), toString() for display\",\n                            \"HashMap methods: put(), keySet() for iteration\",\n                            \"Calculate sum by adding each array element in the loop\"\n                        ]\n                    }\n                },\n                nextLessonId: \"6\",\n                prevLessonId: \"4\"\n            },\n            \"6\": {\n                id: \"6\",\n                title: \"Exception Handling\",\n                description: \"Learn to handle errors gracefully with try-catch blocks and custom exceptions.\",\n                difficulty: \"Advanced\",\n                duration: \"45 min\",\n                content: {\n                    theory: `\n# Exception Handling in Java\n\nException handling allows you to manage runtime errors gracefully and prevent your program from crashing.\n\n## Try-Catch Blocks\nThe basic structure for handling exceptions:\n\n\\`\\`\\`java\ntry {\n    // Code that might throw an exception\n} catch (ExceptionType e) {\n    // Handle the exception\n}\n\\`\\`\\`\n\n### Example:\n\\`\\`\\`java\ntry {\n    int result = 10 / 0;  // This will throw ArithmeticException\n} catch (ArithmeticException e) {\n    System.out.println(\"Cannot divide by zero!\");\n}\n\\`\\`\\`\n\n## Finally Block\nThe finally block always executes, whether an exception occurs or not:\n\n\\`\\`\\`java\ntry {\n    // risky code\n} catch (Exception e) {\n    // handle exception\n} finally {\n    // cleanup code - always runs\n    System.out.println(\"Cleanup completed\");\n}\n\\`\\`\\`\n\n## Custom Exceptions\nYou can create your own exception classes:\n\n\\`\\`\\`java\nclass CustomException extends Exception {\n    public CustomException(String message) {\n        super(message);\n    }\n}\n\\`\\`\\`\n\n## Throws Keyword\nUse throws to declare that a method might throw an exception:\n\n\\`\\`\\`java\npublic void riskyMethod() throws IOException {\n    // code that might throw IOException\n}\n\\`\\`\\`\n\n### Calling methods that throw exceptions:\n\\`\\`\\`java\ntry {\n    riskyMethod();\n} catch (IOException e) {\n    System.out.println(\"IO Error: \" + e.getMessage());\n}\n\\`\\`\\`\n\n## Common Exception Types\n- **ArithmeticException**: Division by zero\n- **NullPointerException**: Using null reference\n- **ArrayIndexOutOfBoundsException**: Invalid array index\n- **NumberFormatException**: Invalid number conversion\n- **IOException**: Input/output operations\n- **FileNotFoundException**: File not found\n\n## Best Practices\n1. Catch specific exceptions rather than generic Exception\n2. Always clean up resources in finally block\n3. Don't ignore exceptions - at least log them\n4. Use meaningful error messages\n          `,\n                    example: `// Custom Exception class\nclass InvalidAgeException extends Exception {\n    public InvalidAgeException(String message) {\n        super(message);\n    }\n}\n\npublic class ExceptionHandlingDemo {\n\n    // Method that throws custom exception\n    public static void validateAge(int age) throws InvalidAgeException {\n        if (age < 0 || age > 150) {\n            throw new InvalidAgeException(\"Age must be between 0 and 150. Got: \" + age);\n        }\n        System.out.println(\"Valid age: \" + age);\n    }\n\n    // Method demonstrating different exception types\n    public static void demonstrateExceptions() {\n\n        // Try-Catch with ArithmeticException\n        System.out.println(\"=== ArithmeticException Demo ===\");\n        try {\n            int result = 10 / 0;\n            System.out.println(\"Result: \" + result);\n        } catch (ArithmeticException e) {\n            System.out.println(\"Error: Cannot divide by zero!\");\n        }\n\n        // Try-Catch with ArrayIndexOutOfBoundsException\n        System.out.println(\"\\\\n=== ArrayIndexOutOfBoundsException Demo ===\");\n        try {\n            int[] numbers = {1, 2, 3};\n            System.out.println(\"Element at index 5: \" + numbers[5]);\n        } catch (ArrayIndexOutOfBoundsException e) {\n            System.out.println(\"Error: Array index out of bounds!\");\n        }\n\n        // Try-Catch with NumberFormatException\n        System.out.println(\"\\\\n=== NumberFormatException Demo ===\");\n        try {\n            String text = \"abc\";\n            int number = Integer.parseInt(text);\n            System.out.println(\"Number: \" + number);\n        } catch (NumberFormatException e) {\n            System.out.println(\"Error: Cannot convert '\" + e.getMessage().split(\"\\\"\")[1] + \"' to number!\");\n        }\n    }\n\n    public static void main(String[] args) {\n\n        // Demonstrate common exceptions\n        demonstrateExceptions();\n\n        // Custom Exception with Throws\n        System.out.println(\"\\\\n=== Custom Exception Demo ===\");\n\n        int[] testAges = {25, -5, 200, 30};\n\n        for (int age : testAges) {\n            try {\n                validateAge(age);\n            } catch (InvalidAgeException e) {\n                System.out.println(\"Custom Error: \" + e.getMessage());\n            }\n        }\n\n        // Finally Block Demo\n        System.out.println(\"\\\\n=== Finally Block Demo ===\");\n        try {\n            System.out.println(\"Executing risky operation...\");\n            int result = 10 / 2;  // This works fine\n            System.out.println(\"Result: \" + result);\n        } catch (Exception e) {\n            System.out.println(\"Exception caught: \" + e.getMessage());\n        } finally {\n            System.out.println(\"Finally block: Cleanup completed!\");\n        }\n\n        System.out.println(\"\\\\nProgram completed successfully!\");\n    }\n}`,\n                    exercise: {\n                        description: `Create a program that demonstrates all four exception handling topics: Try-Catch, Finally Block, Custom Exceptions, and Throws.\n\n**Requirements:**\n1. **Try-Catch**: Handle division by zero and array index out of bounds\n2. **Finally Block**: Use finally to print cleanup message\n3. **Custom Exceptions**: Create InvalidScoreException for scores outside 0-100 range\n4. **Throws**: Create a method that throws the custom exception\n\n**Expected output format:**\n=== Try-Catch Demo ===\nError: Division by zero!\nError: Array index out of bounds!\n\n=== Custom Exception Demo ===\nValid score: 85\nError: Score must be between 0 and 100. Got: 150\n\n=== Finally Block Demo ===\nProcessing...\nFinally: Cleanup completed!`,\n                        starterCode: `// TODO: 1. Custom Exceptions - Create InvalidScoreException class\n\n\npublic class ExceptionExercise {\n\n    // TODO: 2. Throws - Create validateScore method that throws InvalidScoreException\n    // Method should accept int score and throw exception if score < 0 or score > 100\n\n\n    public static void main(String[] args) {\n\n        System.out.println(\"=== Try-Catch Demo ===\");\n\n        // TODO: 3. Try-Catch - Handle division by zero\n        try {\n\n        } catch () {\n\n        }\n\n        // TODO: 3. Try-Catch - Handle array index out of bounds\n        // Create array {1, 2, 3} and try to access index 5\n        try {\n\n        } catch () {\n\n        }\n\n        System.out.println(\"\\\\n=== Custom Exception Demo ===\");\n\n        // TODO: Test validateScore with values 85 and 150\n\n\n        System.out.println(\"\\\\n=== Finally Block Demo ===\");\n\n        // TODO: 4. Finally Block - Use try-catch-finally\n        try {\n            System.out.println(\"Processing...\");\n            // Some operation\n        } catch (Exception e) {\n            System.out.println(\"Error occurred\");\n        } finally {\n            // TODO: Print cleanup message\n        }\n    }\n}`,\n                        expectedOutput: `=== Try-Catch Demo ===\nError: Division by zero!\nError: Array index out of bounds!\n\n=== Custom Exception Demo ===\nValid score: 85\nError: Score must be between 0 and 100. Got: 150\n\n=== Finally Block Demo ===\nProcessing...\nFinally: Cleanup completed!`,\n                        hints: [\n                            \"Custom exception: class InvalidScoreException extends Exception\",\n                            \"Throws: public static void validateScore(int score) throws InvalidScoreException\",\n                            \"Try-catch: catch (ArithmeticException e) and catch (ArrayIndexOutOfBoundsException e)\",\n                            \"Finally block always executes after try-catch\"\n                        ]\n                    }\n                },\n                nextLessonId: undefined,\n                prevLessonId: \"5\"\n            }\n        };\n        return lessons[id] || lessons[\"1\"] // Default to lesson 1 if not found\n        ;\n    };\n    const mockLesson = getLessonData(params.id);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simulate loading lesson data\n        setLesson(mockLesson);\n        setUserCode(mockLesson.content.exercise.starterCode);\n        // Calculate progress based on current tab\n        const tabProgress = {\n            theory: 33,\n            example: 66,\n            exercise: 100\n        };\n        setProgress(tabProgress[currentTab]);\n    }, [\n        currentTab\n    ]);\n    const handleCodeChange = (code)=>{\n        setUserCode(code);\n    };\n    const handleRunCode = async (code)=>{\n        // Simulate code execution\n        try {\n            // In a real app, this would send the code to a backend service\n            const mockOutput = `Student: Alice Johnson\nAge: 20\nGPA: 3.8\nEnrolled: true`;\n            // Check if the output matches expected output\n            if (lesson && mockOutput.trim() === lesson.content.exercise.expectedOutput.trim()) {\n                setIsCompleted(true);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(\"Congratulations! Exercise completed successfully!\");\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Output doesn't match expected result. Keep trying!\");\n            }\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Error executing code\");\n        }\n    };\n    const handleNextLesson = ()=>{\n        if (lesson?.nextLessonId) {\n            router.push(`/lessons/${lesson.nextLessonId}`);\n        }\n    };\n    const handlePrevLesson = ()=>{\n        if (lesson?.prevLessonId) {\n            router.push(`/lessons/${lesson.prevLessonId}`);\n        }\n    };\n    if (!lesson) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                lineNumber: 1457,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n            lineNumber: 1456,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/dashboard\",\n                                        className: \"flex items-center text-gray-600 hover:text-primary-600 transition-colors mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-5 h-5 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1470,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Back to Dashboard\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1469,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-8 h-8 text-primary-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1473,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-xl font-bold text-gray-900\",\n                                        children: \"JavaLearn\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1474,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1468,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Progress: \",\n                                            progress,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1477,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-32 bg-gray-200 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-primary-600 h-2 rounded-full transition-all duration-300\",\n                                            style: {\n                                                width: `${progress}%`\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1481,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1480,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1476,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                        lineNumber: 1467,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                    lineNumber: 1466,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                lineNumber: 1465,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        className: \"mb-8\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                            children: lesson.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1501,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: lesson.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1502,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `px-2 py-1 rounded-full text-xs ${lesson.difficulty === \"Beginner\" ? \"bg-green-100 text-green-800\" : lesson.difficulty === \"Intermediate\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"}`,\n                                                    children: lesson.difficulty\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1504,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1512,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        lesson.duration\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1511,\n                                                    columnNumber: 17\n                                                }, this),\n                                                isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex items-center text-green-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1517,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Completed\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1516,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1503,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1500,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1499,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"-mb-px flex space-x-8\",\n                                    children: [\n                                        {\n                                            id: \"theory\",\n                                            label: \"Theory\",\n                                            icon: _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                                        },\n                                        {\n                                            id: \"example\",\n                                            label: \"Example\",\n                                            icon: _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                                        },\n                                        {\n                                            id: \"exercise\",\n                                            label: \"Exercise\",\n                                            icon: _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n                                        }\n                                    ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setCurrentTab(tab.id),\n                                            className: `flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors ${currentTab === tab.id ? \"border-primary-500 text-primary-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1542,\n                                                    columnNumber: 19\n                                                }, this),\n                                                tab.label\n                                            ]\n                                        }, tab.id, true, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1533,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1527,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1526,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                        lineNumber: 1493,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            duration: 0.4\n                        },\n                        children: [\n                            currentTab === \"theory\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card p-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"prose max-w-none\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        dangerouslySetInnerHTML: {\n                                            __html: lesson.content.theory.replace(/\\n/g, \"<br>\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1560,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1559,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1558,\n                                columnNumber: 13\n                            }, this),\n                            currentTab === \"example\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                            children: \"Example Code\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1568,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CodeEditor__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            initialCode: lesson.content.example,\n                                            readOnly: true,\n                                            height: \"500px\",\n                                            showOutput: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1569,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1567,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1566,\n                                columnNumber: 13\n                            }, this),\n                            currentTab === \"exercise\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900\",\n                                                        children: \"Practice Exercise\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1583,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowHints(!showHints),\n                                                        className: \"btn-secondary text-sm\",\n                                                        children: showHints ? \"Hide Hints\" : \"Show Hints\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1584,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1582,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"prose max-w-none mb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    dangerouslySetInnerHTML: {\n                                                        __html: lesson.content.exercise.description.replace(/\\n/g, \"<br>\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1592,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1591,\n                                                columnNumber: 17\n                                            }, this),\n                                            showHints && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-yellow-800 mb-2\",\n                                                        children: \"Hints:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1597,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"text-yellow-700 text-sm space-y-1\",\n                                                        children: lesson.content.exercise.hints.map((hint, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    \"• \",\n                                                                    hint\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 1600,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1598,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1596,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1581,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CodeEditor__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        initialCode: lesson.content.exercise.starterCode,\n                                        onCodeChange: handleCodeChange,\n                                        onRun: handleRunCode,\n                                        height: \"400px\",\n                                        showOutput: true,\n                                        expectedOutput: lesson.content.exercise.expectedOutput\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1607,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1580,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, currentTab, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                        lineNumber: 1551,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mt-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handlePrevLesson,\n                                disabled: !lesson.prevLessonId,\n                                className: \"flex items-center btn-secondary disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-4 h-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1626,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Previous Lesson\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1621,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4\",\n                                children: [\n                                    currentTab !== \"exercise\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            const nextTab = currentTab === \"theory\" ? \"example\" : \"exercise\";\n                                            setCurrentTab(nextTab);\n                                        },\n                                        className: \"btn-primary\",\n                                        children: [\n                                            \"Continue\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 ml-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1640,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1632,\n                                        columnNumber: 15\n                                    }, this),\n                                    currentTab === \"exercise\" && isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNextLesson,\n                                        disabled: !lesson.nextLessonId,\n                                        className: \"flex items-center btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: [\n                                            \"Next Lesson\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 ml-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1651,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1645,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1630,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                        lineNumber: 1620,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                lineNumber: 1491,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n        lineNumber: 1463,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/lessons/[id]/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/CodeEditor.tsx":
/*!***************************************!*\
  !*** ./src/components/CodeEditor.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CodeEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @monaco-editor/react */ \"(ssr)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Download,Play,RotateCcw,Settings,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Download,Play,RotateCcw,Settings,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Download,Play,RotateCcw,Settings,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Download,Play,RotateCcw,Settings,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Download,Play,RotateCcw,Settings,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Download,Play,RotateCcw,Settings,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction CodeEditor({ initialCode = '// Write your Java code here\\npublic class Main {\\n    public static void main(String[] args) {\\n        System.out.println(\"Hello, World!\");\\n    }\\n}', language = \"java\", theme = \"vs-dark\", height = \"400px\", readOnly = false, onCodeChange, onRun, showOutput = true, expectedOutput }) {\n    const [code, setCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialCode);\n    const [output, setOutput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isRunning, setIsRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [executionTime, setExecutionTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleEditorDidMount = (editor, monaco)=>{\n        editorRef.current = editor;\n        // Configure Java language features\n        monaco.languages.java?.setDiagnosticsOptions({\n            noSemanticValidation: false,\n            noSyntaxValidation: false\n        });\n        // Add custom keyboard shortcuts\n        editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.Enter, ()=>{\n            handleRunCode();\n        });\n    };\n    const handleCodeChange = (value)=>{\n        const newCode = value || \"\";\n        setCode(newCode);\n        onCodeChange?.(newCode);\n    };\n    const handleRunCode = async ()=>{\n        if (isRunning) return;\n        setIsRunning(true);\n        setOutput(\"\");\n        const startTime = Date.now();\n        try {\n            // Call the parent's onRun function if provided\n            if (onRun) {\n                onRun(code);\n                setIsRunning(false);\n                return;\n            }\n            console.log(\"Executing code:\", code.substring(0, 100) + \"...\");\n            // Default execution logic\n            const response = await fetch(\"/api/execute\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    code,\n                    language: \"java\"\n                })\n            });\n            console.log(\"Response status:\", response.status);\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            const result = await response.json();\n            console.log(\"Execution result:\", result);\n            const endTime = Date.now();\n            setExecutionTime(endTime - startTime);\n            if (result.success) {\n                setOutput(result.output || \"Program executed successfully (no output)\");\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"Code executed successfully!\");\n                // Check if output matches expected output\n                if (expectedOutput && result.output?.trim() === expectedOutput.trim()) {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"Correct! Your output matches the expected result.\");\n                } else if (expectedOutput) {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"Output doesn't match expected result. Try again!\");\n                }\n            } else {\n                setOutput(`Error: ${result.error}`);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"Compilation or runtime error occurred\");\n            }\n        } catch (error) {\n            console.error(\"Code execution error:\", error);\n            setOutput(`Error: Failed to execute code. ${error instanceof Error ? error.message : \"Please try again.\"}`);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"Failed to execute code\");\n        } finally{\n            setIsRunning(false);\n        }\n    };\n    const handleStopExecution = ()=>{\n        setIsRunning(false);\n        setOutput(\"Execution stopped by user\");\n    };\n    const handleResetCode = ()=>{\n        setCode(initialCode);\n        setOutput(\"\");\n        setExecutionTime(null);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"Code reset to initial state\");\n    };\n    const handleCopyCode = ()=>{\n        navigator.clipboard.writeText(code);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"Code copied to clipboard\");\n    };\n    const handleDownloadCode = ()=>{\n        const blob = new Blob([\n            code\n        ], {\n            type: \"text/java\"\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = \"Main.java\";\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"Code downloaded as Main.java\");\n    };\n    const formatCode = ()=>{\n        if (editorRef.current) {\n            editorRef.current.getAction(\"editor.action.formatDocument\").run();\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"Code formatted\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"code-editor bg-white rounded-lg shadow-lg overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 text-white px-4 py-2 flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium\",\n                                children: \"Java Editor\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this),\n                            executionTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-300\",\n                                children: [\n                                    \"Executed in \",\n                                    executionTime,\n                                    \"ms\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCopyCode,\n                                className: \"p-1 hover:bg-gray-700 rounded transition-colors\",\n                                title: \"Copy code\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleDownloadCode,\n                                className: \"p-1 hover:bg-gray-700 rounded transition-colors\",\n                                title: \"Download code\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: formatCode,\n                                className: \"p-1 hover:bg-gray-700 rounded transition-colors\",\n                                title: \"Format code\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleResetCode,\n                                className: \"p-1 hover:bg-gray-700 rounded transition-colors\",\n                                title: \"Reset code\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, this),\n                            isRunning ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleStopExecution,\n                                className: \"flex items-center space-x-1 bg-red-600 hover:bg-red-700 px-3 py-1 rounded text-sm transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Stop\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleRunCode,\n                                className: \"flex items-center space-x-1 bg-green-600 hover:bg-green-700 px-3 py-1 rounded text-sm transition-colors\",\n                                disabled: readOnly,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Run\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        height: height,\n                        language: language,\n                        theme: theme,\n                        value: code,\n                        onChange: handleCodeChange,\n                        onMount: handleEditorDidMount,\n                        options: {\n                            readOnly,\n                            minimap: {\n                                enabled: false\n                            },\n                            fontSize: 14,\n                            lineNumbers: \"on\",\n                            roundedSelection: false,\n                            scrollBeyondLastLine: false,\n                            automaticLayout: true,\n                            tabSize: 4,\n                            insertSpaces: true,\n                            wordWrap: \"on\",\n                            contextmenu: true,\n                            selectOnLineNumbers: true,\n                            glyphMargin: true,\n                            folding: true,\n                            foldingStrategy: \"indentation\",\n                            showFoldingControls: \"always\",\n                            bracketPairColorization: {\n                                enabled: true\n                            },\n                            guides: {\n                                bracketPairs: true,\n                                indentation: true\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this),\n                    isRunning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg p-4 flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-700\",\n                                    children: \"Executing code...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, this),\n            showOutput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-100 px-4 py-2 border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium text-gray-700\",\n                            children: \"Output\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 bg-gray-900 text-green-400 font-mono text-sm min-h-[100px] max-h-[200px] overflow-y-auto\",\n                        children: output ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"whitespace-pre-wrap\",\n                            children: output\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-500\",\n                            children: 'Click \"Run\" to execute your code'\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                lineNumber: 266,\n                columnNumber: 9\n            }, this),\n            expectedOutput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-50 px-4 py-2 border-b border-blue-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium text-blue-700\",\n                            children: \"Expected Output\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 bg-blue-900 text-blue-100 font-mono text-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"whitespace-pre-wrap\",\n                            children: expectedOutput\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                lineNumber: 282,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n        lineNumber: 160,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/CodeEditor.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Providers.tsx":
/*!**************************************!*\
  !*** ./src/components/Providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\Providers.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Qcm92aWRlcnMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUVpRDtBQU8xQyxTQUFTQyxVQUFVLEVBQUVDLFFBQVEsRUFBa0I7SUFDcEQscUJBQ0UsOERBQUNGLDREQUFlQTtrQkFDYkU7Ozs7OztBQUdQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vamF2YS1sZWFybmluZy1wbGF0Zm9ybS8uL3NyYy9jb21wb25lbnRzL1Byb3ZpZGVycy50c3g/YjAxOSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0J1xuaW1wb3J0IHsgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnXG5cbmludGVyZmFjZSBQcm92aWRlcnNQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFByb3ZpZGVycyh7IGNoaWxkcmVuIH06IFByb3ZpZGVyc1Byb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPFNlc3Npb25Qcm92aWRlcj5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L1Nlc3Npb25Qcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlNlc3Npb25Qcm92aWRlciIsIlByb3ZpZGVycyIsImNoaWxkcmVuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Providers.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e05ba106f0bb\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vamF2YS1sZWFybmluZy1wbGF0Zm9ybS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/ZTAyMyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImUwNWJhMTA2ZjBiYlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_Providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Providers */ \"(rsc)/./src/components/Providers.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\nconst metadata = {\n    title: \"Java Learning Platform - Interactive Programming Education\",\n    description: \"Learn Java programming through interactive lessons, real-time code execution, quizzes, and earn certificates.\",\n    keywords: \"Java, programming, education, interactive, coding, tutorials, certificates\",\n    authors: [\n        {\n            name: \"Java Learning Platform\"\n        }\n    ]\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 4000,\n                            style: {\n                                background: \"#363636\",\n                                color: \"#fff\"\n                            },\n                            success: {\n                                duration: 3000,\n                                iconTheme: {\n                                    primary: \"#22c55e\",\n                                    secondary: \"#fff\"\n                                }\n                            },\n                            error: {\n                                duration: 5000,\n                                iconTheme: {\n                                    primary: \"#ef4444\",\n                                    secondary: \"#fff\"\n                                }\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFNTUE7QUFKZ0I7QUFDNEI7QUFDVDtBQUlsQyxNQUFNRyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0lBQ2JDLFVBQVU7SUFDVkMsU0FBUztRQUFDO1lBQUVDLE1BQU07UUFBeUI7S0FBRTtBQUMvQyxFQUFDO0FBRU0sTUFBTUMsV0FBVztJQUN0QkMsT0FBTztJQUNQQyxjQUFjO0FBQ2hCLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBV2pCLCtKQUFlO3NCQUM5Qiw0RUFBQ0MsNERBQVNBOztvQkFDUFk7a0NBQ0QsOERBQUNYLG9EQUFPQTt3QkFDTmdCLFVBQVM7d0JBQ1RDLGNBQWM7NEJBQ1pDLFVBQVU7NEJBQ1ZDLE9BQU87Z0NBQ0xDLFlBQVk7Z0NBQ1pDLE9BQU87NEJBQ1Q7NEJBQ0FDLFNBQVM7Z0NBQ1BKLFVBQVU7Z0NBQ1ZLLFdBQVc7b0NBQ1RDLFNBQVM7b0NBQ1RDLFdBQVc7Z0NBQ2I7NEJBQ0Y7NEJBQ0FDLE9BQU87Z0NBQ0xSLFVBQVU7Z0NBQ1ZLLFdBQVc7b0NBQ1RDLFNBQVM7b0NBQ1RDLFdBQVc7Z0NBQ2I7NEJBQ0Y7d0JBQ0Y7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNWiIsInNvdXJjZXMiOlsid2VicGFjazovL2phdmEtbGVhcm5pbmctcGxhdGZvcm0vLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCB7IEludGVyIH0gZnJvbSAnbmV4dC9mb250L2dvb2dsZSdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcbmltcG9ydCB7IFByb3ZpZGVycyB9IGZyb20gJ0AvY29tcG9uZW50cy9Qcm92aWRlcnMnXG5pbXBvcnQgeyBUb2FzdGVyIH0gZnJvbSAncmVhY3QtaG90LXRvYXN0J1xuXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogWydsYXRpbiddIH0pXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnSmF2YSBMZWFybmluZyBQbGF0Zm9ybSAtIEludGVyYWN0aXZlIFByb2dyYW1taW5nIEVkdWNhdGlvbicsXG4gIGRlc2NyaXB0aW9uOiAnTGVhcm4gSmF2YSBwcm9ncmFtbWluZyB0aHJvdWdoIGludGVyYWN0aXZlIGxlc3NvbnMsIHJlYWwtdGltZSBjb2RlIGV4ZWN1dGlvbiwgcXVpenplcywgYW5kIGVhcm4gY2VydGlmaWNhdGVzLicsXG4gIGtleXdvcmRzOiAnSmF2YSwgcHJvZ3JhbW1pbmcsIGVkdWNhdGlvbiwgaW50ZXJhY3RpdmUsIGNvZGluZywgdHV0b3JpYWxzLCBjZXJ0aWZpY2F0ZXMnLFxuICBhdXRob3JzOiBbeyBuYW1lOiAnSmF2YSBMZWFybmluZyBQbGF0Zm9ybScgfV0sXG59XG5cbmV4cG9ydCBjb25zdCB2aWV3cG9ydCA9IHtcbiAgd2lkdGg6ICdkZXZpY2Utd2lkdGgnLFxuICBpbml0aWFsU2NhbGU6IDEsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PlxuICAgICAgICA8UHJvdmlkZXJzPlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICA8VG9hc3RlciBcbiAgICAgICAgICAgIHBvc2l0aW9uPVwidG9wLXJpZ2h0XCJcbiAgICAgICAgICAgIHRvYXN0T3B0aW9ucz17e1xuICAgICAgICAgICAgICBkdXJhdGlvbjogNDAwMCxcbiAgICAgICAgICAgICAgc3R5bGU6IHtcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAnIzM2MzYzNicsXG4gICAgICAgICAgICAgICAgY29sb3I6ICcjZmZmJyxcbiAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgc3VjY2Vzczoge1xuICAgICAgICAgICAgICAgIGR1cmF0aW9uOiAzMDAwLFxuICAgICAgICAgICAgICAgIGljb25UaGVtZToge1xuICAgICAgICAgICAgICAgICAgcHJpbWFyeTogJyMyMmM1NWUnLFxuICAgICAgICAgICAgICAgICAgc2Vjb25kYXJ5OiAnI2ZmZicsXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgZXJyb3I6IHtcbiAgICAgICAgICAgICAgICBkdXJhdGlvbjogNTAwMCxcbiAgICAgICAgICAgICAgICBpY29uVGhlbWU6IHtcbiAgICAgICAgICAgICAgICAgIHByaW1hcnk6ICcjZWY0NDQ0JyxcbiAgICAgICAgICAgICAgICAgIHNlY29uZGFyeTogJyNmZmYnLFxuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB9fVxuICAgICAgICAgIC8+XG4gICAgICAgIDwvUHJvdmlkZXJzPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbImludGVyIiwiUHJvdmlkZXJzIiwiVG9hc3RlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImtleXdvcmRzIiwiYXV0aG9ycyIsIm5hbWUiLCJ2aWV3cG9ydCIsIndpZHRoIiwiaW5pdGlhbFNjYWxlIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIiwicG9zaXRpb24iLCJ0b2FzdE9wdGlvbnMiLCJkdXJhdGlvbiIsInN0eWxlIiwiYmFja2dyb3VuZCIsImNvbG9yIiwic3VjY2VzcyIsImljb25UaGVtZSIsInByaW1hcnkiLCJzZWNvbmRhcnkiLCJlcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/lessons/[id]/page.tsx":
/*!***************************************!*\
  !*** ./src/app/lessons/[id]/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Clients\DMI\Java-app\src\app\lessons\[id]\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/Providers.tsx":
/*!**************************************!*\
  !*** ./src/components/Providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Clients\DMI\Java-app\src\components\Providers.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Clients\DMI\Java-app\src\components\Providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/framer-motion","vendor-chunks/lucide-react","vendor-chunks/react-hot-toast","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/@monaco-editor","vendor-chunks/state-local"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flessons%2F%5Bid%5D%2Fpage&page=%2Flessons%2F%5Bid%5D%2Fpage&appPaths=%2Flessons%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Flessons%2F%5Bid%5D%2Fpage.tsx&appDir=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CClients%5CDMI%5CJava-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();