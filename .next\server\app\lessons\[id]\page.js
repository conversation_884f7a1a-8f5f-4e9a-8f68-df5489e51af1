/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/lessons/[id]/page";
exports.ids = ["app/lessons/[id]/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flessons%2F%5Bid%5D%2Fpage&page=%2Flessons%2F%5Bid%5D%2Fpage&appPaths=%2Flessons%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Flessons%2F%5Bid%5D%2Fpage.tsx&appDir=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CClients%5CDMI%5CJava-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flessons%2F%5Bid%5D%2Fpage&page=%2Flessons%2F%5Bid%5D%2Fpage&appPaths=%2Flessons%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Flessons%2F%5Bid%5D%2Fpage.tsx&appDir=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CClients%5CDMI%5CJava-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'lessons',\n        {\n        children: [\n        '[id]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/lessons/[id]/page.tsx */ \"(rsc)/./src/app/lessons/[id]/page.tsx\")), \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/lessons/[id]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/lessons/[id]/page\",\n        pathname: \"/lessons/[id]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flessons%2F%5Bid%5D%2Fpage&page=%2Flessons%2F%5Bid%5D%2Fpage&appPaths=%2Flessons%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Flessons%2F%5Bid%5D%2Fpage.tsx&appDir=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CClients%5CDMI%5CJava-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Ccomponents%5CProviders.tsx&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Ccomponents%5CProviders.tsx&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Providers.tsx */ \"(ssr)/./src/components/Providers.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q0NsaWVudHMlNUNETUklNUNKYXZhLWFwcCU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNmb250JTVDZ29vZ2xlJTVDdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPUQlM0ElNUNDbGllbnRzJTVDRE1JJTVDSmF2YS1hcHAlNUNub2RlX21vZHVsZXMlNUNyZWFjdC1ob3QtdG9hc3QlNUNkaXN0JTVDaW5kZXgubWpzJm1vZHVsZXM9RCUzQSU1Q0NsaWVudHMlNUNETUklNUNKYXZhLWFwcCU1Q3NyYyU1Q2FwcCU1Q2dsb2JhbHMuY3NzJm1vZHVsZXM9RCUzQSU1Q0NsaWVudHMlNUNETUklNUNKYXZhLWFwcCU1Q3NyYyU1Q2NvbXBvbmVudHMlNUNQcm92aWRlcnMudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzTUFBK0c7QUFDL0ciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9qYXZhLWxlYXJuaW5nLXBsYXRmb3JtLz80ZjhmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcQ2xpZW50c1xcXFxETUlcXFxcSmF2YS1hcHBcXFxcbm9kZV9tb2R1bGVzXFxcXHJlYWN0LWhvdC10b2FzdFxcXFxkaXN0XFxcXGluZGV4Lm1qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcQ2xpZW50c1xcXFxETUlcXFxcSmF2YS1hcHBcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcUHJvdmlkZXJzLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Ccomponents%5CProviders.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp%5Clessons%5C%5Bid%5D%5Cpage.tsx&server=true!":
/*!******************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp%5Clessons%5C%5Bid%5D%5Cpage.tsx&server=true! ***!
  \******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/lessons/[id]/page.tsx */ \"(ssr)/./src/app/lessons/[id]/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q0NsaWVudHMlNUNETUklNUNKYXZhLWFwcCU1Q3NyYyU1Q2FwcCU1Q2xlc3NvbnMlNUMlNUJpZCU1RCU1Q3BhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2phdmEtbGVhcm5pbmctcGxhdGZvcm0vP2VmOTgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxDbGllbnRzXFxcXERNSVxcXFxKYXZhLWFwcFxcXFxzcmNcXFxcYXBwXFxcXGxlc3NvbnNcXFxcW2lkXVxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp%5Clessons%5C%5Bid%5D%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/lessons/[id]/page.tsx":
/*!***************************************!*\
  !*** ./src/app/lessons/[id]/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LessonPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_CodeEditor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/CodeEditor */ \"(ssr)/./src/components/CodeEditor.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction LessonPage() {\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [lesson, setLesson] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"theory\");\n    const [userCode, setUserCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isCompleted, setIsCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showHints, setShowHints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Function to get lesson data based on ID\n    const getLessonData = (id)=>{\n        const lessons = {\n            \"1\": {\n                id: \"1\",\n                title: \"Java Basics: Variables and Data Types\",\n                description: \"Learn about variables, primitive data types, and how to declare and use them in Java.\",\n                difficulty: \"Beginner\",\n                duration: \"30 min\",\n                content: {\n                    theory: `\n# Variables and Data Types in Java\n\n## What are Variables?\nVariables are containers that store data values. In Java, every variable has a specific type that determines what kind of data it can hold.\n\n## Primitive Data Types\nJava has 8 primitive data types:\n\n### Numeric Types:\n- **byte**: 8-bit signed integer (-128 to 127)\n- **short**: 16-bit signed integer (-32,768 to 32,767)\n- **int**: 32-bit signed integer (-2^31 to 2^31-1)\n- **long**: 64-bit signed integer (-2^63 to 2^63-1)\n- **float**: 32-bit floating point\n- **double**: 64-bit floating point\n\n### Other Types:\n- **char**: 16-bit Unicode character\n- **boolean**: true or false\n\n## Variable Declaration\nTo declare a variable in Java:\n\\`\\`\\`java\ndataType variableName = value;\n\\`\\`\\`\n\n## Naming Rules\n- Must start with a letter, underscore, or dollar sign\n- Cannot start with a number\n- Case-sensitive\n- Cannot use Java keywords\n- Use camelCase convention\n          `,\n                    example: `public class VariableExample {\n    public static void main(String[] args) {\n        // Integer variables\n        int age = 25;\n        long population = 7800000000L;\n\n        // Floating point variables\n        double price = 19.99;\n        float temperature = 98.6f;\n\n        // Character and boolean\n        char grade = 'A';\n        boolean isStudent = true;\n\n        // String (reference type)\n        String name = \"John Doe\";\n\n        // Print all variables\n        System.out.println(\"Name: \" + name);\n        System.out.println(\"Age: \" + age);\n        System.out.println(\"Grade: \" + grade);\n        System.out.println(\"Is Student: \" + isStudent);\n        System.out.println(\"Price: $\" + price);\n    }\n}`,\n                    exercise: {\n                        description: `Create a program that declares variables for a student's information and prints them out.\n\n**Requirements:**\n1. Declare a String variable for the student's name\n2. Declare an int variable for the student's age\n3. Declare a double variable for the student's GPA\n4. Declare a boolean variable for enrollment status\n5. Print all the information in a formatted way\n\n**Expected format:**\nStudent: [name]\nAge: [age]\nGPA: [gpa]\nEnrolled: [status]`,\n                        starterCode: `public class StudentInfo {\n    public static void main(String[] args) {\n        // TODO: Declare variables for student information\n\n\n        // TODO: Print the student information\n\n    }\n}`,\n                        expectedOutput: `Student: Alice Johnson\nAge: 20\nGPA: 3.8\nEnrolled: true`,\n                        hints: [\n                            \"Use String for the student's name\",\n                            \"Use int for age, double for GPA, and boolean for enrollment status\",\n                            \"Use System.out.println() to print each line\",\n                            \"You can concatenate strings using the + operator\"\n                        ]\n                    }\n                },\n                nextLessonId: \"2\",\n                prevLessonId: undefined\n            },\n            \"2\": {\n                id: \"2\",\n                title: \"Control Structures: If Statements and Loops\",\n                description: \"Master conditional statements and loops to control program flow.\",\n                difficulty: \"Beginner\",\n                duration: \"45 min\",\n                content: {\n                    theory: `\n# Control Structures in Java\n\n## If Statements\nIf statements allow you to execute code conditionally based on boolean expressions.\n\n### Basic If Statement:\n\\`\\`\\`java\nif (condition) {\n    // code to execute if condition is true\n}\n\\`\\`\\`\n\n### If-Else Statement:\n\\`\\`\\`java\nif (condition) {\n    // code if true\n} else {\n    // code if false\n}\n\\`\\`\\`\n\n### If-Else If-Else:\n\\`\\`\\`java\nif (condition1) {\n    // code if condition1 is true\n} else if (condition2) {\n    // code if condition2 is true\n} else {\n    // code if all conditions are false\n}\n\\`\\`\\`\n\n## Loops\nLoops allow you to repeat code multiple times.\n\n### For Loop:\n\\`\\`\\`java\nfor (initialization; condition; increment) {\n    // code to repeat\n}\n\\`\\`\\`\n\n### While Loop:\n\\`\\`\\`java\nwhile (condition) {\n    // code to repeat\n}\n\\`\\`\\`\n\n### Do-While Loop:\n\\`\\`\\`java\ndo {\n    // code to repeat\n} while (condition);\n\\`\\`\\`\n          `,\n                    example: `public class ControlStructures {\n    public static void main(String[] args) {\n        int score = 85;\n\n        // If-else statement\n        if (score >= 90) {\n            System.out.println(\"Grade: A\");\n        } else if (score >= 80) {\n            System.out.println(\"Grade: B\");\n        } else if (score >= 70) {\n            System.out.println(\"Grade: C\");\n        } else {\n            System.out.println(\"Grade: F\");\n        }\n\n        // For loop\n        System.out.println(\"Counting from 1 to 5:\");\n        for (int i = 1; i <= 5; i++) {\n            System.out.println(\"Count: \" + i);\n        }\n\n        // While loop\n        System.out.println(\"Countdown:\");\n        int countdown = 3;\n        while (countdown > 0) {\n            System.out.println(countdown);\n            countdown--;\n        }\n        System.out.println(\"Blast off!\");\n    }\n}`,\n                    exercise: {\n                        description: `Create a program that checks if a number is positive, negative, or zero, and then prints all even numbers from 1 to 10.\n\n**Requirements:**\n1. Declare an int variable with any value\n2. Use if-else statements to check if the number is positive, negative, or zero\n3. Print an appropriate message for each case\n4. Use a for loop to print all even numbers from 1 to 10\n\n**Expected output format:**\nThe number [number] is [positive/negative/zero]\nEven numbers from 1 to 10:\n2\n4\n6\n8\n10`,\n                        starterCode: `public class NumberChecker {\n    public static void main(String[] args) {\n        // TODO: Declare a number variable\n\n        // TODO: Check if number is positive, negative, or zero\n\n\n        // TODO: Print even numbers from 1 to 10\n\n    }\n}`,\n                        expectedOutput: `The number 7 is positive\nEven numbers from 1 to 10:\n2\n4\n6\n8\n10`,\n                        hints: [\n                            \"Use if (number > 0), else if (number < 0), else for zero check\",\n                            \"For even numbers, use i % 2 == 0 to check if a number is even\",\n                            \"Use a for loop from 1 to 10 and check each number\",\n                            \"Remember to use System.out.println() for each output line\"\n                        ]\n                    }\n                },\n                nextLessonId: \"3\",\n                prevLessonId: \"1\"\n            }\n        };\n        return lessons[id] || lessons[\"1\"] // Default to lesson 1 if not found\n        ;\n    };\n    const mockLesson = getLessonData(params.id);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simulate loading lesson data\n        setLesson(mockLesson);\n        setUserCode(mockLesson.content.exercise.starterCode);\n        // Calculate progress based on current tab\n        const tabProgress = {\n            theory: 33,\n            example: 66,\n            exercise: 100\n        };\n        setProgress(tabProgress[currentTab]);\n    }, [\n        currentTab\n    ]);\n    const handleCodeChange = (code)=>{\n        setUserCode(code);\n    };\n    const handleRunCode = async (code)=>{\n        // Simulate code execution\n        try {\n            // In a real app, this would send the code to a backend service\n            const mockOutput = `Student: Alice Johnson\nAge: 20\nGPA: 3.8\nEnrolled: true`;\n            // Check if the output matches expected output\n            if (lesson && mockOutput.trim() === lesson.content.exercise.expectedOutput.trim()) {\n                setIsCompleted(true);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(\"Congratulations! Exercise completed successfully!\");\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Output doesn't match expected result. Keep trying!\");\n            }\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Error executing code\");\n        }\n    };\n    const handleNextLesson = ()=>{\n        if (lesson?.nextLessonId) {\n            router.push(`/lessons/${lesson.nextLessonId}`);\n        }\n    };\n    const handlePrevLesson = ()=>{\n        if (lesson?.prevLessonId) {\n            router.push(`/lessons/${lesson.prevLessonId}`);\n        }\n    };\n    if (!lesson) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                lineNumber: 362,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n            lineNumber: 361,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/dashboard\",\n                                        className: \"flex items-center text-gray-600 hover:text-primary-600 transition-colors mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-5 h-5 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Back to Dashboard\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-8 h-8 text-primary-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-xl font-bold text-gray-900\",\n                                        children: \"JavaLearn\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Progress: \",\n                                            progress,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-32 bg-gray-200 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-primary-600 h-2 rounded-full transition-all duration-300\",\n                                            style: {\n                                                width: `${progress}%`\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                        lineNumber: 372,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                    lineNumber: 371,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                lineNumber: 370,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        className: \"mb-8\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                            children: lesson.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 406,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: lesson.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `px-2 py-1 rounded-full text-xs ${lesson.difficulty === \"Beginner\" ? \"bg-green-100 text-green-800\" : lesson.difficulty === \"Intermediate\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"}`,\n                                                    children: lesson.difficulty\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        lesson.duration\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 17\n                                                }, this),\n                                                isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex items-center text-green-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 422,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Completed\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"-mb-px flex space-x-8\",\n                                    children: [\n                                        {\n                                            id: \"theory\",\n                                            label: \"Theory\",\n                                            icon: _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                                        },\n                                        {\n                                            id: \"example\",\n                                            label: \"Example\",\n                                            icon: _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                                        },\n                                        {\n                                            id: \"exercise\",\n                                            label: \"Exercise\",\n                                            icon: _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n                                        }\n                                    ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setCurrentTab(tab.id),\n                                            className: `flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors ${currentTab === tab.id ? \"border-primary-500 text-primary-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 19\n                                                }, this),\n                                                tab.label\n                                            ]\n                                        }, tab.id, true, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            duration: 0.4\n                        },\n                        children: [\n                            currentTab === \"theory\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card p-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"prose max-w-none\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        dangerouslySetInnerHTML: {\n                                            __html: lesson.content.theory.replace(/\\n/g, \"<br>\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 463,\n                                columnNumber: 13\n                            }, this),\n                            currentTab === \"example\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                            children: \"Example Code\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CodeEditor__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            initialCode: lesson.content.example,\n                                            readOnly: true,\n                                            height: \"500px\",\n                                            showOutput: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 471,\n                                columnNumber: 13\n                            }, this),\n                            currentTab === \"exercise\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900\",\n                                                        children: \"Practice Exercise\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowHints(!showHints),\n                                                        className: \"btn-secondary text-sm\",\n                                                        children: showHints ? \"Hide Hints\" : \"Show Hints\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"prose max-w-none mb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    dangerouslySetInnerHTML: {\n                                                        __html: lesson.content.exercise.description.replace(/\\n/g, \"<br>\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 497,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 17\n                                            }, this),\n                                            showHints && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-yellow-800 mb-2\",\n                                                        children: \"Hints:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"text-yellow-700 text-sm space-y-1\",\n                                                        children: lesson.content.exercise.hints.map((hint, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    \"• \",\n                                                                    hint\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 505,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CodeEditor__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        initialCode: lesson.content.exercise.starterCode,\n                                        onCodeChange: handleCodeChange,\n                                        onRun: handleRunCode,\n                                        height: \"400px\",\n                                        showOutput: true,\n                                        expectedOutput: lesson.content.exercise.expectedOutput\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 485,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, currentTab, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mt-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handlePrevLesson,\n                                disabled: !lesson.prevLessonId,\n                                className: \"flex items-center btn-secondary disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-4 h-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 531,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Previous Lesson\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 526,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4\",\n                                children: [\n                                    currentTab !== \"exercise\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            const nextTab = currentTab === \"theory\" ? \"example\" : \"exercise\";\n                                            setCurrentTab(nextTab);\n                                        },\n                                        className: \"btn-primary\",\n                                        children: [\n                                            \"Continue\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 ml-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 545,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 15\n                                    }, this),\n                                    currentTab === \"exercise\" && isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNextLesson,\n                                        disabled: !lesson.nextLessonId,\n                                        className: \"flex items-center btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: [\n                                            \"Next Lesson\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 ml-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 535,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                        lineNumber: 525,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                lineNumber: 396,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n        lineNumber: 368,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/lessons/[id]/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/CodeEditor.tsx":
/*!***************************************!*\
  !*** ./src/components/CodeEditor.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CodeEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @monaco-editor/react */ \"(ssr)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Download,Play,RotateCcw,Settings,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Download,Play,RotateCcw,Settings,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Download,Play,RotateCcw,Settings,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Download,Play,RotateCcw,Settings,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Download,Play,RotateCcw,Settings,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Download,Play,RotateCcw,Settings,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction CodeEditor({ initialCode = '// Write your Java code here\\npublic class Main {\\n    public static void main(String[] args) {\\n        System.out.println(\"Hello, World!\");\\n    }\\n}', language = \"java\", theme = \"vs-dark\", height = \"400px\", readOnly = false, onCodeChange, onRun, showOutput = true, expectedOutput }) {\n    const [code, setCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialCode);\n    const [output, setOutput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isRunning, setIsRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [executionTime, setExecutionTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleEditorDidMount = (editor, monaco)=>{\n        editorRef.current = editor;\n        // Configure Java language features\n        monaco.languages.java?.setDiagnosticsOptions({\n            noSemanticValidation: false,\n            noSyntaxValidation: false\n        });\n        // Add custom keyboard shortcuts\n        editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.Enter, ()=>{\n            handleRunCode();\n        });\n    };\n    const handleCodeChange = (value)=>{\n        const newCode = value || \"\";\n        setCode(newCode);\n        onCodeChange?.(newCode);\n    };\n    const handleRunCode = async ()=>{\n        if (isRunning) return;\n        setIsRunning(true);\n        setOutput(\"\");\n        const startTime = Date.now();\n        try {\n            // Call the parent's onRun function if provided\n            if (onRun) {\n                onRun(code);\n                setIsRunning(false);\n                return;\n            }\n            console.log(\"Executing code:\", code.substring(0, 100) + \"...\");\n            // Default execution logic\n            const response = await fetch(\"/api/execute\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    code,\n                    language: \"java\"\n                })\n            });\n            console.log(\"Response status:\", response.status);\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            const result = await response.json();\n            console.log(\"Execution result:\", result);\n            const endTime = Date.now();\n            setExecutionTime(endTime - startTime);\n            if (result.success) {\n                setOutput(result.output || \"Program executed successfully (no output)\");\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"Code executed successfully!\");\n                // Check if output matches expected output\n                if (expectedOutput && result.output?.trim() === expectedOutput.trim()) {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"Correct! Your output matches the expected result.\");\n                } else if (expectedOutput) {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"Output doesn't match expected result. Try again!\");\n                }\n            } else {\n                setOutput(`Error: ${result.error}`);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"Compilation or runtime error occurred\");\n            }\n        } catch (error) {\n            console.error(\"Code execution error:\", error);\n            setOutput(`Error: Failed to execute code. ${error instanceof Error ? error.message : \"Please try again.\"}`);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"Failed to execute code\");\n        } finally{\n            setIsRunning(false);\n        }\n    };\n    const handleStopExecution = ()=>{\n        setIsRunning(false);\n        setOutput(\"Execution stopped by user\");\n    };\n    const handleResetCode = ()=>{\n        setCode(initialCode);\n        setOutput(\"\");\n        setExecutionTime(null);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"Code reset to initial state\");\n    };\n    const handleCopyCode = ()=>{\n        navigator.clipboard.writeText(code);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"Code copied to clipboard\");\n    };\n    const handleDownloadCode = ()=>{\n        const blob = new Blob([\n            code\n        ], {\n            type: \"text/java\"\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = \"Main.java\";\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"Code downloaded as Main.java\");\n    };\n    const formatCode = ()=>{\n        if (editorRef.current) {\n            editorRef.current.getAction(\"editor.action.formatDocument\").run();\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"Code formatted\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"code-editor bg-white rounded-lg shadow-lg overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 text-white px-4 py-2 flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium\",\n                                children: \"Java Editor\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this),\n                            executionTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-300\",\n                                children: [\n                                    \"Executed in \",\n                                    executionTime,\n                                    \"ms\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCopyCode,\n                                className: \"p-1 hover:bg-gray-700 rounded transition-colors\",\n                                title: \"Copy code\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleDownloadCode,\n                                className: \"p-1 hover:bg-gray-700 rounded transition-colors\",\n                                title: \"Download code\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: formatCode,\n                                className: \"p-1 hover:bg-gray-700 rounded transition-colors\",\n                                title: \"Format code\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleResetCode,\n                                className: \"p-1 hover:bg-gray-700 rounded transition-colors\",\n                                title: \"Reset code\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, this),\n                            isRunning ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleStopExecution,\n                                className: \"flex items-center space-x-1 bg-red-600 hover:bg-red-700 px-3 py-1 rounded text-sm transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Stop\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleRunCode,\n                                className: \"flex items-center space-x-1 bg-green-600 hover:bg-green-700 px-3 py-1 rounded text-sm transition-colors\",\n                                disabled: readOnly,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Download_Play_RotateCcw_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Run\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        height: height,\n                        language: language,\n                        theme: theme,\n                        value: code,\n                        onChange: handleCodeChange,\n                        onMount: handleEditorDidMount,\n                        options: {\n                            readOnly,\n                            minimap: {\n                                enabled: false\n                            },\n                            fontSize: 14,\n                            lineNumbers: \"on\",\n                            roundedSelection: false,\n                            scrollBeyondLastLine: false,\n                            automaticLayout: true,\n                            tabSize: 4,\n                            insertSpaces: true,\n                            wordWrap: \"on\",\n                            contextmenu: true,\n                            selectOnLineNumbers: true,\n                            glyphMargin: true,\n                            folding: true,\n                            foldingStrategy: \"indentation\",\n                            showFoldingControls: \"always\",\n                            bracketPairColorization: {\n                                enabled: true\n                            },\n                            guides: {\n                                bracketPairs: true,\n                                indentation: true\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this),\n                    isRunning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg p-4 flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-700\",\n                                    children: \"Executing code...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, this),\n            showOutput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-100 px-4 py-2 border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium text-gray-700\",\n                            children: \"Output\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 bg-gray-900 text-green-400 font-mono text-sm min-h-[100px] max-h-[200px] overflow-y-auto\",\n                        children: output ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"whitespace-pre-wrap\",\n                            children: output\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-500\",\n                            children: 'Click \"Run\" to execute your code'\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                lineNumber: 266,\n                columnNumber: 9\n            }, this),\n            expectedOutput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-50 px-4 py-2 border-b border-blue-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium text-blue-700\",\n                            children: \"Expected Output\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 bg-blue-900 text-blue-100 font-mono text-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"whitespace-pre-wrap\",\n                            children: expectedOutput\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n                lineNumber: 282,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\CodeEditor.tsx\",\n        lineNumber: 160,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/CodeEditor.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Providers.tsx":
/*!**************************************!*\
  !*** ./src/components/Providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\Providers.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Qcm92aWRlcnMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUVpRDtBQU8xQyxTQUFTQyxVQUFVLEVBQUVDLFFBQVEsRUFBa0I7SUFDcEQscUJBQ0UsOERBQUNGLDREQUFlQTtrQkFDYkU7Ozs7OztBQUdQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vamF2YS1sZWFybmluZy1wbGF0Zm9ybS8uL3NyYy9jb21wb25lbnRzL1Byb3ZpZGVycy50c3g/YjAxOSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0J1xuaW1wb3J0IHsgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnXG5cbmludGVyZmFjZSBQcm92aWRlcnNQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFByb3ZpZGVycyh7IGNoaWxkcmVuIH06IFByb3ZpZGVyc1Byb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPFNlc3Npb25Qcm92aWRlcj5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L1Nlc3Npb25Qcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlNlc3Npb25Qcm92aWRlciIsIlByb3ZpZGVycyIsImNoaWxkcmVuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Providers.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e05ba106f0bb\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vamF2YS1sZWFybmluZy1wbGF0Zm9ybS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/ZTAyMyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImUwNWJhMTA2ZjBiYlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_Providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Providers */ \"(rsc)/./src/components/Providers.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\nconst metadata = {\n    title: \"Java Learning Platform - Interactive Programming Education\",\n    description: \"Learn Java programming through interactive lessons, real-time code execution, quizzes, and earn certificates.\",\n    keywords: \"Java, programming, education, interactive, coding, tutorials, certificates\",\n    authors: [\n        {\n            name: \"Java Learning Platform\"\n        }\n    ]\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 4000,\n                            style: {\n                                background: \"#363636\",\n                                color: \"#fff\"\n                            },\n                            success: {\n                                duration: 3000,\n                                iconTheme: {\n                                    primary: \"#22c55e\",\n                                    secondary: \"#fff\"\n                                }\n                            },\n                            error: {\n                                duration: 5000,\n                                iconTheme: {\n                                    primary: \"#ef4444\",\n                                    secondary: \"#fff\"\n                                }\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/lessons/[id]/page.tsx":
/*!***************************************!*\
  !*** ./src/app/lessons/[id]/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Clients\DMI\Java-app\src\app\lessons\[id]\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/Providers.tsx":
/*!**************************************!*\
  !*** ./src/components/Providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Clients\DMI\Java-app\src\components\Providers.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Clients\DMI\Java-app\src\components\Providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/react-hot-toast","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/framer-motion","vendor-chunks/lucide-react","vendor-chunks/@monaco-editor","vendor-chunks/state-local"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flessons%2F%5Bid%5D%2Fpage&page=%2Flessons%2F%5Bid%5D%2Fpage&appPaths=%2Flessons%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Flessons%2F%5Bid%5D%2Fpage.tsx&appDir=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CClients%5CDMI%5CJava-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();