// Test and Debug Script for Java Learning Platform
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testAPI() {
  console.log('🔍 Testing Java Learning Platform APIs...\n');

  // Test 1: Test API endpoint
  try {
    console.log('1. Testing /api/test endpoint...');
    const response = await axios.get(`${BASE_URL}/api/test`);
    console.log('✅ Test API Response:', response.data);
  } catch (error) {
    console.log('❌ Test API Error:', error.message);
  }

  // Test 2: Test code execution API
  try {
    console.log('\n2. Testing /api/execute endpoint...');
    const codeToTest = `public class HelloWorld {
    public static void main(String[] args) {
        System.out.println("Hello, World!");
        System.out.println("Testing Java execution");
    }
}`;

    const response = await axios.post(`${BASE_URL}/api/execute`, {
      code: codeToTest,
      language: 'java'
    });
    console.log('✅ Code Execution Response:', response.data);
  } catch (error) {
    console.log('❌ Code Execution Error:', error.message);
    if (error.response) {
      console.log('Error Response:', error.response.data);
    }
  }

  // Test 3: Test authentication API
  try {
    console.log('\n3. Testing authentication...');
    const response = await axios.post(`${BASE_URL}/api/auth/signin`, {
      email: '<EMAIL>',
      password: 'password123'
    });
    console.log('✅ Auth Response:', response.data);
  } catch (error) {
    console.log('❌ Auth Error:', error.message);
  }

  // Test 4: Check if Java is installed
  try {
    console.log('\n4. Checking Java installation...');
    const { exec } = require('child_process');
    const { promisify } = require('util');
    const execAsync = promisify(exec);
    
    const javaVersion = await execAsync('java -version');
    console.log('✅ Java is installed:', javaVersion.stderr.split('\n')[0]);
    
    const javacVersion = await execAsync('javac -version');
    console.log('✅ Java compiler is available:', javacVersion.stderr);
  } catch (error) {
    console.log('❌ Java not found:', error.message);
    console.log('💡 Install Java JDK 17+ for real code execution');
  }

  // Test 5: Test file system permissions
  try {
    console.log('\n5. Testing file system permissions...');
    const fs = require('fs').promises;
    const path = require('path');
    
    const tempDir = path.join(process.cwd(), 'temp');
    await fs.mkdir(tempDir, { recursive: true });
    
    const testFile = path.join(tempDir, 'test.txt');
    await fs.writeFile(testFile, 'test content');
    await fs.unlink(testFile);
    
    console.log('✅ File system permissions OK');
  } catch (error) {
    console.log('❌ File system error:', error.message);
  }

  console.log('\n🎯 Test Summary:');
  console.log('- If all tests pass, the platform should work correctly');
  console.log('- If Java tests fail, code execution will use simulation mode');
  console.log('- Check browser console for frontend errors');
  console.log('- Use demo credentials: <EMAIL> / password123');
}

// Run tests
testAPI().catch(console.error);
