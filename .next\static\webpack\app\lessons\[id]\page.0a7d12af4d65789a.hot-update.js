"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/lessons/[id]/page",{

/***/ "(app-pages-browser)/./src/app/lessons/[id]/page.tsx":
/*!***************************************!*\
  !*** ./src/app/lessons/[id]/page.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LessonPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_CodeEditor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/CodeEditor */ \"(app-pages-browser)/./src/components/CodeEditor.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction LessonPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [lesson, setLesson] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"theory\");\n    const [userCode, setUserCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isCompleted, setIsCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showHints, setShowHints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Function to get lesson data based on ID\n    const getLessonData = (id)=>{\n        const lessons = {\n            \"1\": {\n                id: \"1\",\n                title: \"Java Basics: Variables and Data Types\",\n                description: \"Learn about variables, primitive data types, and how to declare and use them in Java.\",\n                difficulty: \"Beginner\",\n                duration: \"30 min\",\n                content: {\n                    theory: \"\\n# Variables and Data Types in Java\\n\\n## What are Variables?\\nVariables are containers that store data values. In Java, every variable has a specific type that determines what kind of data it can hold.\\n\\n## Primitive Data Types\\nJava has 8 primitive data types:\\n\\n### Numeric Types:\\n- **byte**: 8-bit signed integer (-128 to 127)\\n- **short**: 16-bit signed integer (-32,768 to 32,767)\\n- **int**: 32-bit signed integer (-2^31 to 2^31-1)\\n- **long**: 64-bit signed integer (-2^63 to 2^63-1)\\n- **float**: 32-bit floating point\\n- **double**: 64-bit floating point\\n\\n### Other Types:\\n- **char**: 16-bit Unicode character\\n- **boolean**: true or false\\n\\n## Variable Declaration\\nTo declare a variable in Java:\\n```java\\ndataType variableName = value;\\n```\\n\\n## Naming Rules\\n- Must start with a letter, underscore, or dollar sign\\n- Cannot start with a number\\n- Case-sensitive\\n- Cannot use Java keywords\\n- Use camelCase convention\\n          \",\n                    example: 'public class VariableExample {\\n    public static void main(String[] args) {\\n        // Integer variables\\n        int age = 25;\\n        long population = 7800000000L;\\n\\n        // Floating point variables\\n        double price = 19.99;\\n        float temperature = 98.6f;\\n\\n        // Character and boolean\\n        char grade = \\'A\\';\\n        boolean isStudent = true;\\n\\n        // String (reference type)\\n        String name = \"John Doe\";\\n\\n        // Print all variables\\n        System.out.println(\"Name: \" + name);\\n        System.out.println(\"Age: \" + age);\\n        System.out.println(\"Grade: \" + grade);\\n        System.out.println(\"Is Student: \" + isStudent);\\n        System.out.println(\"Price: $\" + price);\\n    }\\n}',\n                    exercise: {\n                        description: \"Create a program that declares variables for a student's information and prints them out.\\n\\n**Requirements:**\\n1. Declare a String variable for the student's name\\n2. Declare an int variable for the student's age\\n3. Declare a double variable for the student's GPA\\n4. Declare a boolean variable for enrollment status\\n5. Print all the information in a formatted way\\n\\n**Expected format:**\\nStudent: [name]\\nAge: [age]\\nGPA: [gpa]\\nEnrolled: [status]\",\n                        starterCode: \"public class StudentInfo {\\n    public static void main(String[] args) {\\n        // TODO: Declare variables for student information\\n\\n\\n        // TODO: Print the student information\\n\\n    }\\n}\",\n                        expectedOutput: \"Student: Alice Johnson\\nAge: 20\\nGPA: 3.8\\nEnrolled: true\",\n                        hints: [\n                            \"Use String for the student's name\",\n                            \"Use int for age, double for GPA, and boolean for enrollment status\",\n                            \"Use System.out.println() to print each line\",\n                            \"You can concatenate strings using the + operator\"\n                        ]\n                    }\n                },\n                nextLessonId: \"2\",\n                prevLessonId: undefined\n            },\n            \"2\": {\n                id: \"2\",\n                title: \"Control Structures: If Statements and Loops\",\n                description: \"Master conditional statements and loops to control program flow.\",\n                difficulty: \"Beginner\",\n                duration: \"45 min\",\n                content: {\n                    theory: \"\\n# Control Structures in Java\\n\\nControl structures allow you to control the flow of execution in your Java programs. They include conditional statements and loops.\\n\\n## If Statements\\nIf statements execute code based on boolean conditions.\\n\\n### Basic If Statement:\\n```java\\nif (condition) {\\n    // code to execute if condition is true\\n}\\n```\\n\\n### If-Else Statement:\\n```java\\nif (condition) {\\n    // code if true\\n} else {\\n    // code if false\\n}\\n```\\n\\n### If-Else If-Else Chain:\\n```java\\nif (condition1) {\\n    // code if condition1 is true\\n} else if (condition2) {\\n    // code if condition2 is true\\n} else {\\n    // code if all conditions are false\\n}\\n```\\n\\n## For Loops\\nFor loops repeat code a specific number of times.\\n\\n### Basic For Loop:\\n```java\\nfor (initialization; condition; increment) {\\n    // code to repeat\\n}\\n```\\n\\n### Enhanced For Loop (for arrays):\\n```java\\nfor (dataType variable : array) {\\n    // code using variable\\n}\\n```\\n\\n## While Loops\\nWhile loops repeat code while a condition is true.\\n\\n### While Loop:\\n```java\\nwhile (condition) {\\n    // code to repeat\\n    // don't forget to update condition!\\n}\\n```\\n\\n### Do-While Loop:\\n```java\\ndo {\\n    // code to repeat at least once\\n} while (condition);\\n```\\n\\n## Switch Cases\\nSwitch statements provide an alternative to multiple if-else statements.\\n\\n```java\\nswitch (variable) {\\n    case value1:\\n        // code for value1\\n        break;\\n    case value2:\\n        // code for value2\\n        break;\\n    default:\\n        // default code\\n        break;\\n}\\n```\\n          \",\n                    example: 'public class ControlStructuresDemo {\\n    public static void main(String[] args) {\\n        // If Statements Example\\n        int score = 85;\\n        System.out.println(\"=== If Statements ===\");\\n\\n        if (score >= 90) {\\n            System.out.println(\"Grade: A - Excellent!\");\\n        } else if (score >= 80) {\\n            System.out.println(\"Grade: B - Good job!\");\\n        } else if (score >= 70) {\\n            System.out.println(\"Grade: C - Average\");\\n        } else {\\n            System.out.println(\"Grade: F - Need improvement\");\\n        }\\n\\n        // For Loop Example\\n        System.out.println(\"\\\\n=== For Loop ===\");\\n        System.out.println(\"Counting from 1 to 5:\");\\n        for (int i = 1; i <= 5; i++) {\\n            System.out.println(\"Count: \" + i);\\n        }\\n\\n        // While Loop Example\\n        System.out.println(\"\\\\n=== While Loop ===\");\\n        System.out.println(\"Countdown:\");\\n        int countdown = 3;\\n        while (countdown > 0) {\\n            System.out.println(countdown + \"...\");\\n            countdown--;\\n        }\\n        System.out.println(\"Blast off!\");\\n\\n        // Switch Case Example\\n        System.out.println(\"\\\\n=== Switch Case ===\");\\n        int dayOfWeek = 3;\\n        switch (dayOfWeek) {\\n            case 1:\\n                System.out.println(\"Monday\");\\n                break;\\n            case 2:\\n                System.out.println(\"Tuesday\");\\n                break;\\n            case 3:\\n                System.out.println(\"Wednesday\");\\n                break;\\n            case 4:\\n                System.out.println(\"Thursday\");\\n                break;\\n            case 5:\\n                System.out.println(\"Friday\");\\n                break;\\n            default:\\n                System.out.println(\"Weekend!\");\\n                break;\\n        }\\n    }\\n}',\n                    exercise: {\n                        description: \"Create a program that demonstrates all four control structure topics: If Statements, For Loops, While Loops, and Switch Cases.\\n\\n**Requirements:**\\n1. **If Statements**: Check if a student's grade (0-100) and print the letter grade (A, B, C, D, F)\\n2. **For Loops**: Print multiplication table for number 5 (5x1 to 5x10)\\n3. **While Loops**: Print numbers from 10 down to 1\\n4. **Switch Cases**: Given a month number (1-12), print the season\\n\\n**Expected output format:**\\nGrade: [letter grade]\\n=== Multiplication Table ===\\n5 x 1 = 5\\n5 x 2 = 10\\n...\\n=== Countdown ===\\n10\\n9\\n...\\n1\\nSeason: [season name]\",\n                        starterCode: 'public class ControlStructuresExercise {\\n    public static void main(String[] args) {\\n        // Test values\\n        int grade = 87;\\n        int tableNumber = 5;\\n        int month = 6;\\n\\n        // TODO: 1. If Statements - Check grade and print letter grade\\n        // A: 90-100, B: 80-89, C: 70-79, D: 60-69, F: below 60\\n\\n\\n        // TODO: 2. For Loop - Print multiplication table for tableNumber\\n        System.out.println(\"=== Multiplication Table ===\");\\n\\n\\n        // TODO: 3. While Loop - Print countdown from 10 to 1\\n        System.out.println(\"=== Countdown ===\");\\n\\n\\n        // TODO: 4. Switch Case - Print season based on month\\n        // Spring: 3,4,5  Summer: 6,7,8  Fall: 9,10,11  Winter: 12,1,2\\n\\n    }\\n}',\n                        expectedOutput: \"Grade: B\\n=== Multiplication Table ===\\n5 x 1 = 5\\n5 x 2 = 10\\n5 x 3 = 15\\n5 x 4 = 20\\n5 x 5 = 25\\n5 x 6 = 30\\n5 x 7 = 35\\n5 x 8 = 40\\n5 x 9 = 45\\n5 x 10 = 50\\n=== Countdown ===\\n10\\n9\\n8\\n7\\n6\\n5\\n4\\n3\\n2\\n1\\nSeason: Summer\",\n                        hints: [\n                            \"For if statements: Use >= for grade ranges (90, 80, 70, 60)\",\n                            \"For multiplication: Use for(int i = 1; i <= 10; i++)\",\n                            \"For countdown: Use while loop with int counter = 10; counter >= 1; counter--\",\n                            \"For switch: Use cases 3,4,5 for Spring; 6,7,8 for Summer, etc.\"\n                        ]\n                    }\n                },\n                nextLessonId: \"3\",\n                prevLessonId: \"1\"\n            }\n        };\n        return lessons[id] || lessons[\"1\"] // Default to lesson 1 if not found\n        ;\n    };\n    const mockLesson = getLessonData(params.id);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simulate loading lesson data\n        setLesson(mockLesson);\n        setUserCode(mockLesson.content.exercise.starterCode);\n        // Calculate progress based on current tab\n        const tabProgress = {\n            theory: 33,\n            example: 66,\n            exercise: 100\n        };\n        setProgress(tabProgress[currentTab]);\n    }, [\n        currentTab\n    ]);\n    const handleCodeChange = (code)=>{\n        setUserCode(code);\n    };\n    const handleRunCode = async (code)=>{\n        // Simulate code execution\n        try {\n            // In a real app, this would send the code to a backend service\n            const mockOutput = \"Student: Alice Johnson\\nAge: 20\\nGPA: 3.8\\nEnrolled: true\";\n            // Check if the output matches expected output\n            if (lesson && mockOutput.trim() === lesson.content.exercise.expectedOutput.trim()) {\n                setIsCompleted(true);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(\"Congratulations! Exercise completed successfully!\");\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Output doesn't match expected result. Keep trying!\");\n            }\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Error executing code\");\n        }\n    };\n    const handleNextLesson = ()=>{\n        if (lesson === null || lesson === void 0 ? void 0 : lesson.nextLessonId) {\n            router.push(\"/lessons/\".concat(lesson.nextLessonId));\n        }\n    };\n    const handlePrevLesson = ()=>{\n        if (lesson === null || lesson === void 0 ? void 0 : lesson.prevLessonId) {\n            router.push(\"/lessons/\".concat(lesson.prevLessonId));\n        }\n    };\n    if (!lesson) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                lineNumber: 453,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n            lineNumber: 452,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/dashboard\",\n                                        className: \"flex items-center text-gray-600 hover:text-primary-600 transition-colors mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-5 h-5 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Back to Dashboard\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-8 h-8 text-primary-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-xl font-bold text-gray-900\",\n                                        children: \"JavaLearn\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Progress: \",\n                                            progress,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-32 bg-gray-200 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-primary-600 h-2 rounded-full transition-all duration-300\",\n                                            style: {\n                                                width: \"\".concat(progress, \"%\")\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 472,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                        lineNumber: 463,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                    lineNumber: 462,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                lineNumber: 461,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        className: \"mb-8\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                            children: lesson.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 497,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: lesson.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 py-1 rounded-full text-xs \".concat(lesson.difficulty === \"Beginner\" ? \"bg-green-100 text-green-800\" : lesson.difficulty === \"Intermediate\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                                                    children: lesson.difficulty\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        lesson.duration\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 17\n                                                }, this),\n                                                isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex items-center text-green-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 513,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Completed\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"-mb-px flex space-x-8\",\n                                    children: [\n                                        {\n                                            id: \"theory\",\n                                            label: \"Theory\",\n                                            icon: _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                                        },\n                                        {\n                                            id: \"example\",\n                                            label: \"Example\",\n                                            icon: _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                                        },\n                                        {\n                                            id: \"exercise\",\n                                            label: \"Exercise\",\n                                            icon: _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n                                        }\n                                    ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setCurrentTab(tab.id),\n                                            className: \"flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors \".concat(currentTab === tab.id ? \"border-primary-500 text-primary-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 19\n                                                }, this),\n                                                tab.label\n                                            ]\n                                        }, tab.id, true, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 522,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                        lineNumber: 489,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            duration: 0.4\n                        },\n                        children: [\n                            currentTab === \"theory\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card p-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"prose max-w-none\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        dangerouslySetInnerHTML: {\n                                            __html: lesson.content.theory.replace(/\\n/g, \"<br>\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 556,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 554,\n                                columnNumber: 13\n                            }, this),\n                            currentTab === \"example\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                            children: \"Example Code\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CodeEditor__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            initialCode: lesson.content.example,\n                                            readOnly: true,\n                                            height: \"500px\",\n                                            showOutput: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 565,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 563,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 562,\n                                columnNumber: 13\n                            }, this),\n                            currentTab === \"exercise\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900\",\n                                                        children: \"Practice Exercise\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 579,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowHints(!showHints),\n                                                        className: \"btn-secondary text-sm\",\n                                                        children: showHints ? \"Hide Hints\" : \"Show Hints\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 580,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 578,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"prose max-w-none mb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    dangerouslySetInnerHTML: {\n                                                        __html: lesson.content.exercise.description.replace(/\\n/g, \"<br>\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 588,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 587,\n                                                columnNumber: 17\n                                            }, this),\n                                            showHints && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-yellow-800 mb-2\",\n                                                        children: \"Hints:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 593,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"text-yellow-700 text-sm space-y-1\",\n                                                        children: lesson.content.exercise.hints.map((hint, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    \"• \",\n                                                                    hint\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 596,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 594,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 592,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 577,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CodeEditor__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        initialCode: lesson.content.exercise.starterCode,\n                                        onCodeChange: handleCodeChange,\n                                        onRun: handleRunCode,\n                                        height: \"400px\",\n                                        showOutput: true,\n                                        expectedOutput: lesson.content.exercise.expectedOutput\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 603,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 576,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, currentTab, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                        lineNumber: 547,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mt-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handlePrevLesson,\n                                disabled: !lesson.prevLessonId,\n                                className: \"flex items-center btn-secondary disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-4 h-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 622,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Previous Lesson\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4\",\n                                children: [\n                                    currentTab !== \"exercise\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            const nextTab = currentTab === \"theory\" ? \"example\" : \"exercise\";\n                                            setCurrentTab(nextTab);\n                                        },\n                                        className: \"btn-primary\",\n                                        children: [\n                                            \"Continue\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 ml-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 636,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 628,\n                                        columnNumber: 15\n                                    }, this),\n                                    currentTab === \"exercise\" && isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNextLesson,\n                                        disabled: !lesson.nextLessonId,\n                                        className: \"flex items-center btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: [\n                                            \"Next Lesson\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 ml-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 626,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                        lineNumber: 616,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                lineNumber: 487,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n        lineNumber: 459,\n        columnNumber: 5\n    }, this);\n}\n_s(LessonPage, \"dBUNDWH67aICokwnd2zYUw3Quos=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = LessonPage;\nvar _c;\n$RefreshReg$(_c, \"LessonPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/lessons/[id]/page.tsx\n"));

/***/ })

});