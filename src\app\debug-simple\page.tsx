'use client'

import { useState } from 'react'

export default function DebugSimplePage() {
  const [result, setResult] = useState<string>('Click button to test...')

  const testWithVanillaJS = () => {
    setResult('Testing with vanilla JavaScript...')
    
    // Use vanilla JavaScript fetch
    const code = 'public class Test { public static void main(String[] args) { System.out.println("Hello from vanilla JS!"); } }'
    
    fetch('/api/execute', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        code: code,
        language: 'java'
      })
    })
    .then(response => {
      console.log('Response status:', response.status)
      return response.json()
    })
    .then(data => {
      console.log('Response data:', data)
      setResult(`SUCCESS!\n\nOutput: ${data.output}\n\nFull response: ${JSON.stringify(data, null, 2)}`)
    })
    .catch(error => {
      console.error('Error:', error)
      setResult(`ERROR: ${error.message}`)
    })
  }

  const testWithAsyncAwait = async () => {
    setResult('Testing with async/await...')
    
    try {
      const code = 'public class Test { public static void main(String[] args) { System.out.println("Hello from async!"); } }'
      
      const response = await fetch('/api/execute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code: code,
          language: 'java'
        })
      })

      console.log('Response status:', response.status)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      console.log('Response data:', data)
      
      setResult(`SUCCESS!\n\nOutput: ${data.output}\n\nFull response: ${JSON.stringify(data, null, 2)}`)
      
    } catch (error) {
      console.error('Error:', error)
      setResult(`ERROR: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  const testAPIStatus = async () => {
    setResult('Testing API status...')
    
    try {
      const response = await fetch('/api/test')
      const data = await response.json()
      setResult(`API Status: ${JSON.stringify(data, null, 2)}`)
    } catch (error) {
      setResult(`API Error: ${error}`)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">🔧 Debug Simple Test</h1>
        
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Test Buttons</h2>
          <div className="space-x-4 mb-6">
            <button
              onClick={testAPIStatus}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            >
              Test API Status
            </button>
            <button
              onClick={testWithVanillaJS}
              className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
            >
              Test with Vanilla JS
            </button>
            <button
              onClick={testWithAsyncAwait}
              className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600"
            >
              Test with Async/Await
            </button>
          </div>
          
          <div className="bg-gray-100 p-4 rounded">
            <h3 className="font-semibold mb-2">Result:</h3>
            <pre className="whitespace-pre-wrap text-sm">{result}</pre>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Instructions</h2>
          <ol className="list-decimal list-inside space-y-2">
            <li>Open browser console (F12) to see detailed logs</li>
            <li>Try each button and check the results</li>
            <li>If any of these work, the issue is with the complex components</li>
            <li>If none work, there's a fundamental React/Next.js issue</li>
          </ol>
        </div>
      </div>
    </div>
  )
}
