"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/lessons/[id]/page",{

/***/ "(app-pages-browser)/./src/app/lessons/[id]/page.tsx":
/*!***************************************!*\
  !*** ./src/app/lessons/[id]/page.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LessonPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_CodeEditor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/CodeEditor */ \"(app-pages-browser)/./src/components/CodeEditor.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction LessonPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [lesson, setLesson] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"theory\");\n    const [userCode, setUserCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isCompleted, setIsCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showHints, setShowHints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Function to get lesson data based on ID\n    const getLessonData = (id)=>{\n        const lessons = {\n            \"1\": {\n                id: \"1\",\n                title: \"Java Basics: Variables and Data Types\",\n                description: \"Learn about variables, primitive data types, and how to declare and use them in Java.\",\n                difficulty: \"Beginner\",\n                duration: \"30 min\",\n                content: {\n                    theory: \"\\n# Variables and Data Types in Java\\n\\n## What are Variables?\\nVariables are containers that store data values. In Java, every variable has a specific type that determines what kind of data it can hold.\\n\\n## Primitive Data Types\\nJava has 8 primitive data types:\\n\\n### Numeric Types:\\n- **byte**: 8-bit signed integer (-128 to 127)\\n- **short**: 16-bit signed integer (-32,768 to 32,767)\\n- **int**: 32-bit signed integer (-2^31 to 2^31-1)\\n- **long**: 64-bit signed integer (-2^63 to 2^63-1)\\n- **float**: 32-bit floating point\\n- **double**: 64-bit floating point\\n\\n### Other Types:\\n- **char**: 16-bit Unicode character\\n- **boolean**: true or false\\n\\n## Variable Declaration\\nTo declare a variable in Java:\\n```java\\ndataType variableName = value;\\n```\\n\\n## Naming Rules\\n- Must start with a letter, underscore, or dollar sign\\n- Cannot start with a number\\n- Case-sensitive\\n- Cannot use Java keywords\\n- Use camelCase convention\\n          \",\n                    example: 'public class VariableExample {\\n    public static void main(String[] args) {\\n        // Integer variables\\n        int age = 25;\\n        long population = 7800000000L;\\n\\n        // Floating point variables\\n        double price = 19.99;\\n        float temperature = 98.6f;\\n\\n        // Character and boolean\\n        char grade = \\'A\\';\\n        boolean isStudent = true;\\n\\n        // String (reference type)\\n        String name = \"John Doe\";\\n\\n        // Print all variables\\n        System.out.println(\"Name: \" + name);\\n        System.out.println(\"Age: \" + age);\\n        System.out.println(\"Grade: \" + grade);\\n        System.out.println(\"Is Student: \" + isStudent);\\n        System.out.println(\"Price: $\" + price);\\n    }\\n}',\n                    exercise: {\n                        description: \"Create a program that declares variables for a student's information and prints them out.\\n\\n**Requirements:**\\n1. Declare a String variable for the student's name\\n2. Declare an int variable for the student's age\\n3. Declare a double variable for the student's GPA\\n4. Declare a boolean variable for enrollment status\\n5. Print all the information in a formatted way\\n\\n**Expected format:**\\nStudent: [name]\\nAge: [age]\\nGPA: [gpa]\\nEnrolled: [status]\",\n                        starterCode: \"public class StudentInfo {\\n    public static void main(String[] args) {\\n        // TODO: Declare variables for student information\\n\\n\\n        // TODO: Print the student information\\n\\n    }\\n}\",\n                        expectedOutput: \"Student: Alice Johnson\\nAge: 20\\nGPA: 3.8\\nEnrolled: true\",\n                        hints: [\n                            \"Use String for the student's name\",\n                            \"Use int for age, double for GPA, and boolean for enrollment status\",\n                            \"Use System.out.println() to print each line\",\n                            \"You can concatenate strings using the + operator\"\n                        ]\n                    }\n                },\n                nextLessonId: \"2\",\n                prevLessonId: undefined\n            },\n            \"2\": {\n                id: \"2\",\n                title: \"Control Structures: If Statements and Loops\",\n                description: \"Master conditional statements and loops to control program flow.\",\n                difficulty: \"Beginner\",\n                duration: \"45 min\",\n                content: {\n                    theory: \"\\n# Control Structures in Java\\n\\nControl structures allow you to control the flow of execution in your Java programs. They include conditional statements and loops.\\n\\n## If Statements\\nIf statements execute code based on boolean conditions.\\n\\n### Basic If Statement:\\n```java\\nif (condition) {\\n    // code to execute if condition is true\\n}\\n```\\n\\n### If-Else Statement:\\n```java\\nif (condition) {\\n    // code if true\\n} else {\\n    // code if false\\n}\\n```\\n\\n### If-Else If-Else Chain:\\n```java\\nif (condition1) {\\n    // code if condition1 is true\\n} else if (condition2) {\\n    // code if condition2 is true\\n} else {\\n    // code if all conditions are false\\n}\\n```\\n\\n## For Loops\\nFor loops repeat code a specific number of times.\\n\\n### Basic For Loop:\\n```java\\nfor (initialization; condition; increment) {\\n    // code to repeat\\n}\\n```\\n\\n### Enhanced For Loop (for arrays):\\n```java\\nfor (dataType variable : array) {\\n    // code using variable\\n}\\n```\\n\\n## While Loops\\nWhile loops repeat code while a condition is true.\\n\\n### While Loop:\\n```java\\nwhile (condition) {\\n    // code to repeat\\n    // don't forget to update condition!\\n}\\n```\\n\\n### Do-While Loop:\\n```java\\ndo {\\n    // code to repeat at least once\\n} while (condition);\\n```\\n\\n## Switch Cases\\nSwitch statements provide an alternative to multiple if-else statements.\\n\\n```java\\nswitch (variable) {\\n    case value1:\\n        // code for value1\\n        break;\\n    case value2:\\n        // code for value2\\n        break;\\n    default:\\n        // default code\\n        break;\\n}\\n```\\n          \",\n                    example: 'public class ControlStructuresDemo {\\n    public static void main(String[] args) {\\n        // If Statements Example\\n        int score = 85;\\n        System.out.println(\"=== If Statements ===\");\\n\\n        if (score >= 90) {\\n            System.out.println(\"Grade: A - Excellent!\");\\n        } else if (score >= 80) {\\n            System.out.println(\"Grade: B - Good job!\");\\n        } else if (score >= 70) {\\n            System.out.println(\"Grade: C - Average\");\\n        } else {\\n            System.out.println(\"Grade: F - Need improvement\");\\n        }\\n\\n        // For Loop Example\\n        System.out.println(\"\\\\n=== For Loop ===\");\\n        System.out.println(\"Counting from 1 to 5:\");\\n        for (int i = 1; i <= 5; i++) {\\n            System.out.println(\"Count: \" + i);\\n        }\\n\\n        // While Loop Example\\n        System.out.println(\"\\\\n=== While Loop ===\");\\n        System.out.println(\"Countdown:\");\\n        int countdown = 3;\\n        while (countdown > 0) {\\n            System.out.println(countdown + \"...\");\\n            countdown--;\\n        }\\n        System.out.println(\"Blast off!\");\\n\\n        // Switch Case Example\\n        System.out.println(\"\\\\n=== Switch Case ===\");\\n        int dayOfWeek = 3;\\n        switch (dayOfWeek) {\\n            case 1:\\n                System.out.println(\"Monday\");\\n                break;\\n            case 2:\\n                System.out.println(\"Tuesday\");\\n                break;\\n            case 3:\\n                System.out.println(\"Wednesday\");\\n                break;\\n            case 4:\\n                System.out.println(\"Thursday\");\\n                break;\\n            case 5:\\n                System.out.println(\"Friday\");\\n                break;\\n            default:\\n                System.out.println(\"Weekend!\");\\n                break;\\n        }\\n    }\\n}',\n                    exercise: {\n                        description: \"Create a program that demonstrates all four control structure topics: If Statements, For Loops, While Loops, and Switch Cases.\\n\\n**Requirements:**\\n1. **If Statements**: Check if a student's grade (0-100) and print the letter grade (A, B, C, D, F)\\n2. **For Loops**: Print multiplication table for number 5 (5x1 to 5x10)\\n3. **While Loops**: Print numbers from 10 down to 1\\n4. **Switch Cases**: Given a month number (1-12), print the season\\n\\n**Expected output format:**\\nGrade: [letter grade]\\n=== Multiplication Table ===\\n5 x 1 = 5\\n5 x 2 = 10\\n...\\n=== Countdown ===\\n10\\n9\\n...\\n1\\nSeason: [season name]\",\n                        starterCode: 'public class ControlStructuresExercise {\\n    public static void main(String[] args) {\\n        // Test values\\n        int grade = 87;\\n        int tableNumber = 5;\\n        int month = 6;\\n\\n        // TODO: 1. If Statements - Check grade and print letter grade\\n        // A: 90-100, B: 80-89, C: 70-79, D: 60-69, F: below 60\\n\\n\\n        // TODO: 2. For Loop - Print multiplication table for tableNumber\\n        System.out.println(\"=== Multiplication Table ===\");\\n\\n\\n        // TODO: 3. While Loop - Print countdown from 10 to 1\\n        System.out.println(\"=== Countdown ===\");\\n\\n\\n        // TODO: 4. Switch Case - Print season based on month\\n        // Spring: 3,4,5  Summer: 6,7,8  Fall: 9,10,11  Winter: 12,1,2\\n\\n    }\\n}',\n                        expectedOutput: \"Grade: B\\n=== Multiplication Table ===\\n5 x 1 = 5\\n5 x 2 = 10\\n5 x 3 = 15\\n5 x 4 = 20\\n5 x 5 = 25\\n5 x 6 = 30\\n5 x 7 = 35\\n5 x 8 = 40\\n5 x 9 = 45\\n5 x 10 = 50\\n=== Countdown ===\\n10\\n9\\n8\\n7\\n6\\n5\\n4\\n3\\n2\\n1\\nSeason: Summer\",\n                        hints: [\n                            \"For if statements: Use >= for grade ranges (90, 80, 70, 60)\",\n                            \"For multiplication: Use for(int i = 1; i <= 10; i++)\",\n                            \"For countdown: Use while loop with int counter = 10; counter >= 1; counter--\",\n                            \"For switch: Use cases 3,4,5 for Spring; 6,7,8 for Summer, etc.\"\n                        ]\n                    }\n                },\n                nextLessonId: \"3\",\n                prevLessonId: \"1\"\n            },\n            \"3\": {\n                id: \"3\",\n                title: \"Methods and Functions\",\n                description: \"Learn how to create reusable code with methods and understand parameters.\",\n                difficulty: \"Beginner\",\n                duration: \"40 min\",\n                content: {\n                    theory: '\\n# Methods and Functions in Java\\n\\nMethods are blocks of code that perform specific tasks and can be reused throughout your program.\\n\\n## Method Declaration\\nThe basic syntax for declaring a method:\\n\\n```java\\naccessModifier returnType methodName(parameters) {\\n    // method body\\n    return value; // if returnType is not void\\n}\\n```\\n\\n### Components:\\n- **Access Modifier**: public, private, protected\\n- **Return Type**: void, int, String, etc.\\n- **Method Name**: follows camelCase convention\\n- **Parameters**: input values (optional)\\n\\n## Method Declaration Examples\\n\\n### Void Method (no return value):\\n```java\\npublic void printMessage() {\\n    System.out.println(\"Hello from method!\");\\n}\\n```\\n\\n### Method with Return Value:\\n```java\\npublic int addNumbers(int a, int b) {\\n    return a + b;\\n}\\n```\\n\\n## Parameters\\nParameters allow you to pass data into methods.\\n\\n### Single Parameter:\\n```java\\npublic void greetUser(String name) {\\n    System.out.println(\"Hello, \" + name + \"!\");\\n}\\n```\\n\\n### Multiple Parameters:\\n```java\\npublic double calculateArea(double length, double width) {\\n    return length * width;\\n}\\n```\\n\\n## Return Types\\nMethods can return different types of data.\\n\\n### Common Return Types:\\n- **void**: No return value\\n- **int**: Integer numbers\\n- **double**: Decimal numbers\\n- **String**: Text\\n- **boolean**: true/false\\n\\n## Method Overloading\\nYou can have multiple methods with the same name but different parameters.\\n\\n```java\\npublic int add(int a, int b) {\\n    return a + b;\\n}\\n\\npublic double add(double a, double b) {\\n    return a + b;\\n}\\n\\npublic int add(int a, int b, int c) {\\n    return a + b + c;\\n}\\n```\\n          ',\n                    example: 'public class MethodsDemo {\\n\\n    // Method Declaration - void method with no parameters\\n    public static void printWelcome() {\\n        System.out.println(\"Welcome to Java Methods!\");\\n        System.out.println(\"=========================\");\\n    }\\n\\n    // Method with Parameters - single parameter\\n    public static void greetUser(String userName) {\\n        System.out.println(\"Hello, \" + userName + \"! Nice to meet you.\");\\n    }\\n\\n    // Method with Return Type - returns an integer\\n    public static int addTwoNumbers(int num1, int num2) {\\n        int sum = num1 + num2;\\n        return sum;\\n    }\\n\\n    // Method with multiple parameters and return type\\n    public static double calculateRectangleArea(double length, double width) {\\n        return length * width;\\n    }\\n\\n    // Method Overloading - same name, different parameters\\n    public static int multiply(int a, int b) {\\n        return a * b;\\n    }\\n\\n    public static double multiply(double a, double b) {\\n        return a * b;\\n    }\\n\\n    public static int multiply(int a, int b, int c) {\\n        return a * b * c;\\n    }\\n\\n    public static void main(String[] args) {\\n        // Calling methods\\n        printWelcome();\\n\\n        greetUser(\"Alice\");\\n        greetUser(\"Bob\");\\n\\n        int result = addTwoNumbers(15, 25);\\n        System.out.println(\"15 + 25 = \" + result);\\n\\n        double area = calculateRectangleArea(5.5, 3.2);\\n        System.out.println(\"Rectangle area: \" + area);\\n\\n        // Method overloading examples\\n        System.out.println(\"2 * 3 = \" + multiply(2, 3));\\n        System.out.println(\"2.5 * 3.0 = \" + multiply(2.5, 3.0));\\n        System.out.println(\"2 * 3 * 4 = \" + multiply(2, 3, 4));\\n    }\\n}',\n                    exercise: {\n                        description: 'Create a program that demonstrates all four method topics: Method Declaration, Parameters, Return Types, and Method Overloading.\\n\\n**Requirements:**\\n1. **Method Declaration**: Create a void method that prints a welcome message\\n2. **Parameters**: Create a method that takes a name and age, prints a personalized message\\n3. **Return Types**: Create a method that calculates and returns the square of a number\\n4. **Method Overloading**: Create three versions of a \"calculate\" method:\\n   - One that adds two integers\\n   - One that adds two doubles\\n   - One that adds three integers\\n\\n**Expected output format:**\\nWelcome to the Method Exercise!\\nHello John, you are 25 years old.\\nThe square of 7 is: 49\\nSum of 5 and 3: 8\\nSum of 2.5 and 3.7: 6.2\\nSum of 1, 2, and 3: 6',\n                        starterCode: 'public class MethodsExercise {\\n\\n    // TODO: 1. Method Declaration - Create a void method called printWelcome()\\n\\n\\n    // TODO: 2. Parameters - Create a method called printPersonInfo(String name, int age)\\n\\n\\n    // TODO: 3. Return Types - Create a method called calculateSquare(int number) that returns int\\n\\n\\n    // TODO: 4. Method Overloading - Create three calculate methods:\\n    // - calculate(int a, int b) returns int\\n    // - calculate(double a, double b) returns double\\n    // - calculate(int a, int b, int c) returns int\\n\\n\\n\\n\\n    public static void main(String[] args) {\\n        // TODO: Call all your methods here with these values:\\n        // printWelcome()\\n        // printPersonInfo(\"John\", 25)\\n        // calculateSquare(7)\\n        // calculate(5, 3)\\n        // calculate(2.5, 3.7)\\n        // calculate(1, 2, 3)\\n\\n    }\\n}',\n                        expectedOutput: \"Welcome to the Method Exercise!\\nHello John, you are 25 years old.\\nThe square of 7 is: 49\\nSum of 5 and 3: 8\\nSum of 2.5 and 3.7: 6.2\\nSum of 1, 2, and 3: 6\",\n                        hints: [\n                            \"Use 'public static void' for methods that don't return values\",\n                            \"Use 'public static int' for methods that return integers\",\n                            \"Method overloading means same name, different parameter types or counts\",\n                            \"Don't forget to call all methods in main() and print the results\"\n                        ]\n                    }\n                },\n                nextLessonId: \"4\",\n                prevLessonId: \"2\"\n            },\n            \"4\": {\n                id: \"4\",\n                title: \"Object-Oriented Programming: Classes and Objects\",\n                description: \"Introduction to OOP concepts with classes, objects, and encapsulation.\",\n                difficulty: \"Intermediate\",\n                duration: \"60 min\",\n                content: {\n                    theory: '\\n# Object-Oriented Programming in Java\\n\\nObject-Oriented Programming (OOP) is a programming paradigm based on the concept of \"objects\" which contain data and code.\\n\\n## Classes\\nA class is a blueprint or template for creating objects. It defines the properties and behaviors that objects of that type will have.\\n\\n### Class Declaration:\\n```java\\npublic class ClassName {\\n    // fields (attributes)\\n    // constructors\\n    // methods\\n}\\n```\\n\\n### Example Class:\\n```java\\npublic class Car {\\n    // Fields (attributes)\\n    private String brand;\\n    private String model;\\n    private int year;\\n\\n    // Constructor\\n    public Car(String brand, String model, int year) {\\n        this.brand = brand;\\n        this.model = model;\\n        this.year = year;\\n    }\\n\\n    // Methods\\n    public void startEngine() {\\n        System.out.println(\"Engine started!\");\\n    }\\n}\\n```\\n\\n## Objects\\nAn object is an instance of a class. You create objects using the `new` keyword.\\n\\n```java\\nCar myCar = new Car(\"Toyota\", \"Camry\", 2023);\\n```\\n\\n## Constructors\\nConstructors are special methods used to initialize objects when they are created.\\n\\n### Default Constructor:\\n```java\\npublic Car() {\\n    // default values\\n}\\n```\\n\\n### Parameterized Constructor:\\n```java\\npublic Car(String brand, String model, int year) {\\n    this.brand = brand;\\n    this.model = model;\\n    this.year = year;\\n}\\n```\\n\\n## Encapsulation\\nEncapsulation is the practice of keeping fields private and providing public methods to access them.\\n\\n### Private Fields with Public Methods:\\n```java\\nprivate String name;\\n\\npublic String getName() {\\n    return name;\\n}\\n\\npublic void setName(String name) {\\n    this.name = name;\\n}\\n```\\n          ',\n                    example: '// Student class demonstrating OOP concepts\\nclass Student {\\n    // Private fields (Encapsulation)\\n    private String name;\\n    private int age;\\n    private String studentId;\\n    private double gpa;\\n\\n    // Default Constructor\\n    public Student() {\\n        this.name = \"Unknown\";\\n        this.age = 0;\\n        this.studentId = \"000000\";\\n        this.gpa = 0.0;\\n    }\\n\\n    // Parameterized Constructor\\n    public Student(String name, int age, String studentId, double gpa) {\\n        this.name = name;\\n        this.age = age;\\n        this.studentId = studentId;\\n        this.gpa = gpa;\\n    }\\n\\n    // Getter methods (Encapsulation)\\n    public String getName() {\\n        return name;\\n    }\\n\\n    public int getAge() {\\n        return age;\\n    }\\n\\n    public String getStudentId() {\\n        return studentId;\\n    }\\n\\n    public double getGpa() {\\n        return gpa;\\n    }\\n\\n    // Setter methods (Encapsulation)\\n    public void setName(String name) {\\n        this.name = name;\\n    }\\n\\n    public void setAge(int age) {\\n        if (age > 0) {\\n            this.age = age;\\n        }\\n    }\\n\\n    public void setGpa(double gpa) {\\n        if (gpa >= 0.0 && gpa <= 4.0) {\\n            this.gpa = gpa;\\n        }\\n    }\\n\\n    // Method to display student information\\n    public void displayInfo() {\\n        System.out.println(\"Student Information:\");\\n        System.out.println(\"Name: \" + name);\\n        System.out.println(\"Age: \" + age);\\n        System.out.println(\"Student ID: \" + studentId);\\n        System.out.println(\"GPA: \" + gpa);\\n    }\\n\\n    // Method to check if student is on honor roll\\n    public boolean isHonorRoll() {\\n        return gpa >= 3.5;\\n    }\\n}\\n\\npublic class OOPDemo {\\n    public static void main(String[] args) {\\n        // Creating objects using different constructors\\n        Student student1 = new Student();\\n        Student student2 = new Student(\"Alice Johnson\", 20, \"STU001\", 3.8);\\n\\n        System.out.println(\"=== Student 1 (Default Constructor) ===\");\\n        student1.displayInfo();\\n\\n        System.out.println(\"\\\\n=== Student 2 (Parameterized Constructor) ===\");\\n        student2.displayInfo();\\n\\n        // Using setter methods to modify student1\\n        student1.setName(\"Bob Smith\");\\n        student1.setAge(19);\\n        student1.setGpa(3.2);\\n\\n        System.out.println(\"\\\\n=== Student 1 (After modifications) ===\");\\n        student1.displayInfo();\\n\\n        // Using methods\\n        System.out.println(\"\\\\n=== Honor Roll Status ===\");\\n        System.out.println(student1.getName() + \" honor roll: \" + student1.isHonorRoll());\\n        System.out.println(student2.getName() + \" honor roll: \" + student2.isHonorRoll());\\n    }\\n}',\n                    exercise: {\n                        description: \"Create a Book class that demonstrates all four OOP topics: Classes, Objects, Constructors, and Encapsulation.\\n\\n**Requirements:**\\n1. **Classes**: Create a Book class with private fields: title, author, pages, price\\n2. **Objects**: Create two Book objects in main method\\n3. **Constructors**: Implement both default and parameterized constructors\\n4. **Encapsulation**: Create getter and setter methods for all fields, plus a displayInfo() method\\n\\n**Expected output format:**\\n=== Book 1 (Default Constructor) ===\\nTitle: Unknown\\nAuthor: Unknown\\nPages: 0\\nPrice: $0.0\\n\\n=== Book 2 (Parameterized Constructor) ===\\nTitle: Java Programming\\nAuthor: John Doe\\nPages: 500\\nPrice: $49.99\\n\\n=== Book 1 (After modifications) ===\\nTitle: Python Basics\\nAuthor: Jane Smith\\nPages: 300\\nPrice: $29.99\",\n                        starterCode: '// TODO: Create Book class here\\nclass Book {\\n    // TODO: 1. Classes - Add private fields: title, author, pages, price\\n\\n\\n    // TODO: 2. Constructors - Create default constructor\\n\\n\\n    // TODO: 2. Constructors - Create parameterized constructor\\n\\n\\n    // TODO: 3. Encapsulation - Create getter methods\\n\\n\\n\\n\\n\\n    // TODO: 3. Encapsulation - Create setter methods\\n\\n\\n\\n\\n\\n    // TODO: Create displayInfo() method\\n\\n}\\n\\npublic class BookDemo {\\n    public static void main(String[] args) {\\n        // TODO: 4. Objects - Create two Book objects\\n        // book1 using default constructor\\n        // book2 using parameterized constructor with values:\\n        // \"Java Programming\", \"John Doe\", 500, 49.99\\n\\n\\n        // TODO: Display both books\\n\\n\\n        // TODO: Modify book1 using setters:\\n        // title: \"Python Basics\", author: \"Jane Smith\", pages: 300, price: 29.99\\n\\n\\n        // TODO: Display book1 again\\n\\n    }\\n}',\n                        expectedOutput: \"=== Book 1 (Default Constructor) ===\\nTitle: Unknown\\nAuthor: Unknown\\nPages: 0\\nPrice: $0.0\\n\\n=== Book 2 (Parameterized Constructor) ===\\nTitle: Java Programming\\nAuthor: John Doe\\nPages: 500\\nPrice: $49.99\\n\\n=== Book 1 (After modifications) ===\\nTitle: Python Basics\\nAuthor: Jane Smith\\nPages: 300\\nPrice: $29.99\",\n                        hints: [\n                            \"Use private fields and public methods for encapsulation\",\n                            \"Default constructor should set default values like 'Unknown' and 0\",\n                            \"Parameterized constructor should accept all four parameters\",\n                            \"Getter methods return field values, setter methods update them\"\n                        ]\n                    }\n                },\n                nextLessonId: \"5\",\n                prevLessonId: \"3\"\n            },\n            \"5\": {\n                id: \"5\",\n                title: \"Arrays and Collections\",\n                description: \"Work with arrays and Java collections like ArrayList and HashMap.\",\n                difficulty: \"Intermediate\",\n                duration: \"50 min\",\n                content: {\n                    theory: '\\n# Arrays and Collections in Java\\n\\nArrays and Collections are used to store multiple values in Java.\\n\\n## Arrays\\nArrays store multiple values of the same type in a fixed-size sequential collection.\\n\\n### Array Declaration and Initialization:\\n```java\\n// Declaration\\nint[] numbers;\\nString[] names;\\n\\n// Initialization\\nint[] numbers = new int[5];  // Array of 5 integers\\nString[] names = {\"Alice\", \"Bob\", \"Charlie\"};  // Array with values\\n```\\n\\n### Accessing Array Elements:\\n```java\\nint[] numbers = {10, 20, 30, 40, 50};\\nSystem.out.println(numbers[0]);  // Prints 10\\nnumbers[1] = 25;  // Changes second element to 25\\n```\\n\\n### Array Properties:\\n```java\\nint[] numbers = {1, 2, 3, 4, 5};\\nSystem.out.println(numbers.length);  // Prints 5\\n```\\n\\n## ArrayList\\nArrayList is a resizable array implementation that can grow and shrink dynamically.\\n\\n### ArrayList Declaration and Initialization:\\n```java\\nimport java.util.ArrayList;\\n\\nArrayList<String> names = new ArrayList<>();\\nArrayList<Integer> numbers = new ArrayList<>();\\n```\\n\\n### ArrayList Methods:\\n```java\\nArrayList<String> fruits = new ArrayList<>();\\nfruits.add(\"Apple\");        // Add element\\nfruits.add(\"Banana\");\\nfruits.get(0);             // Get element at index 0\\nfruits.set(0, \"Orange\");   // Replace element at index 0\\nfruits.remove(1);          // Remove element at index 1\\nfruits.size();             // Get size\\n```\\n\\n## HashMap\\nHashMap stores key-value pairs and allows fast lookup by key.\\n\\n### HashMap Declaration and Usage:\\n```java\\nimport java.util.HashMap;\\n\\nHashMap<String, Integer> ages = new HashMap<>();\\nages.put(\"Alice\", 25);     // Add key-value pair\\nages.put(\"Bob\", 30);\\nages.get(\"Alice\");         // Get value by key (returns 25)\\nages.remove(\"Bob\");        // Remove key-value pair\\n```\\n\\n## Iteration\\nYou can iterate through arrays and collections using loops.\\n\\n### For-each Loop:\\n```java\\n// Arrays\\nint[] numbers = {1, 2, 3, 4, 5};\\nfor (int num : numbers) {\\n    System.out.println(num);\\n}\\n\\n// ArrayList\\nArrayList<String> names = new ArrayList<>();\\nfor (String name : names) {\\n    System.out.println(name);\\n}\\n```\\n          ',\n                    example: 'import java.util.ArrayList;\\nimport java.util.HashMap;\\n\\npublic class ArraysCollectionsDemo {\\n    public static void main(String[] args) {\\n\\n        // === ARRAYS ===\\n        System.out.println(\"=== Arrays Demo ===\");\\n\\n        // Array declaration and initialization\\n        int[] scores = {85, 92, 78, 96, 88};\\n        String[] subjects = new String[3];\\n        subjects[0] = \"Math\";\\n        subjects[1] = \"Science\";\\n        subjects[2] = \"English\";\\n\\n        // Accessing and modifying arrays\\n        System.out.println(\"First score: \" + scores[0]);\\n        System.out.println(\"Array length: \" + scores.length);\\n        scores[0] = 90;  // Modify first element\\n\\n        // Iteration through array\\n        System.out.println(\"All scores:\");\\n        for (int score : scores) {\\n            System.out.println(\"Score: \" + score);\\n        }\\n\\n        // === ARRAYLIST ===\\n        System.out.println(\"\\\\n=== ArrayList Demo ===\");\\n\\n        ArrayList<String> students = new ArrayList<>();\\n\\n        // Adding elements\\n        students.add(\"Alice\");\\n        students.add(\"Bob\");\\n        students.add(\"Charlie\");\\n        students.add(\"Diana\");\\n\\n        System.out.println(\"Number of students: \" + students.size());\\n        System.out.println(\"First student: \" + students.get(0));\\n\\n        // Modifying ArrayList\\n        students.set(1, \"Robert\");  // Change Bob to Robert\\n        students.remove(\"Charlie\"); // Remove Charlie\\n\\n        // Iteration through ArrayList\\n        System.out.println(\"Current students:\");\\n        for (String student : students) {\\n            System.out.println(\"Student: \" + student);\\n        }\\n\\n        // === HASHMAP ===\\n        System.out.println(\"\\\\n=== HashMap Demo ===\");\\n\\n        HashMap<String, Integer> studentGrades = new HashMap<>();\\n\\n        // Adding key-value pairs\\n        studentGrades.put(\"Alice\", 95);\\n        studentGrades.put(\"Robert\", 87);\\n        studentGrades.put(\"Diana\", 92);\\n\\n        // Accessing values\\n        System.out.println(\"Alice\\'s grade: \" + studentGrades.get(\"Alice\"));\\n        System.out.println(\"Number of grades: \" + studentGrades.size());\\n\\n        // Iteration through HashMap\\n        System.out.println(\"All grades:\");\\n        for (String name : studentGrades.keySet()) {\\n            System.out.println(name + \": \" + studentGrades.get(name));\\n        }\\n\\n        // Check if key exists\\n        if (studentGrades.containsKey(\"Alice\")) {\\n            System.out.println(\"Alice\\'s grade found!\");\\n        }\\n    }\\n}',\n                    exercise: {\n                        description: \"Create a program that demonstrates all four collection topics: Arrays, ArrayList, HashMap, and Iteration.\\n\\n**Requirements:**\\n1. **Arrays**: Create an array of 5 integers, display them, and calculate their sum\\n2. **ArrayList**: Create an ArrayList of fruits, add 4 fruits, remove one, and display the list\\n3. **HashMap**: Create a HashMap of countries and their capitals, add 3 pairs, and display them\\n4. **Iteration**: Use for-each loops to iterate through all collections\\n\\n**Expected output format:**\\n=== Arrays ===\\nNumbers: 10 20 30 40 50\\nSum: 150\\n\\n=== ArrayList ===\\nFruits: [Apple, Banana, Orange]\\n\\n=== HashMap ===\\nCountries and Capitals:\\nUSA: Washington DC\\nFrance: Paris\\nJapan: Tokyo\",\n                        starterCode: 'import java.util.ArrayList;\\nimport java.util.HashMap;\\n\\npublic class CollectionsExercise {\\n    public static void main(String[] args) {\\n\\n        // TODO: 1. Arrays - Create array with values {10, 20, 30, 40, 50}\\n        System.out.println(\"=== Arrays ===\");\\n\\n\\n        // TODO: Display array elements and calculate sum using iteration\\n\\n\\n        // TODO: 2. ArrayList - Create ArrayList of fruits\\n        System.out.println(\"\\\\n=== ArrayList ===\");\\n\\n\\n        // TODO: Add fruits: \"Apple\", \"Banana\", \"Cherry\", \"Orange\"\\n        // TODO: Remove \"Cherry\"\\n        // TODO: Display remaining fruits\\n\\n\\n        // TODO: 3. HashMap - Create HashMap of countries and capitals\\n        System.out.println(\"\\\\n=== HashMap ===\");\\n\\n\\n        // TODO: Add pairs: \"USA\"->\"Washington DC\", \"France\"->\"Paris\", \"Japan\"->\"Tokyo\"\\n        // TODO: Display all pairs using iteration\\n\\n    }\\n}',\n                        expectedOutput: \"=== Arrays ===\\nNumbers: 10 20 30 40 50\\nSum: 150\\n\\n=== ArrayList ===\\nFruits: [Apple, Banana, Orange]\\n\\n=== HashMap ===\\nCountries and Capitals:\\nUSA: Washington DC\\nFrance: Paris\\nJapan: Tokyo\",\n                        hints: [\n                            \"Use for-each loop: for(int num : array) to iterate arrays\",\n                            \"ArrayList methods: add(), remove(), toString() for display\",\n                            \"HashMap methods: put(), keySet() for iteration\",\n                            \"Calculate sum by adding each array element in the loop\"\n                        ]\n                    }\n                },\n                nextLessonId: \"6\",\n                prevLessonId: \"4\"\n            },\n            \"6\": {\n                id: \"6\",\n                title: \"Exception Handling\",\n                description: \"Learn to handle errors gracefully with try-catch blocks and custom exceptions.\",\n                difficulty: \"Advanced\",\n                duration: \"45 min\",\n                content: {\n                    theory: '\\n# Exception Handling in Java\\n\\nException handling allows you to manage runtime errors gracefully and prevent your program from crashing.\\n\\n## Try-Catch Blocks\\nThe basic structure for handling exceptions:\\n\\n```java\\ntry {\\n    // Code that might throw an exception\\n} catch (ExceptionType e) {\\n    // Handle the exception\\n}\\n```\\n\\n### Example:\\n```java\\ntry {\\n    int result = 10 / 0;  // This will throw ArithmeticException\\n} catch (ArithmeticException e) {\\n    System.out.println(\"Cannot divide by zero!\");\\n}\\n```\\n\\n## Finally Block\\nThe finally block always executes, whether an exception occurs or not:\\n\\n```java\\ntry {\\n    // risky code\\n} catch (Exception e) {\\n    // handle exception\\n} finally {\\n    // cleanup code - always runs\\n    System.out.println(\"Cleanup completed\");\\n}\\n```\\n\\n## Custom Exceptions\\nYou can create your own exception classes:\\n\\n```java\\nclass CustomException extends Exception {\\n    public CustomException(String message) {\\n        super(message);\\n    }\\n}\\n```\\n\\n## Throws Keyword\\nUse throws to declare that a method might throw an exception:\\n\\n```java\\npublic void riskyMethod() throws IOException {\\n    // code that might throw IOException\\n}\\n```\\n\\n### Calling methods that throw exceptions:\\n```java\\ntry {\\n    riskyMethod();\\n} catch (IOException e) {\\n    System.out.println(\"IO Error: \" + e.getMessage());\\n}\\n```\\n\\n## Common Exception Types\\n- **ArithmeticException**: Division by zero\\n- **NullPointerException**: Using null reference\\n- **ArrayIndexOutOfBoundsException**: Invalid array index\\n- **NumberFormatException**: Invalid number conversion\\n- **IOException**: Input/output operations\\n- **FileNotFoundException**: File not found\\n\\n## Best Practices\\n1. Catch specific exceptions rather than generic Exception\\n2. Always clean up resources in finally block\\n3. Don\\'t ignore exceptions - at least log them\\n4. Use meaningful error messages\\n          ',\n                    example: '// Custom Exception class\\nclass InvalidAgeException extends Exception {\\n    public InvalidAgeException(String message) {\\n        super(message);\\n    }\\n}\\n\\npublic class ExceptionHandlingDemo {\\n\\n    // Method that throws custom exception\\n    public static void validateAge(int age) throws InvalidAgeException {\\n        if (age < 0 || age > 150) {\\n            throw new InvalidAgeException(\"Age must be between 0 and 150. Got: \" + age);\\n        }\\n        System.out.println(\"Valid age: \" + age);\\n    }\\n\\n    // Method demonstrating different exception types\\n    public static void demonstrateExceptions() {\\n\\n        // Try-Catch with ArithmeticException\\n        System.out.println(\"=== ArithmeticException Demo ===\");\\n        try {\\n            int result = 10 / 0;\\n            System.out.println(\"Result: \" + result);\\n        } catch (ArithmeticException e) {\\n            System.out.println(\"Error: Cannot divide by zero!\");\\n        }\\n\\n        // Try-Catch with ArrayIndexOutOfBoundsException\\n        System.out.println(\"\\\\n=== ArrayIndexOutOfBoundsException Demo ===\");\\n        try {\\n            int[] numbers = {1, 2, 3};\\n            System.out.println(\"Element at index 5: \" + numbers[5]);\\n        } catch (ArrayIndexOutOfBoundsException e) {\\n            System.out.println(\"Error: Array index out of bounds!\");\\n        }\\n\\n        // Try-Catch with NumberFormatException\\n        System.out.println(\"\\\\n=== NumberFormatException Demo ===\");\\n        try {\\n            String text = \"abc\";\\n            int number = Integer.parseInt(text);\\n            System.out.println(\"Number: \" + number);\\n        } catch (NumberFormatException e) {\\n            System.out.println(\"Error: Cannot convert \\'\" + e.getMessage().split(\"\"\")[1] + \"\\' to number!\");\\n        }\\n    }\\n\\n    public static void main(String[] args) {\\n\\n        // Demonstrate common exceptions\\n        demonstrateExceptions();\\n\\n        // Custom Exception with Throws\\n        System.out.println(\"\\\\n=== Custom Exception Demo ===\");\\n\\n        int[] testAges = {25, -5, 200, 30};\\n\\n        for (int age : testAges) {\\n            try {\\n                validateAge(age);\\n            } catch (InvalidAgeException e) {\\n                System.out.println(\"Custom Error: \" + e.getMessage());\\n            }\\n        }\\n\\n        // Finally Block Demo\\n        System.out.println(\"\\\\n=== Finally Block Demo ===\");\\n        try {\\n            System.out.println(\"Executing risky operation...\");\\n            int result = 10 / 2;  // This works fine\\n            System.out.println(\"Result: \" + result);\\n        } catch (Exception e) {\\n            System.out.println(\"Exception caught: \" + e.getMessage());\\n        } finally {\\n            System.out.println(\"Finally block: Cleanup completed!\");\\n        }\\n\\n        System.out.println(\"\\\\nProgram completed successfully!\");\\n    }\\n}',\n                    exercise: {\n                        description: \"Create a program that demonstrates all four exception handling topics: Try-Catch, Finally Block, Custom Exceptions, and Throws.\\n\\n**Requirements:**\\n1. **Try-Catch**: Handle division by zero and array index out of bounds\\n2. **Finally Block**: Use finally to print cleanup message\\n3. **Custom Exceptions**: Create InvalidScoreException for scores outside 0-100 range\\n4. **Throws**: Create a method that throws the custom exception\\n\\n**Expected output format:**\\n=== Try-Catch Demo ===\\nError: Division by zero!\\nError: Array index out of bounds!\\n\\n=== Custom Exception Demo ===\\nValid score: 85\\nError: Score must be between 0 and 100. Got: 150\\n\\n=== Finally Block Demo ===\\nProcessing...\\nFinally: Cleanup completed!\",\n                        starterCode: '// TODO: 1. Custom Exceptions - Create InvalidScoreException class\\n\\n\\npublic class ExceptionExercise {\\n\\n    // TODO: 2. Throws - Create validateScore method that throws InvalidScoreException\\n    // Method should accept int score and throw exception if score < 0 or score > 100\\n\\n\\n    public static void main(String[] args) {\\n\\n        System.out.println(\"=== Try-Catch Demo ===\");\\n\\n        // TODO: 3. Try-Catch - Handle division by zero\\n        try {\\n\\n        } catch () {\\n\\n        }\\n\\n        // TODO: 3. Try-Catch - Handle array index out of bounds\\n        // Create array {1, 2, 3} and try to access index 5\\n        try {\\n\\n        } catch () {\\n\\n        }\\n\\n        System.out.println(\"\\\\n=== Custom Exception Demo ===\");\\n\\n        // TODO: Test validateScore with values 85 and 150\\n\\n\\n        System.out.println(\"\\\\n=== Finally Block Demo ===\");\\n\\n        // TODO: 4. Finally Block - Use try-catch-finally\\n        try {\\n            System.out.println(\"Processing...\");\\n            // Some operation\\n        } catch (Exception e) {\\n            System.out.println(\"Error occurred\");\\n        } finally {\\n            // TODO: Print cleanup message\\n        }\\n    }\\n}',\n                        expectedOutput: \"=== Try-Catch Demo ===\\nError: Division by zero!\\nError: Array index out of bounds!\\n\\n=== Custom Exception Demo ===\\nValid score: 85\\nError: Score must be between 0 and 100. Got: 150\\n\\n=== Finally Block Demo ===\\nProcessing...\\nFinally: Cleanup completed!\",\n                        hints: [\n                            \"Custom exception: class InvalidScoreException extends Exception\",\n                            \"Throws: public static void validateScore(int score) throws InvalidScoreException\",\n                            \"Try-catch: catch (ArithmeticException e) and catch (ArrayIndexOutOfBoundsException e)\",\n                            \"Finally block always executes after try-catch\"\n                        ]\n                    }\n                },\n                nextLessonId: undefined,\n                prevLessonId: \"5\"\n            }\n        };\n        return lessons[id] || lessons[\"1\"] // Default to lesson 1 if not found\n        ;\n    };\n    const mockLesson = getLessonData(params.id);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simulate loading lesson data\n        setLesson(mockLesson);\n        setUserCode(mockLesson.content.exercise.starterCode);\n        // Calculate progress based on current tab\n        const tabProgress = {\n            theory: 33,\n            example: 66,\n            exercise: 100\n        };\n        setProgress(tabProgress[currentTab]);\n    }, [\n        currentTab\n    ]);\n    const handleCodeChange = (code)=>{\n        setUserCode(code);\n    };\n    const handleRunCode = async (code)=>{\n        console.log(\"\\uD83D\\uDE80 Lesson handleRunCode called with code:\", code.substring(0, 100) + \"...\");\n        try {\n            // Call the real API for code execution\n            const apiUrl = window.location.origin + \"/api/execute\";\n            console.log(\"\\uD83D\\uDCE1 Calling API:\", apiUrl);\n            const response = await fetch(apiUrl, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    code: code,\n                    language: \"java\"\n                })\n            });\n            console.log(\"\\uD83D\\uDCE1 API Response status:\", response.status);\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const result = await response.json();\n            console.log(\"✅ API Result:\", result);\n            if (result.success) {\n                const actualOutput = result.output || \"\";\n                console.log(\"\\uD83D\\uDCDD Actual output:\", actualOutput);\n                console.log(\"\\uD83D\\uDCDD Expected output:\", lesson === null || lesson === void 0 ? void 0 : lesson.content.exercise.expectedOutput);\n                // Check if the output matches expected output (compare first lines for flexibility)\n                if (lesson && lesson.content.exercise.expectedOutput) {\n                    const expectedLines = lesson.content.exercise.expectedOutput.trim().split(\"\\n\");\n                    const actualLines = actualOutput.trim().split(\"\\n\");\n                    // Check if the main content matches (ignoring simulation message)\n                    const actualMainContent = actualLines.filter((line)=>!line.includes(\"Simulation Mode\")).join(\"\\n\");\n                    const expectedMainContent = expectedLines.join(\"\\n\");\n                    if (actualMainContent.includes(expectedMainContent) || expectedMainContent.includes(actualMainContent)) {\n                        setIsCompleted(true);\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(\"Congratulations! Exercise completed successfully!\");\n                    } else {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Output doesn't match expected result. Keep trying!\");\n                    }\n                } else {\n                    // If no expected output, just show success\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(\"Code executed successfully!\");\n                }\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Execution failed: \".concat(result.error));\n            }\n        } catch (error) {\n            console.error(\"\\uD83D\\uDCA5 Lesson execution error:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Error executing code: \" + (error instanceof Error ? error.message : \"Unknown error\"));\n        }\n    };\n    const handleNextLesson = ()=>{\n        if (lesson === null || lesson === void 0 ? void 0 : lesson.nextLessonId) {\n            router.push(\"/lessons/\".concat(lesson.nextLessonId));\n        }\n    };\n    const handlePrevLesson = ()=>{\n        if (lesson === null || lesson === void 0 ? void 0 : lesson.prevLessonId) {\n            router.push(\"/lessons/\".concat(lesson.prevLessonId));\n        }\n    };\n    if (!lesson) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                lineNumber: 1497,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n            lineNumber: 1496,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/dashboard\",\n                                        className: \"flex items-center text-gray-600 hover:text-primary-600 transition-colors mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-5 h-5 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1510,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Back to Dashboard\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1509,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-8 h-8 text-primary-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1513,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-xl font-bold text-gray-900\",\n                                        children: \"JavaLearn\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1514,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1508,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Progress: \",\n                                            progress,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1517,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-32 bg-gray-200 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-primary-600 h-2 rounded-full transition-all duration-300\",\n                                            style: {\n                                                width: \"\".concat(progress, \"%\")\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1521,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1520,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1516,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                        lineNumber: 1507,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                    lineNumber: 1506,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                lineNumber: 1505,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        className: \"mb-8\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                            children: lesson.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1541,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: lesson.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1542,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 py-1 rounded-full text-xs \".concat(lesson.difficulty === \"Beginner\" ? \"bg-green-100 text-green-800\" : lesson.difficulty === \"Intermediate\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                                                    children: lesson.difficulty\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1544,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1552,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        lesson.duration\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1551,\n                                                    columnNumber: 17\n                                                }, this),\n                                                isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex items-center text-green-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1557,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Completed\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1556,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1543,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1540,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1539,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"-mb-px flex space-x-8\",\n                                    children: [\n                                        {\n                                            id: \"theory\",\n                                            label: \"Theory\",\n                                            icon: _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                                        },\n                                        {\n                                            id: \"example\",\n                                            label: \"Example\",\n                                            icon: _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                                        },\n                                        {\n                                            id: \"exercise\",\n                                            label: \"Exercise\",\n                                            icon: _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n                                        }\n                                    ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setCurrentTab(tab.id),\n                                            className: \"flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors \".concat(currentTab === tab.id ? \"border-primary-500 text-primary-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1582,\n                                                    columnNumber: 19\n                                                }, this),\n                                                tab.label\n                                            ]\n                                        }, tab.id, true, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1573,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1567,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1566,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                        lineNumber: 1533,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            duration: 0.4\n                        },\n                        children: [\n                            currentTab === \"theory\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card p-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"prose max-w-none\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        dangerouslySetInnerHTML: {\n                                            __html: lesson.content.theory.replace(/\\n/g, \"<br>\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1600,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1599,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1598,\n                                columnNumber: 13\n                            }, this),\n                            currentTab === \"example\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                            children: \"Example Code\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1608,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CodeEditor__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            initialCode: lesson.content.example,\n                                            readOnly: true,\n                                            height: \"500px\",\n                                            showOutput: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1609,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1607,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1606,\n                                columnNumber: 13\n                            }, this),\n                            currentTab === \"exercise\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900\",\n                                                        children: \"Practice Exercise\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1623,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowHints(!showHints),\n                                                        className: \"btn-secondary text-sm\",\n                                                        children: showHints ? \"Hide Hints\" : \"Show Hints\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1624,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1622,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"prose max-w-none mb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    dangerouslySetInnerHTML: {\n                                                        __html: lesson.content.exercise.description.replace(/\\n/g, \"<br>\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1632,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1631,\n                                                columnNumber: 17\n                                            }, this),\n                                            showHints && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-yellow-800 mb-2\",\n                                                        children: \"Hints:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1637,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"text-yellow-700 text-sm space-y-1\",\n                                                        children: lesson.content.exercise.hints.map((hint, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    \"• \",\n                                                                    hint\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 1640,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1638,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1636,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1621,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CodeEditor__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        initialCode: lesson.content.exercise.starterCode,\n                                        onCodeChange: handleCodeChange,\n                                        onRun: handleRunCode,\n                                        height: \"400px\",\n                                        showOutput: true,\n                                        expectedOutput: lesson.content.exercise.expectedOutput\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1647,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1620,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, currentTab, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                        lineNumber: 1591,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mt-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handlePrevLesson,\n                                disabled: !lesson.prevLessonId,\n                                className: \"flex items-center btn-secondary disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-4 h-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1666,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Previous Lesson\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1661,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4\",\n                                children: [\n                                    currentTab !== \"exercise\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            const nextTab = currentTab === \"theory\" ? \"example\" : \"exercise\";\n                                            setCurrentTab(nextTab);\n                                        },\n                                        className: \"btn-primary\",\n                                        children: [\n                                            \"Continue\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 ml-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1680,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1672,\n                                        columnNumber: 15\n                                    }, this),\n                                    currentTab === \"exercise\" && isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNextLesson,\n                                        disabled: !lesson.nextLessonId,\n                                        className: \"flex items-center btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: [\n                                            \"Next Lesson\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 ml-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1691,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1685,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1670,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                        lineNumber: 1660,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                lineNumber: 1531,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n        lineNumber: 1503,\n        columnNumber: 5\n    }, this);\n}\n_s(LessonPage, \"dBUNDWH67aICokwnd2zYUw3Quos=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = LessonPage;\nvar _c;\n$RefreshReg$(_c, \"LessonPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/lessons/[id]/page.tsx\n"));

/***/ })

});