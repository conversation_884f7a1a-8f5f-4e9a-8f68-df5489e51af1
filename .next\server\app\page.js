/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CClients%5CDMI%5CJava-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CClients%5CDMI%5CJava-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CClients%5CDMI%5CJava-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q0NsaWVudHMlNUNETUklNUNKYXZhLWFwcCU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNkaXN0JTVDY2xpZW50JTVDY29tcG9uZW50cyU1Q2FwcC1yb3V0ZXIuanMmbW9kdWxlcz1EJTNBJTVDQ2xpZW50cyU1Q0RNSSU1Q0phdmEtYXBwJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNjb21wb25lbnRzJTVDZXJyb3ItYm91bmRhcnkuanMmbW9kdWxlcz1EJTNBJTVDQ2xpZW50cyU1Q0RNSSU1Q0phdmEtYXBwJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNjb21wb25lbnRzJTVDbGF5b3V0LXJvdXRlci5qcyZtb2R1bGVzPUQlM0ElNUNDbGllbnRzJTVDRE1JJTVDSmF2YS1hcHAlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNub3QtZm91bmQtYm91bmRhcnkuanMmbW9kdWxlcz1EJTNBJTVDQ2xpZW50cyU1Q0RNSSU1Q0phdmEtYXBwJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNjb21wb25lbnRzJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyZtb2R1bGVzPUQlM0ElNUNDbGllbnRzJTVDRE1JJTVDSmF2YS1hcHAlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNzdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQTRIO0FBQzVILDBPQUFnSTtBQUNoSSx3T0FBK0g7QUFDL0gsa1BBQW9JO0FBQ3BJLHNRQUE4STtBQUM5SSIsInNvdXJjZXMiOlsid2VicGFjazovL2phdmEtbGVhcm5pbmctcGxhdGZvcm0vPzAxMTUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxDbGllbnRzXFxcXERNSVxcXFxKYXZhLWFwcFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGFwcC1yb3V0ZXIuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXENsaWVudHNcXFxcRE1JXFxcXEphdmEtYXBwXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXENsaWVudHNcXFxcRE1JXFxcXEphdmEtYXBwXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbGF5b3V0LXJvdXRlci5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcQ2xpZW50c1xcXFxETUlcXFxcSmF2YS1hcHBcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxub3QtZm91bmQtYm91bmRhcnkuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXENsaWVudHNcXFxcRE1JXFxcXEphdmEtYXBwXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxccmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcQ2xpZW50c1xcXFxETUlcXFxcSmF2YS1hcHBcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxzdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Ccomponents%5CProviders.tsx&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Ccomponents%5CProviders.tsx&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Providers.tsx */ \"(ssr)/./src/components/Providers.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q0NsaWVudHMlNUNETUklNUNKYXZhLWFwcCU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNmb250JTVDZ29vZ2xlJTVDdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPUQlM0ElNUNDbGllbnRzJTVDRE1JJTVDSmF2YS1hcHAlNUNub2RlX21vZHVsZXMlNUNyZWFjdC1ob3QtdG9hc3QlNUNkaXN0JTVDaW5kZXgubWpzJm1vZHVsZXM9RCUzQSU1Q0NsaWVudHMlNUNETUklNUNKYXZhLWFwcCU1Q3NyYyU1Q2FwcCU1Q2dsb2JhbHMuY3NzJm1vZHVsZXM9RCUzQSU1Q0NsaWVudHMlNUNETUklNUNKYXZhLWFwcCU1Q3NyYyU1Q2NvbXBvbmVudHMlNUNQcm92aWRlcnMudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzTUFBK0c7QUFDL0ciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9qYXZhLWxlYXJuaW5nLXBsYXRmb3JtLz80ZjhmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcQ2xpZW50c1xcXFxETUlcXFxcSmF2YS1hcHBcXFxcbm9kZV9tb2R1bGVzXFxcXHJlYWN0LWhvdC10b2FzdFxcXFxkaXN0XFxcXGluZGV4Lm1qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcQ2xpZW50c1xcXFxETUlcXFxcSmF2YS1hcHBcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcUHJvdmlkZXJzLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Ccomponents%5CProviders.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp%5Cpage.tsx&server=true!":
/*!*********************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp%5Cpage.tsx&server=true! ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q0NsaWVudHMlNUNETUklNUNKYXZhLWFwcCU1Q3NyYyU1Q2FwcCU1Q3BhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2phdmEtbGVhcm5pbmctcGxhdGZvcm0vPzAzNmUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxDbGllbnRzXFxcXERNSVxcXFxKYXZhLWFwcFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BookOpen_Code_Play_Star_Target_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BookOpen,Code,Play,Star,Target,Trophy,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BookOpen_Code_Play_Star_Target_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BookOpen,Code,Play,Star,Target,Trophy,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BookOpen_Code_Play_Star_Target_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BookOpen,Code,Play,Star,Target,Trophy,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BookOpen_Code_Play_Star_Target_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BookOpen,Code,Play,Star,Target,Trophy,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BookOpen_Code_Play_Star_Target_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BookOpen,Code,Play,Star,Target,Trophy,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BookOpen_Code_Play_Star_Target_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BookOpen,Code,Play,Star,Target,Trophy,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BookOpen_Code_Play_Star_Target_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BookOpen,Code,Play,Star,Target,Trophy,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BookOpen_Code_Play_Star_Target_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BookOpen,Code,Play,Star,Target,Trophy,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BookOpen_Code_Play_Star_Target_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BookOpen,Code,Play,Star,Target,Trophy,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction HomePage() {\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalUsers: 0,\n        lessonsCompleted: 0,\n        certificatesIssued: 0\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Animate stats on load\n        const animateStats = ()=>{\n            setTimeout(()=>setStats({\n                    totalUsers: 1250,\n                    lessonsCompleted: 8500,\n                    certificatesIssued: 320\n                }), 500);\n        };\n        animateStats();\n    }, []);\n    const features = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BookOpen_Code_Play_Star_Target_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 37,\n                columnNumber: 13\n            }, this),\n            title: \"Interactive Code Editor\",\n            description: \"Write and execute Java code in real-time with our advanced online IDE\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BookOpen_Code_Play_Star_Target_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 42,\n                columnNumber: 13\n            }, this),\n            title: \"Structured Lessons\",\n            description: \"Learn Java from basics to advanced concepts with step-by-step tutorials\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BookOpen_Code_Play_Star_Target_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 47,\n                columnNumber: 13\n            }, this),\n            title: \"Quizzes & Challenges\",\n            description: \"Test your knowledge with interactive quizzes and coding challenges\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BookOpen_Code_Play_Star_Target_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 52,\n                columnNumber: 13\n            }, this),\n            title: \"Certificates\",\n            description: \"Earn certificates upon completion to showcase your Java skills\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BookOpen_Code_Play_Star_Target_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 57,\n                columnNumber: 13\n            }, this),\n            title: \"Progress Tracking\",\n            description: \"Monitor your learning journey with detailed progress analytics\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BookOpen_Code_Play_Star_Target_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 62,\n                columnNumber: 13\n            }, this),\n            title: \"Real-time Execution\",\n            description: \"See your code results instantly with our secure execution environment\"\n        }\n    ];\n    const testimonials = [\n        {\n            name: \"Sarah Johnson\",\n            role: \"Computer Science Student\",\n            content: \"This platform made learning Java so much easier! The interactive exercises really helped me understand the concepts.\",\n            rating: 5\n        },\n        {\n            name: \"Mike Chen\",\n            role: \"Software Developer\",\n            content: \"Great way to brush up on Java fundamentals. The real-time code execution is fantastic!\",\n            rating: 5\n        },\n        {\n            name: \"Emily Davis\",\n            role: \"Career Changer\",\n            content: \"I went from zero programming knowledge to confident Java developer thanks to this platform.\",\n            rating: 5\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BookOpen_Code_Play_Star_Target_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-8 h-8 text-primary-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-xl font-bold text-gray-900\",\n                                        children: \"JavaLearn\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex items-center space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/lessons\",\n                                        className: \"text-gray-600 hover:text-primary-600 transition-colors\",\n                                        children: \"Lessons\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/practice\",\n                                        className: \"text-gray-600 hover:text-primary-600 transition-colors\",\n                                        children: \"Practice\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/certificates\",\n                                        className: \"text-gray-600 hover:text-primary-600 transition-colors\",\n                                        children: \"Certificates\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/auth/login\",\n                                        className: \"text-gray-600 hover:text-primary-600 transition-colors\",\n                                        children: \"Login\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/auth/register\",\n                                        className: \"btn-primary\",\n                                        children: \"Get Started\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-gradient-to-br from-primary-50 to-secondary-50 py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.h1, {\n                                className: \"text-4xl md:text-6xl font-bold text-gray-900 mb-6\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6\n                                },\n                                children: [\n                                    \"Master Java Programming\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-primary-600\",\n                                        children: \" Interactively\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.p, {\n                                className: \"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.2\n                                },\n                                children: \"Learn Java through hands-on coding exercises, real-time execution, interactive quizzes, and earn certificates to validate your skills.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.4\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/auth/register\",\n                                        className: \"btn-primary text-lg px-8 py-3 inline-flex items-center\",\n                                        children: [\n                                            \"Start Learning Free\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BookOpen_Code_Play_Star_Target_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"ml-2 w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/demo\",\n                                        className: \"btn-secondary text-lg px-8 py-3 inline-flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BookOpen_Code_Play_Star_Target_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"mr-2 w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Watch Demo\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                className: \"p-6\",\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.8\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.2\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl font-bold text-primary-600 mb-2\",\n                                        children: [\n                                            stats.totalUsers.toLocaleString(),\n                                            \"+\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Active Learners\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                className: \"p-6\",\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.8\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.4\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl font-bold text-secondary-600 mb-2\",\n                                        children: [\n                                            stats.lessonsCompleted.toLocaleString(),\n                                            \"+\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Lessons Completed\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                className: \"p-6\",\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.8\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.6\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl font-bold text-yellow-600 mb-2\",\n                                        children: [\n                                            stats.certificatesIssued.toLocaleString(),\n                                            \"+\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Certificates Issued\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                                    children: \"Why Choose JavaLearn?\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                                    children: \"Our platform combines the best of interactive learning with practical coding experience\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                    className: \"card p-6 hover:shadow-lg transition-shadow duration-300\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: index * 0.1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-primary-600 mb-4\",\n                                            children: feature.icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                            children: feature.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: feature.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                                children: \"What Our Students Say\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                            children: testimonials.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                    className: \"card p-6\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: index * 0.2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex mb-4\",\n                                            children: [\n                                                ...Array(testimonial.rating)\n                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BookOpen_Code_Play_Star_Target_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-5 h-5 text-yellow-400 fill-current\"\n                                                }, i, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: [\n                                                '\"',\n                                                testimonial.content,\n                                                '\"'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: testimonial.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: testimonial.role\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 232,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-primary-600\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-white mb-4\",\n                            children: \"Ready to Start Your Java Journey?\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-primary-100 mb-8 max-w-2xl mx-auto\",\n                            children: \"Join thousands of students who are already mastering Java programming with our interactive platform.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/auth/register\",\n                            className: \"bg-white text-primary-600 hover:bg-gray-100 font-bold py-3 px-8 rounded-lg text-lg transition-colors duration-200 inline-flex items-center\",\n                            children: [\n                                \"Get Started Today\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BookOpen_Code_Play_Star_Target_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"ml-2 w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 265,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gray-900 text-white py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BookOpen_Code_Play_Star_Target_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"w-8 h-8 text-primary-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-2 text-xl font-bold\",\n                                                    children: \"JavaLearn\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400\",\n                                            children: \"Interactive Java programming education platform for all skill levels.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Learn\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/lessons\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"Lessons\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/practice\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"Practice\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/quizzes\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"Quizzes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/certificates\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"Certificates\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Support\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/help\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"Help Center\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/contact\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"Contact Us\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/community\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"Community\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Company\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/about\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"About\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/privacy\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"Privacy\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/terms\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"Terms\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"\\xa9 2024 JavaLearn. All rights reserved.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 282,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 90,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Providers.tsx":
/*!**************************************!*\
  !*** ./src/components/Providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\Providers.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Qcm92aWRlcnMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUVpRDtBQU8xQyxTQUFTQyxVQUFVLEVBQUVDLFFBQVEsRUFBa0I7SUFDcEQscUJBQ0UsOERBQUNGLDREQUFlQTtrQkFDYkU7Ozs7OztBQUdQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vamF2YS1sZWFybmluZy1wbGF0Zm9ybS8uL3NyYy9jb21wb25lbnRzL1Byb3ZpZGVycy50c3g/YjAxOSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0J1xuaW1wb3J0IHsgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnXG5cbmludGVyZmFjZSBQcm92aWRlcnNQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFByb3ZpZGVycyh7IGNoaWxkcmVuIH06IFByb3ZpZGVyc1Byb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPFNlc3Npb25Qcm92aWRlcj5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L1Nlc3Npb25Qcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlNlc3Npb25Qcm92aWRlciIsIlByb3ZpZGVycyIsImNoaWxkcmVuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Providers.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e05ba106f0bb\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vamF2YS1sZWFybmluZy1wbGF0Zm9ybS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/ZTAyMyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImUwNWJhMTA2ZjBiYlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_Providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Providers */ \"(rsc)/./src/components/Providers.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\nconst metadata = {\n    title: \"Java Learning Platform - Interactive Programming Education\",\n    description: \"Learn Java programming through interactive lessons, real-time code execution, quizzes, and earn certificates.\",\n    keywords: \"Java, programming, education, interactive, coding, tutorials, certificates\",\n    authors: [\n        {\n            name: \"Java Learning Platform\"\n        }\n    ]\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 4000,\n                            style: {\n                                background: \"#363636\",\n                                color: \"#fff\"\n                            },\n                            success: {\n                                duration: 3000,\n                                iconTheme: {\n                                    primary: \"#22c55e\",\n                                    secondary: \"#fff\"\n                                }\n                            },\n                            error: {\n                                duration: 5000,\n                                iconTheme: {\n                                    primary: \"#ef4444\",\n                                    secondary: \"#fff\"\n                                }\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Clients\DMI\Java-app\src\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/Providers.tsx":
/*!**************************************!*\
  !*** ./src/components/Providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Clients\DMI\Java-app\src\components\Providers.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Clients\DMI\Java-app\src\components\Providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/react-hot-toast","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/framer-motion","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CClients%5CDMI%5CJava-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();