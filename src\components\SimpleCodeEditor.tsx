'use client'

import { useState } from 'react'
import { Play } from 'lucide-react'
import toast from 'react-hot-toast'

interface SimpleCodeEditorProps {
  initialCode?: string
  onRun?: (code: string) => void
}

export default function SimpleCodeEditor({ 
  initialCode = 'public class Test {\n    public static void main(String[] args) {\n        System.out.println("Hello, World!");\n    }\n}',
  onRun
}: SimpleCodeEditorProps) {
  const [code, setCode] = useState(initialCode)
  const [output, setOutput] = useState('')
  const [isRunning, setIsRunning] = useState(false)

  const handleRunCode = async () => {
    console.log('🚀 SimpleCodeEditor: Run button clicked')
    
    if (isRunning) {
      console.log('⚠️ Already running, ignoring click')
      return
    }
    
    setIsRunning(true)
    setOutput('Executing...')
    
    try {
      console.log('📝 Code to execute:', code.substring(0, 100) + '...')
      
      // If parent provides onRun, use it
      if (onRun) {
        console.log('🔄 Using parent onRun function')
        onRun(code)
        setIsRunning(false)
        return
      }

      console.log('📡 Making API request to /api/execute')
      
      const response = await fetch('/api/execute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code: code,
          language: 'java'
        })
      })

      console.log('📡 Response received:', response.status, response.statusText)

      if (!response.ok) {
        const errorText = await response.text()
        console.log('❌ Response not OK:', errorText)
        throw new Error(`HTTP ${response.status}: ${errorText}`)
      }

      const result = await response.json()
      console.log('✅ API Result:', result)

      if (result.success) {
        setOutput(result.output || 'No output')
        toast.success('Code executed successfully!')
      } else {
        setOutput(`Error: ${result.error}`)
        toast.error('Code execution failed!')
      }

    } catch (error) {
      console.error('💥 Execution error:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      setOutput(`Error: ${errorMessage}`)
      toast.error('Failed to execute code!')
    } finally {
      setIsRunning(false)
    }
  }

  return (
    <div className="border border-gray-300 rounded-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gray-800 text-white px-4 py-2 flex items-center justify-between">
        <span className="text-sm font-medium">Simple Java Editor</span>
        <button
          onClick={handleRunCode}
          disabled={isRunning}
          className={`flex items-center space-x-1 px-3 py-1 rounded text-sm transition-colors ${
            isRunning 
              ? 'bg-gray-600 cursor-not-allowed' 
              : 'bg-green-600 hover:bg-green-700'
          }`}
        >
          <Play className="w-4 h-4" />
          <span>{isRunning ? 'Running...' : 'Run'}</span>
        </button>
      </div>

      {/* Code Input */}
      <textarea
        value={code}
        onChange={(e) => setCode(e.target.value)}
        className="w-full h-64 p-4 font-mono text-sm border-none resize-none focus:outline-none"
        placeholder="Enter your Java code here..."
      />

      {/* Output */}
      <div className="border-t border-gray-200">
        <div className="bg-gray-100 px-4 py-2 border-b border-gray-200">
          <span className="text-sm font-medium text-gray-700">Output</span>
        </div>
        <div className="p-4 bg-gray-900 text-green-400 font-mono text-sm min-h-[100px]">
          {output || 'Click "Run" to execute your code'}
        </div>
      </div>
    </div>
  )
}
