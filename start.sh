#!/bin/bash

# Java Learning Platform Startup Script
echo "🚀 Starting Java Learning Platform..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ and try again."
    exit 1
fi

# Check if Java is installed
if ! command -v java &> /dev/null; then
    echo "⚠️  Java is not installed. Code execution will not work without Java JDK 17+."
    echo "   You can still run the platform, but install Java for full functionality."
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "⚠️  Docker is not installed. Using local Java execution instead."
    echo "   Install Docker for enhanced security and isolation."
fi

# Create .env.local if it doesn't exist
if [ ! -f .env.local ]; then
    echo "📝 Creating environment configuration..."
    cat > .env.local << 'EOF'
# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=java-learning-platform-secret-key-for-development-only

# OAuth Providers (Optional - commented out to avoid errors)
# GOOGLE_CLIENT_ID=
# GOOGLE_CLIENT_SECRET=
# GITHUB_ID=
# GITHUB_SECRET=

# Backend Server URL
BACKEND_URL=http://localhost:3001

# Development Settings
NODE_ENV=development
EOF
    echo "✅ Created .env.local with default configuration"
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Create temp directory for code execution
mkdir -p server/temp

# Start the application
echo "🎯 Starting development servers..."
echo "   Frontend: http://localhost:3000"
echo "   Backend:  http://localhost:3001"
echo ""

# Start backend server in background
echo "Starting backend server..."
npm run dev:server &
BACKEND_PID=$!

# Wait a moment for backend to start
sleep 3

# Start frontend server
echo "Starting frontend server..."
npm run dev &
FRONTEND_PID=$!

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Shutting down servers..."
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

echo ""
echo "✅ Java Learning Platform is running!"
echo "   Open http://localhost:3000 in your browser"
echo "   Press Ctrl+C to stop the servers"
echo ""

# Wait for user to stop the script
wait
