'use client'

import { useState, useEffect } from 'react'
import <PERSON> from 'next/link'
import { motion } from 'framer-motion'
import { 
  BookOpen, 
  Clock, 
  Target, 
  Play, 
  CheckCircle,
  Code,
  ArrowLeft,
  Star,
  Users
} from 'lucide-react'

interface Lesson {
  id: number
  title: string
  description: string
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced'
  duration: string
  completed: boolean
  progress: number
  topics: string[]
}

export default function LessonsPage() {
  const [lessons, setLessons] = useState<Lesson[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState<'all' | 'beginner' | 'intermediate' | 'advanced'>('all')

  useEffect(() => {
    // Mock lessons data
    const mockLessons: Lesson[] = [
      {
        id: 1,
        title: 'Java Basics: Variables and Data Types',
        description: 'Learn about variables, primitive data types, and how to declare and use them in Java.',
        difficulty: 'Beginner',
        duration: '30 min',
        completed: false,
        progress: 0,
        topics: ['Variables', 'Data Types', 'Declaration', 'Initialization']
      },
      {
        id: 2,
        title: 'Control Structures: If Statements and Loops',
        description: 'Master conditional statements and loops to control program flow.',
        difficulty: 'Beginner',
        duration: '45 min',
        completed: false,
        progress: 0,
        topics: ['If Statements', 'For Loops', 'While Loops', 'Switch Cases']
      },
      {
        id: 3,
        title: 'Methods and Functions',
        description: 'Learn how to create reusable code with methods and understand parameters.',
        difficulty: 'Beginner',
        duration: '40 min',
        completed: false,
        progress: 0,
        topics: ['Method Declaration', 'Parameters', 'Return Types', 'Method Overloading']
      },
      {
        id: 4,
        title: 'Object-Oriented Programming: Classes and Objects',
        description: 'Introduction to OOP concepts with classes, objects, and encapsulation.',
        difficulty: 'Intermediate',
        duration: '60 min',
        completed: false,
        progress: 0,
        topics: ['Classes', 'Objects', 'Constructors', 'Encapsulation']
      },
      {
        id: 5,
        title: 'Arrays and Collections',
        description: 'Work with arrays and Java collections like ArrayList and HashMap.',
        difficulty: 'Intermediate',
        duration: '50 min',
        completed: false,
        progress: 0,
        topics: ['Arrays', 'ArrayList', 'HashMap', 'Iteration']
      },
      {
        id: 6,
        title: 'Exception Handling',
        description: 'Learn to handle errors gracefully with try-catch blocks and custom exceptions.',
        difficulty: 'Advanced',
        duration: '45 min',
        completed: false,
        progress: 0,
        topics: ['Try-Catch', 'Finally Block', 'Custom Exceptions', 'Throws']
      }
    ]

    setTimeout(() => {
      setLessons(mockLessons)
      setLoading(false)
    }, 1000)
  }, [])

  const filteredLessons = lessons.filter(lesson => {
    if (filter === 'all') return true
    return lesson.difficulty.toLowerCase() === filter
  })

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return 'bg-green-100 text-green-800'
      case 'Intermediate': return 'bg-yellow-100 text-yellow-800'
      case 'Advanced': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/dashboard" className="flex items-center text-gray-600 hover:text-primary-600 transition-colors mr-4">
                <ArrowLeft className="w-5 h-5 mr-1" />
                Back to Dashboard
              </Link>
              <Code className="w-8 h-8 text-primary-600" />
              <span className="ml-2 text-xl font-bold text-gray-900">JavaLearn</span>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/quizzes" className="text-gray-600 hover:text-primary-600 transition-colors">
                Quizzes
              </Link>
              <Link href="/practice" className="text-gray-600 hover:text-primary-600 transition-colors">
                Practice
              </Link>
              <Link href="/certificates" className="text-gray-600 hover:text-primary-600 transition-colors">
                Certificates
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div 
          className="mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Java Programming Lessons</h1>
          <p className="text-gray-600">Master Java programming with our interactive lessons</p>
        </motion.div>

        {/* Filter Tabs */}
        <motion.div 
          className="mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: 'all', label: 'All Lessons' },
                { id: 'beginner', label: 'Beginner' },
                { id: 'intermediate', label: 'Intermediate' },
                { id: 'advanced', label: 'Advanced' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setFilter(tab.id as any)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                    filter === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </motion.div>

        {/* Lessons Grid */}
        <motion.div 
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {filteredLessons.map((lesson, index) => (
            <motion.div
              key={lesson.id}
              className="card p-6 hover:shadow-lg transition-shadow duration-300"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <div className="flex items-center justify-between mb-4">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(lesson.difficulty)}`}>
                  {lesson.difficulty}
                </span>
                {lesson.completed && (
                  <CheckCircle className="w-5 h-5 text-green-600" />
                )}
              </div>

              <h3 className="text-xl font-semibold text-gray-900 mb-2">{lesson.title}</h3>
              <p className="text-gray-600 mb-4 text-sm">{lesson.description}</p>

              <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                <span className="flex items-center">
                  <Clock className="w-4 h-4 mr-1" />
                  {lesson.duration}
                </span>
                <span className="flex items-center">
                  <BookOpen className="w-4 h-4 mr-1" />
                  {lesson.topics.length} topics
                </span>
              </div>

              <div className="mb-4">
                <div className="flex flex-wrap gap-1">
                  {lesson.topics.slice(0, 3).map((topic, topicIndex) => (
                    <span 
                      key={topicIndex}
                      className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded"
                    >
                      {topic}
                    </span>
                  ))}
                  {lesson.topics.length > 3 && (
                    <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                      +{lesson.topics.length - 3} more
                    </span>
                  )}
                </div>
              </div>

              {lesson.progress > 0 && (
                <div className="mb-4">
                  <div className="flex justify-between text-sm text-gray-600 mb-1">
                    <span>Progress</span>
                    <span>{lesson.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${lesson.progress}%` }}
                    ></div>
                  </div>
                </div>
              )}

              <Link 
                href={`/lessons/${lesson.id}`}
                className="w-full btn-primary text-center inline-flex items-center justify-center"
              >
                <Play className="w-4 h-4 mr-2" />
                {lesson.completed ? 'Review Lesson' : 'Start Lesson'}
              </Link>
            </motion.div>
          ))}
        </motion.div>

        {/* Stats Section */}
        <motion.div 
          className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <div className="card p-6 text-center">
            <BookOpen className="w-8 h-8 text-primary-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">{lessons.length}</div>
            <div className="text-gray-600">Total Lessons</div>
          </div>
          <div className="card p-6 text-center">
            <Target className="w-8 h-8 text-secondary-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">
              {lessons.filter(l => l.completed).length}
            </div>
            <div className="text-gray-600">Completed</div>
          </div>
          <div className="card p-6 text-center">
            <Star className="w-8 h-8 text-yellow-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">
              {Math.round(lessons.filter(l => l.completed).length / lessons.length * 100) || 0}%
            </div>
            <div className="text-gray-600">Progress</div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}
