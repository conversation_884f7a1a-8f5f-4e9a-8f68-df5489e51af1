/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/simple-test/page";
exports.ids = ["app/simple-test/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsimple-test%2Fpage&page=%2Fsimple-test%2Fpage&appPaths=%2Fsimple-test%2Fpage&pagePath=private-next-app-dir%2Fsimple-test%2Fpage.tsx&appDir=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CClients%5CDMI%5CJava-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsimple-test%2Fpage&page=%2Fsimple-test%2Fpage&appPaths=%2Fsimple-test%2Fpage&pagePath=private-next-app-dir%2Fsimple-test%2Fpage.tsx&appDir=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CClients%5CDMI%5CJava-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'simple-test',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/simple-test/page.tsx */ \"(rsc)/./src/app/simple-test/page.tsx\")), \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/simple-test/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/simple-test/page\",\n        pathname: \"/simple-test\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsimple-test%2Fpage&page=%2Fsimple-test%2Fpage&appPaths=%2Fsimple-test%2Fpage&pagePath=private-next-app-dir%2Fsimple-test%2Fpage.tsx&appDir=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CClients%5CDMI%5CJava-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Ccomponents%5CProviders.tsx&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Ccomponents%5CProviders.tsx&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Providers.tsx */ \"(ssr)/./src/components/Providers.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q0NsaWVudHMlNUNETUklNUNKYXZhLWFwcCU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNmb250JTVDZ29vZ2xlJTVDdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPUQlM0ElNUNDbGllbnRzJTVDRE1JJTVDSmF2YS1hcHAlNUNub2RlX21vZHVsZXMlNUNyZWFjdC1ob3QtdG9hc3QlNUNkaXN0JTVDaW5kZXgubWpzJm1vZHVsZXM9RCUzQSU1Q0NsaWVudHMlNUNETUklNUNKYXZhLWFwcCU1Q3NyYyU1Q2FwcCU1Q2dsb2JhbHMuY3NzJm1vZHVsZXM9RCUzQSU1Q0NsaWVudHMlNUNETUklNUNKYXZhLWFwcCU1Q3NyYyU1Q2NvbXBvbmVudHMlNUNQcm92aWRlcnMudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzTUFBK0c7QUFDL0ciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9qYXZhLWxlYXJuaW5nLXBsYXRmb3JtLz80ZjhmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcQ2xpZW50c1xcXFxETUlcXFxcSmF2YS1hcHBcXFxcbm9kZV9tb2R1bGVzXFxcXHJlYWN0LWhvdC10b2FzdFxcXFxkaXN0XFxcXGluZGV4Lm1qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcQ2xpZW50c1xcXFxETUlcXFxcSmF2YS1hcHBcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcUHJvdmlkZXJzLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5CClients%5CDMI%5CJava-app%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Ccomponents%5CProviders.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp%5Csimple-test%5Cpage.tsx&server=true!":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp%5Csimple-test%5Cpage.tsx&server=true! ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/simple-test/page.tsx */ \"(ssr)/./src/app/simple-test/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q0NsaWVudHMlNUNETUklNUNKYXZhLWFwcCU1Q3NyYyU1Q2FwcCU1Q3NpbXBsZS10ZXN0JTVDcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vamF2YS1sZWFybmluZy1wbGF0Zm9ybS8/NDk0YSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXENsaWVudHNcXFxcRE1JXFxcXEphdmEtYXBwXFxcXHNyY1xcXFxhcHBcXFxcc2ltcGxlLXRlc3RcXFxccGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp%5Csimple-test%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/simple-test/page.tsx":
/*!**************************************!*\
  !*** ./src/app/simple-test/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleTestPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SimpleCodeEditor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/SimpleCodeEditor */ \"(ssr)/./src/components/SimpleCodeEditor.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bug_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bug!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction SimpleTestPage() {\n    const [apiStatus, setApiStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Not tested\");\n    const testAPI = async ()=>{\n        try {\n            setApiStatus(\"Testing...\");\n            const response = await fetch(\"/api/test\");\n            const result = await response.json();\n            setApiStatus(`✅ API Working: ${result.message}`);\n        } catch (error) {\n            setApiStatus(`❌ API Failed: ${error}`);\n        }\n    };\n    const testExecuteAPI = async ()=>{\n        try {\n            setApiStatus(\"Testing execute API...\");\n            const response = await fetch(\"/api/execute\");\n            const result = await response.json();\n            setApiStatus(`✅ Execute API Working: ${result.message}`);\n        } catch (error) {\n            setApiStatus(`❌ Execute API Failed: ${error}`);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bug_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-8 h-8 text-red-600 mr-3\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: \"Simple Code Execution Test\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"API Status\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: testAPI,\n                                    className: \"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600\",\n                                    children: \"Test Basic API\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: testExecuteAPI,\n                                    className: \"bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600\",\n                                    children: \"Test Execute API\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 bg-gray-100 rounded\",\n                            children: [\n                                \"Status: \",\n                                apiStatus\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"Simple Code Editor Test\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"This is a simplified version of the code editor. If this works, we know the issue is with the Monaco editor.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SimpleCodeEditor__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            initialCode: `public class SimpleTest {\n    public static void main(String[] args) {\n        System.out.println(\"Hello from Simple Editor!\");\n        System.out.println(\"Testing basic execution\");\n        \n        int x = 5;\n        int y = 10;\n        System.out.println(\"Sum: \" + (x + y));\n    }\n}`\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6 mt-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"Test Instructions\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                            className: \"list-decimal list-inside space-y-2 text-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"First, test the API endpoints using the buttons above\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Then, try running code in the Simple Code Editor\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Check the browser console (F12) for detailed logs\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"If this works, the issue is with the Monaco editor integration\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"If this doesn't work, the issue is with the API or network\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6 mt-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"Direct API Test\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"You can also test the API directly by visiting:\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"list-disc list-inside space-y-1 text-blue-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/api/test\",\n                                        target: \"_blank\",\n                                        className: \"hover:underline\",\n                                        children: \"/api/test\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/api/execute\",\n                                        target: \"_blank\",\n                                        className: \"hover:underline\",\n                                        children: \"/api/execute\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/test-api.html\",\n                                        target: \"_blank\",\n                                        className: \"hover:underline\",\n                                        children: \"/test-api.html\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3NpbXBsZS10ZXN0L3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRWdDO0FBQzRCO0FBQzFCO0FBRW5CLFNBQVNHO0lBQ3RCLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHTCwrQ0FBUUEsQ0FBUztJQUVuRCxNQUFNTSxVQUFVO1FBQ2QsSUFBSTtZQUNGRCxhQUFhO1lBQ2IsTUFBTUUsV0FBVyxNQUFNQyxNQUFNO1lBQzdCLE1BQU1DLFNBQVMsTUFBTUYsU0FBU0csSUFBSTtZQUNsQ0wsYUFBYSxDQUFDLGVBQWUsRUFBRUksT0FBT0UsT0FBTyxDQUFDLENBQUM7UUFDakQsRUFBRSxPQUFPQyxPQUFPO1lBQ2RQLGFBQWEsQ0FBQyxjQUFjLEVBQUVPLE1BQU0sQ0FBQztRQUN2QztJQUNGO0lBRUEsTUFBTUMsaUJBQWlCO1FBQ3JCLElBQUk7WUFDRlIsYUFBYTtZQUNiLE1BQU1FLFdBQVcsTUFBTUMsTUFBTTtZQUM3QixNQUFNQyxTQUFTLE1BQU1GLFNBQVNHLElBQUk7WUFDbENMLGFBQWEsQ0FBQyx1QkFBdUIsRUFBRUksT0FBT0UsT0FBTyxDQUFDLENBQUM7UUFDekQsRUFBRSxPQUFPQyxPQUFPO1lBQ2RQLGFBQWEsQ0FBQyxzQkFBc0IsRUFBRU8sTUFBTSxDQUFDO1FBQy9DO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ0U7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ2IsK0VBQUdBOzRCQUFDYSxXQUFVOzs7Ozs7c0NBQ2YsOERBQUNDOzRCQUFHRCxXQUFVO3NDQUFtQzs7Ozs7Ozs7Ozs7OzhCQUluRCw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRTs0QkFBR0YsV0FBVTtzQ0FBNkI7Ozs7OztzQ0FDM0MsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0c7b0NBQ0NDLFNBQVNiO29DQUNUUyxXQUFVOzhDQUNYOzs7Ozs7OENBR0QsOERBQUNHO29DQUNDQyxTQUFTTjtvQ0FDVEUsV0FBVTs4Q0FDWDs7Ozs7Ozs7Ozs7O3NDQUlILDhEQUFDRDs0QkFBSUMsV0FBVTs7Z0NBQTBCO2dDQUM5Qlg7Ozs7Ozs7Ozs7Ozs7OEJBS2IsOERBQUNVO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0U7NEJBQUdGLFdBQVU7c0NBQTZCOzs7Ozs7c0NBQzNDLDhEQUFDSzs0QkFBRUwsV0FBVTtzQ0FBcUI7Ozs7OztzQ0FJbEMsOERBQUNkLG9FQUFnQkE7NEJBQ2ZvQixhQUFhLENBQUM7Ozs7Ozs7OztDQVN6QixDQUFDOzs7Ozs7Ozs7Ozs7OEJBS00sOERBQUNQO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0U7NEJBQUdGLFdBQVU7c0NBQTZCOzs7Ozs7c0NBQzNDLDhEQUFDTzs0QkFBR1AsV0FBVTs7OENBQ1osOERBQUNROzhDQUFHOzs7Ozs7OENBQ0osOERBQUNBOzhDQUFHOzs7Ozs7OENBQ0osOERBQUNBOzhDQUFHOzs7Ozs7OENBQ0osOERBQUNBOzhDQUFHOzs7Ozs7OENBQ0osOERBQUNBOzhDQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBS1IsOERBQUNUO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0U7NEJBQUdGLFdBQVU7c0NBQTZCOzs7Ozs7c0NBQzNDLDhEQUFDSzs0QkFBRUwsV0FBVTtzQ0FBcUI7Ozs7OztzQ0FHbEMsOERBQUNTOzRCQUFHVCxXQUFVOzs4Q0FDWiw4REFBQ1E7OENBQUcsNEVBQUNFO3dDQUFFQyxNQUFLO3dDQUFZQyxRQUFPO3dDQUFTWixXQUFVO2tEQUFrQjs7Ozs7Ozs7Ozs7OENBQ3BFLDhEQUFDUTs4Q0FBRyw0RUFBQ0U7d0NBQUVDLE1BQUs7d0NBQWVDLFFBQU87d0NBQVNaLFdBQVU7a0RBQWtCOzs7Ozs7Ozs7Ozs4Q0FDdkUsOERBQUNROzhDQUFHLDRFQUFDRTt3Q0FBRUMsTUFBSzt3Q0FBaUJDLFFBQU87d0NBQVNaLFdBQVU7a0RBQWtCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTXJGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vamF2YS1sZWFybmluZy1wbGF0Zm9ybS8uL3NyYy9hcHAvc2ltcGxlLXRlc3QvcGFnZS50c3g/NmE2NiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcbmltcG9ydCBTaW1wbGVDb2RlRWRpdG9yIGZyb20gJ0AvY29tcG9uZW50cy9TaW1wbGVDb2RlRWRpdG9yJ1xuaW1wb3J0IHsgQnVnIH0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTaW1wbGVUZXN0UGFnZSgpIHtcbiAgY29uc3QgW2FwaVN0YXR1cywgc2V0QXBpU3RhdHVzXSA9IHVzZVN0YXRlPHN0cmluZz4oJ05vdCB0ZXN0ZWQnKVxuXG4gIGNvbnN0IHRlc3RBUEkgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldEFwaVN0YXR1cygnVGVzdGluZy4uLicpXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL3Rlc3QnKVxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG4gICAgICBzZXRBcGlTdGF0dXMoYOKchSBBUEkgV29ya2luZzogJHtyZXN1bHQubWVzc2FnZX1gKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBzZXRBcGlTdGF0dXMoYOKdjCBBUEkgRmFpbGVkOiAke2Vycm9yfWApXG4gICAgfVxuICB9XG5cbiAgY29uc3QgdGVzdEV4ZWN1dGVBUEkgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldEFwaVN0YXR1cygnVGVzdGluZyBleGVjdXRlIEFQSS4uLicpXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2V4ZWN1dGUnKVxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG4gICAgICBzZXRBcGlTdGF0dXMoYOKchSBFeGVjdXRlIEFQSSBXb3JraW5nOiAke3Jlc3VsdC5tZXNzYWdlfWApXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHNldEFwaVN0YXR1cyhg4p2MIEV4ZWN1dGUgQVBJIEZhaWxlZDogJHtlcnJvcn1gKVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MCBwLThcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctNHhsIG14LWF1dG9cIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBtYi04XCI+XG4gICAgICAgICAgPEJ1ZyBjbGFzc05hbWU9XCJ3LTggaC04IHRleHQtcmVkLTYwMCBtci0zXCIgLz5cbiAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj5TaW1wbGUgQ29kZSBFeGVjdXRpb24gVGVzdDwvaDE+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBBUEkgU3RhdHVzICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93IHAtNiBtYi02XCI+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCBtYi00XCI+QVBJIFN0YXR1czwvaDI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtNCBtYi00XCI+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e3Rlc3RBUEl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWJsdWUtNTAwIHRleHQtd2hpdGUgcHgtNCBweS0yIHJvdW5kZWQgaG92ZXI6YmctYmx1ZS02MDBcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBUZXN0IEJhc2ljIEFQSVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e3Rlc3RFeGVjdXRlQVBJfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmVlbi01MDAgdGV4dC13aGl0ZSBweC00IHB5LTIgcm91bmRlZCBob3ZlcjpiZy1ncmVlbi02MDBcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBUZXN0IEV4ZWN1dGUgQVBJXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMyBiZy1ncmF5LTEwMCByb3VuZGVkXCI+XG4gICAgICAgICAgICBTdGF0dXM6IHthcGlTdGF0dXN9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBTaW1wbGUgQ29kZSBFZGl0b3IgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3cgcC02XCI+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCBtYi00XCI+U2ltcGxlIENvZGUgRWRpdG9yIFRlc3Q8L2gyPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNFwiPlxuICAgICAgICAgICAgVGhpcyBpcyBhIHNpbXBsaWZpZWQgdmVyc2lvbiBvZiB0aGUgY29kZSBlZGl0b3IuIElmIHRoaXMgd29ya3MsIHdlIGtub3cgdGhlIGlzc3VlIGlzIHdpdGggdGhlIE1vbmFjbyBlZGl0b3IuXG4gICAgICAgICAgPC9wPlxuICAgICAgICAgIFxuICAgICAgICAgIDxTaW1wbGVDb2RlRWRpdG9yIFxuICAgICAgICAgICAgaW5pdGlhbENvZGU9e2BwdWJsaWMgY2xhc3MgU2ltcGxlVGVzdCB7XG4gICAgcHVibGljIHN0YXRpYyB2b2lkIG1haW4oU3RyaW5nW10gYXJncykge1xuICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCJIZWxsbyBmcm9tIFNpbXBsZSBFZGl0b3IhXCIpO1xuICAgICAgICBTeXN0ZW0ub3V0LnByaW50bG4oXCJUZXN0aW5nIGJhc2ljIGV4ZWN1dGlvblwiKTtcbiAgICAgICAgXG4gICAgICAgIGludCB4ID0gNTtcbiAgICAgICAgaW50IHkgPSAxMDtcbiAgICAgICAgU3lzdGVtLm91dC5wcmludGxuKFwiU3VtOiBcIiArICh4ICsgeSkpO1xuICAgIH1cbn1gfVxuICAgICAgICAgIC8+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBJbnN0cnVjdGlvbnMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3cgcC02IG10LTZcIj5cbiAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIG1iLTRcIj5UZXN0IEluc3RydWN0aW9uczwvaDI+XG4gICAgICAgICAgPG9sIGNsYXNzTmFtZT1cImxpc3QtZGVjaW1hbCBsaXN0LWluc2lkZSBzcGFjZS15LTIgdGV4dC1ncmF5LTcwMFwiPlxuICAgICAgICAgICAgPGxpPkZpcnN0LCB0ZXN0IHRoZSBBUEkgZW5kcG9pbnRzIHVzaW5nIHRoZSBidXR0b25zIGFib3ZlPC9saT5cbiAgICAgICAgICAgIDxsaT5UaGVuLCB0cnkgcnVubmluZyBjb2RlIGluIHRoZSBTaW1wbGUgQ29kZSBFZGl0b3I8L2xpPlxuICAgICAgICAgICAgPGxpPkNoZWNrIHRoZSBicm93c2VyIGNvbnNvbGUgKEYxMikgZm9yIGRldGFpbGVkIGxvZ3M8L2xpPlxuICAgICAgICAgICAgPGxpPklmIHRoaXMgd29ya3MsIHRoZSBpc3N1ZSBpcyB3aXRoIHRoZSBNb25hY28gZWRpdG9yIGludGVncmF0aW9uPC9saT5cbiAgICAgICAgICAgIDxsaT5JZiB0aGlzIGRvZXNuJ3Qgd29yaywgdGhlIGlzc3VlIGlzIHdpdGggdGhlIEFQSSBvciBuZXR3b3JrPC9saT5cbiAgICAgICAgICA8L29sPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogRGlyZWN0IEFQSSBUZXN0ICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93IHAtNiBtdC02XCI+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCBtYi00XCI+RGlyZWN0IEFQSSBUZXN0PC9oMj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG1iLTRcIj5cbiAgICAgICAgICAgIFlvdSBjYW4gYWxzbyB0ZXN0IHRoZSBBUEkgZGlyZWN0bHkgYnkgdmlzaXRpbmc6XG4gICAgICAgICAgPC9wPlxuICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJsaXN0LWRpc2MgbGlzdC1pbnNpZGUgc3BhY2UteS0xIHRleHQtYmx1ZS02MDBcIj5cbiAgICAgICAgICAgIDxsaT48YSBocmVmPVwiL2FwaS90ZXN0XCIgdGFyZ2V0PVwiX2JsYW5rXCIgY2xhc3NOYW1lPVwiaG92ZXI6dW5kZXJsaW5lXCI+L2FwaS90ZXN0PC9hPjwvbGk+XG4gICAgICAgICAgICA8bGk+PGEgaHJlZj1cIi9hcGkvZXhlY3V0ZVwiIHRhcmdldD1cIl9ibGFua1wiIGNsYXNzTmFtZT1cImhvdmVyOnVuZGVybGluZVwiPi9hcGkvZXhlY3V0ZTwvYT48L2xpPlxuICAgICAgICAgICAgPGxpPjxhIGhyZWY9XCIvdGVzdC1hcGkuaHRtbFwiIHRhcmdldD1cIl9ibGFua1wiIGNsYXNzTmFtZT1cImhvdmVyOnVuZGVybGluZVwiPi90ZXN0LWFwaS5odG1sPC9hPjwvbGk+XG4gICAgICAgICAgPC91bD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwiU2ltcGxlQ29kZUVkaXRvciIsIkJ1ZyIsIlNpbXBsZVRlc3RQYWdlIiwiYXBpU3RhdHVzIiwic2V0QXBpU3RhdHVzIiwidGVzdEFQSSIsInJlc3BvbnNlIiwiZmV0Y2giLCJyZXN1bHQiLCJqc29uIiwibWVzc2FnZSIsImVycm9yIiwidGVzdEV4ZWN1dGVBUEkiLCJkaXYiLCJjbGFzc05hbWUiLCJoMSIsImgyIiwiYnV0dG9uIiwib25DbGljayIsInAiLCJpbml0aWFsQ29kZSIsIm9sIiwibGkiLCJ1bCIsImEiLCJocmVmIiwidGFyZ2V0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/simple-test/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Providers.tsx":
/*!**************************************!*\
  !*** ./src/components/Providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\Providers.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Qcm92aWRlcnMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUVpRDtBQU8xQyxTQUFTQyxVQUFVLEVBQUVDLFFBQVEsRUFBa0I7SUFDcEQscUJBQ0UsOERBQUNGLDREQUFlQTtrQkFDYkU7Ozs7OztBQUdQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vamF2YS1sZWFybmluZy1wbGF0Zm9ybS8uL3NyYy9jb21wb25lbnRzL1Byb3ZpZGVycy50c3g/YjAxOSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0J1xuaW1wb3J0IHsgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnXG5cbmludGVyZmFjZSBQcm92aWRlcnNQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFByb3ZpZGVycyh7IGNoaWxkcmVuIH06IFByb3ZpZGVyc1Byb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPFNlc3Npb25Qcm92aWRlcj5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L1Nlc3Npb25Qcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlNlc3Npb25Qcm92aWRlciIsIlByb3ZpZGVycyIsImNoaWxkcmVuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SimpleCodeEditor.tsx":
/*!*********************************************!*\
  !*** ./src/components/SimpleCodeEditor.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleCodeEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Play_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Play!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction SimpleCodeEditor({ initialCode = 'public class Test {\\n    public static void main(String[] args) {\\n        System.out.println(\"Hello, World!\");\\n    }\\n}', onRun }) {\n    const [code, setCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialCode);\n    const [output, setOutput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isRunning, setIsRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleRunCode = async ()=>{\n        console.log(\"\\uD83D\\uDE80 SimpleCodeEditor: Run button clicked\");\n        if (isRunning) {\n            console.log(\"⚠️ Already running, ignoring click\");\n            return;\n        }\n        setIsRunning(true);\n        setOutput(\"Executing...\");\n        try {\n            console.log(\"\\uD83D\\uDCDD Code to execute:\", code.substring(0, 100) + \"...\");\n            // If parent provides onRun, use it\n            if (onRun) {\n                console.log(\"\\uD83D\\uDD04 Using parent onRun function\");\n                onRun(code);\n                setIsRunning(false);\n                return;\n            }\n            console.log(\"\\uD83D\\uDCE1 Making API request to /api/execute\");\n            // Add absolute URL to ensure it works\n            const apiUrl = window.location.origin + \"/api/execute\";\n            console.log(\"\\uD83D\\uDCE1 API URL:\", apiUrl);\n            const requestBody = {\n                code: code,\n                language: \"java\"\n            };\n            console.log(\"\\uD83D\\uDCE1 Request body:\", requestBody);\n            const response = await fetch(apiUrl, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestBody)\n            });\n            console.log(\"\\uD83D\\uDCE1 Response received:\", response.status, response.statusText);\n            console.log(\"\\uD83D\\uDCE1 Response headers:\", [\n                ...response.headers.entries()\n            ]);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.log(\"❌ Response not OK:\", errorText);\n                throw new Error(`HTTP ${response.status}: ${errorText}`);\n            }\n            const result = await response.json();\n            console.log(\"✅ API Result:\", result);\n            if (result.success) {\n                setOutput(result.output || \"No output\");\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].success(\"Code executed successfully!\");\n            } else {\n                setOutput(`Error: ${result.error}`);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(\"Code execution failed!\");\n            }\n        } catch (error) {\n            console.error(\"\\uD83D\\uDCA5 Execution error:\", error);\n            const errorMessage = error instanceof Error ? error.message : \"Unknown error\";\n            setOutput(`Error: ${errorMessage}`);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(\"Failed to execute code!\");\n        } finally{\n            setIsRunning(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border border-gray-300 rounded-lg overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 text-white px-4 py-2 flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium\",\n                        children: \"Simple Java Editor\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\SimpleCodeEditor.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleRunCode,\n                        disabled: isRunning,\n                        className: `flex items-center space-x-1 px-3 py-1 rounded text-sm transition-colors ${isRunning ? \"bg-gray-600 cursor-not-allowed\" : \"bg-green-600 hover:bg-green-700\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\SimpleCodeEditor.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: isRunning ? \"Running...\" : \"Run\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\SimpleCodeEditor.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\SimpleCodeEditor.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\SimpleCodeEditor.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                value: code,\n                onChange: (e)=>setCode(e.target.value),\n                className: \"w-full h-64 p-4 font-mono text-sm border-none resize-none focus:outline-none\",\n                placeholder: \"Enter your Java code here...\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\SimpleCodeEditor.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-100 px-4 py-2 border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium text-gray-700\",\n                            children: \"Output\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\SimpleCodeEditor.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\SimpleCodeEditor.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 bg-gray-900 text-green-400 font-mono text-sm min-h-[100px]\",\n                        children: output || 'Click \"Run\" to execute your code'\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\SimpleCodeEditor.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\SimpleCodeEditor.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\components\\\\SimpleCodeEditor.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SimpleCodeEditor.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e05ba106f0bb\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vamF2YS1sZWFybmluZy1wbGF0Zm9ybS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/ZTAyMyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImUwNWJhMTA2ZjBiYlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_Providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Providers */ \"(rsc)/./src/components/Providers.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\nconst metadata = {\n    title: \"Java Learning Platform - Interactive Programming Education\",\n    description: \"Learn Java programming through interactive lessons, real-time code execution, quizzes, and earn certificates.\",\n    keywords: \"Java, programming, education, interactive, coding, tutorials, certificates\",\n    authors: [\n        {\n            name: \"Java Learning Platform\"\n        }\n    ]\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 4000,\n                            style: {\n                                background: \"#363636\",\n                                color: \"#fff\"\n                            },\n                            success: {\n                                duration: 3000,\n                                iconTheme: {\n                                    primary: \"#22c55e\",\n                                    secondary: \"#fff\"\n                                }\n                            },\n                            error: {\n                                duration: 5000,\n                                iconTheme: {\n                                    primary: \"#ef4444\",\n                                    secondary: \"#fff\"\n                                }\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/simple-test/page.tsx":
/*!**************************************!*\
  !*** ./src/app/simple-test/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Clients\DMI\Java-app\src\app\simple-test\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/Providers.tsx":
/*!**************************************!*\
  !*** ./src/components/Providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Clients\DMI\Java-app\src\components\Providers.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Clients\DMI\Java-app\src\components\Providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/react-hot-toast","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsimple-test%2Fpage&page=%2Fsimple-test%2Fpage&appPaths=%2Fsimple-test%2Fpage&pagePath=private-next-app-dir%2Fsimple-test%2Fpage.tsx&appDir=D%3A%5CClients%5CDMI%5CJava-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CClients%5CDMI%5CJava-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();