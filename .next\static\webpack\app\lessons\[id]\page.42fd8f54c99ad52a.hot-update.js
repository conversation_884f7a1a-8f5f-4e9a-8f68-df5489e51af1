"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/lessons/[id]/page",{

/***/ "(app-pages-browser)/./src/app/lessons/[id]/page.tsx":
/*!***************************************!*\
  !*** ./src/app/lessons/[id]/page.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LessonPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,CheckCircle,ChevronLeft,ChevronRight,Clock,Code,Star,Target!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_CodeEditor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/CodeEditor */ \"(app-pages-browser)/./src/components/CodeEditor.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction LessonPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [lesson, setLesson] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"theory\");\n    const [userCode, setUserCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isCompleted, setIsCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showHints, setShowHints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Function to get lesson data based on ID\n    const getLessonData = (id)=>{\n        const lessons = {\n            \"1\": {\n                id: \"1\",\n                title: \"Java Basics: Variables and Data Types\",\n                description: \"Learn about variables, primitive data types, and how to declare and use them in Java.\",\n                difficulty: \"Beginner\",\n                duration: \"30 min\",\n                content: {\n                    theory: \"\\n# Variables and Data Types in Java\\n\\n## What are Variables?\\nVariables are containers that store data values. In Java, every variable has a specific type that determines what kind of data it can hold.\\n\\n## Primitive Data Types\\nJava has 8 primitive data types:\\n\\n### Numeric Types:\\n- **byte**: 8-bit signed integer (-128 to 127)\\n- **short**: 16-bit signed integer (-32,768 to 32,767)\\n- **int**: 32-bit signed integer (-2^31 to 2^31-1)\\n- **long**: 64-bit signed integer (-2^63 to 2^63-1)\\n- **float**: 32-bit floating point\\n- **double**: 64-bit floating point\\n\\n### Other Types:\\n- **char**: 16-bit Unicode character\\n- **boolean**: true or false\\n\\n## Variable Declaration\\nTo declare a variable in Java:\\n```java\\ndataType variableName = value;\\n```\\n\\n## Naming Rules\\n- Must start with a letter, underscore, or dollar sign\\n- Cannot start with a number\\n- Case-sensitive\\n- Cannot use Java keywords\\n- Use camelCase convention\\n          \",\n                    example: 'public class VariableExample {\\n    public static void main(String[] args) {\\n        // Integer variables\\n        int age = 25;\\n        long population = 7800000000L;\\n\\n        // Floating point variables\\n        double price = 19.99;\\n        float temperature = 98.6f;\\n\\n        // Character and boolean\\n        char grade = \\'A\\';\\n        boolean isStudent = true;\\n\\n        // String (reference type)\\n        String name = \"John Doe\";\\n\\n        // Print all variables\\n        System.out.println(\"Name: \" + name);\\n        System.out.println(\"Age: \" + age);\\n        System.out.println(\"Grade: \" + grade);\\n        System.out.println(\"Is Student: \" + isStudent);\\n        System.out.println(\"Price: $\" + price);\\n    }\\n}',\n                    exercise: {\n                        description: \"Create a program that declares variables for a student's information and prints them out.\\n\\n**Requirements:**\\n1. Declare a String variable for the student's name\\n2. Declare an int variable for the student's age\\n3. Declare a double variable for the student's GPA\\n4. Declare a boolean variable for enrollment status\\n5. Print all the information in a formatted way\\n\\n**Expected format:**\\nStudent: [name]\\nAge: [age]\\nGPA: [gpa]\\nEnrolled: [status]\",\n                        starterCode: \"public class StudentInfo {\\n    public static void main(String[] args) {\\n        // TODO: Declare variables for student information\\n\\n\\n        // TODO: Print the student information\\n\\n    }\\n}\",\n                        expectedOutput: \"Student: Alice Johnson\\nAge: 20\\nGPA: 3.8\\nEnrolled: true\",\n                        hints: [\n                            \"Use String for the student's name\",\n                            \"Use int for age, double for GPA, and boolean for enrollment status\",\n                            \"Use System.out.println() to print each line\",\n                            \"You can concatenate strings using the + operator\"\n                        ]\n                    }\n                },\n                nextLessonId: \"2\",\n                prevLessonId: undefined\n            },\n            \"2\": {\n                id: \"2\",\n                title: \"Control Structures: If Statements and Loops\",\n                description: \"Master conditional statements and loops to control program flow.\",\n                difficulty: \"Beginner\",\n                duration: \"45 min\",\n                content: {\n                    theory: \"\\n# Control Structures in Java\\n\\nControl structures allow you to control the flow of execution in your Java programs. They include conditional statements and loops.\\n\\n## If Statements\\nIf statements execute code based on boolean conditions.\\n\\n### Basic If Statement:\\n```java\\nif (condition) {\\n    // code to execute if condition is true\\n}\\n```\\n\\n### If-Else Statement:\\n```java\\nif (condition) {\\n    // code if true\\n} else {\\n    // code if false\\n}\\n```\\n\\n### If-Else If-Else Chain:\\n```java\\nif (condition1) {\\n    // code if condition1 is true\\n} else if (condition2) {\\n    // code if condition2 is true\\n} else {\\n    // code if all conditions are false\\n}\\n```\\n\\n## For Loops\\nFor loops repeat code a specific number of times.\\n\\n### Basic For Loop:\\n```java\\nfor (initialization; condition; increment) {\\n    // code to repeat\\n}\\n```\\n\\n### Enhanced For Loop (for arrays):\\n```java\\nfor (dataType variable : array) {\\n    // code using variable\\n}\\n```\\n\\n## While Loops\\nWhile loops repeat code while a condition is true.\\n\\n### While Loop:\\n```java\\nwhile (condition) {\\n    // code to repeat\\n    // don't forget to update condition!\\n}\\n```\\n\\n### Do-While Loop:\\n```java\\ndo {\\n    // code to repeat at least once\\n} while (condition);\\n```\\n\\n## Switch Cases\\nSwitch statements provide an alternative to multiple if-else statements.\\n\\n```java\\nswitch (variable) {\\n    case value1:\\n        // code for value1\\n        break;\\n    case value2:\\n        // code for value2\\n        break;\\n    default:\\n        // default code\\n        break;\\n}\\n```\\n          \",\n                    example: 'public class ControlStructuresDemo {\\n    public static void main(String[] args) {\\n        // If Statements Example\\n        int score = 85;\\n        System.out.println(\"=== If Statements ===\");\\n\\n        if (score >= 90) {\\n            System.out.println(\"Grade: A - Excellent!\");\\n        } else if (score >= 80) {\\n            System.out.println(\"Grade: B - Good job!\");\\n        } else if (score >= 70) {\\n            System.out.println(\"Grade: C - Average\");\\n        } else {\\n            System.out.println(\"Grade: F - Need improvement\");\\n        }\\n\\n        // For Loop Example\\n        System.out.println(\"\\\\n=== For Loop ===\");\\n        System.out.println(\"Counting from 1 to 5:\");\\n        for (int i = 1; i <= 5; i++) {\\n            System.out.println(\"Count: \" + i);\\n        }\\n\\n        // While Loop Example\\n        System.out.println(\"\\\\n=== While Loop ===\");\\n        System.out.println(\"Countdown:\");\\n        int countdown = 3;\\n        while (countdown > 0) {\\n            System.out.println(countdown + \"...\");\\n            countdown--;\\n        }\\n        System.out.println(\"Blast off!\");\\n\\n        // Switch Case Example\\n        System.out.println(\"\\\\n=== Switch Case ===\");\\n        int dayOfWeek = 3;\\n        switch (dayOfWeek) {\\n            case 1:\\n                System.out.println(\"Monday\");\\n                break;\\n            case 2:\\n                System.out.println(\"Tuesday\");\\n                break;\\n            case 3:\\n                System.out.println(\"Wednesday\");\\n                break;\\n            case 4:\\n                System.out.println(\"Thursday\");\\n                break;\\n            case 5:\\n                System.out.println(\"Friday\");\\n                break;\\n            default:\\n                System.out.println(\"Weekend!\");\\n                break;\\n        }\\n    }\\n}',\n                    exercise: {\n                        description: \"Create a program that demonstrates all four control structure topics: If Statements, For Loops, While Loops, and Switch Cases.\\n\\n**Requirements:**\\n1. **If Statements**: Check if a student's grade (0-100) and print the letter grade (A, B, C, D, F)\\n2. **For Loops**: Print multiplication table for number 5 (5x1 to 5x10)\\n3. **While Loops**: Print numbers from 10 down to 1\\n4. **Switch Cases**: Given a month number (1-12), print the season\\n\\n**Expected output format:**\\nGrade: [letter grade]\\n=== Multiplication Table ===\\n5 x 1 = 5\\n5 x 2 = 10\\n...\\n=== Countdown ===\\n10\\n9\\n...\\n1\\nSeason: [season name]\",\n                        starterCode: 'public class ControlStructuresExercise {\\n    public static void main(String[] args) {\\n        // Test values\\n        int grade = 87;\\n        int tableNumber = 5;\\n        int month = 6;\\n\\n        // TODO: 1. If Statements - Check grade and print letter grade\\n        // A: 90-100, B: 80-89, C: 70-79, D: 60-69, F: below 60\\n\\n\\n        // TODO: 2. For Loop - Print multiplication table for tableNumber\\n        System.out.println(\"=== Multiplication Table ===\");\\n\\n\\n        // TODO: 3. While Loop - Print countdown from 10 to 1\\n        System.out.println(\"=== Countdown ===\");\\n\\n\\n        // TODO: 4. Switch Case - Print season based on month\\n        // Spring: 3,4,5  Summer: 6,7,8  Fall: 9,10,11  Winter: 12,1,2\\n\\n    }\\n}',\n                        expectedOutput: \"Grade: B\\n=== Multiplication Table ===\\n5 x 1 = 5\\n5 x 2 = 10\\n5 x 3 = 15\\n5 x 4 = 20\\n5 x 5 = 25\\n5 x 6 = 30\\n5 x 7 = 35\\n5 x 8 = 40\\n5 x 9 = 45\\n5 x 10 = 50\\n=== Countdown ===\\n10\\n9\\n8\\n7\\n6\\n5\\n4\\n3\\n2\\n1\\nSeason: Summer\",\n                        hints: [\n                            \"For if statements: Use >= for grade ranges (90, 80, 70, 60)\",\n                            \"For multiplication: Use for(int i = 1; i <= 10; i++)\",\n                            \"For countdown: Use while loop with int counter = 10; counter >= 1; counter--\",\n                            \"For switch: Use cases 3,4,5 for Spring; 6,7,8 for Summer, etc.\"\n                        ]\n                    }\n                },\n                nextLessonId: \"3\",\n                prevLessonId: \"1\"\n            },\n            \"3\": {\n                id: \"3\",\n                title: \"Methods and Functions\",\n                description: \"Learn how to create reusable code with methods and understand parameters.\",\n                difficulty: \"Beginner\",\n                duration: \"40 min\",\n                content: {\n                    theory: '\\n# Methods and Functions in Java\\n\\nMethods are blocks of code that perform specific tasks and can be reused throughout your program.\\n\\n## Method Declaration\\nThe basic syntax for declaring a method:\\n\\n```java\\naccessModifier returnType methodName(parameters) {\\n    // method body\\n    return value; // if returnType is not void\\n}\\n```\\n\\n### Components:\\n- **Access Modifier**: public, private, protected\\n- **Return Type**: void, int, String, etc.\\n- **Method Name**: follows camelCase convention\\n- **Parameters**: input values (optional)\\n\\n## Method Declaration Examples\\n\\n### Void Method (no return value):\\n```java\\npublic void printMessage() {\\n    System.out.println(\"Hello from method!\");\\n}\\n```\\n\\n### Method with Return Value:\\n```java\\npublic int addNumbers(int a, int b) {\\n    return a + b;\\n}\\n```\\n\\n## Parameters\\nParameters allow you to pass data into methods.\\n\\n### Single Parameter:\\n```java\\npublic void greetUser(String name) {\\n    System.out.println(\"Hello, \" + name + \"!\");\\n}\\n```\\n\\n### Multiple Parameters:\\n```java\\npublic double calculateArea(double length, double width) {\\n    return length * width;\\n}\\n```\\n\\n## Return Types\\nMethods can return different types of data.\\n\\n### Common Return Types:\\n- **void**: No return value\\n- **int**: Integer numbers\\n- **double**: Decimal numbers\\n- **String**: Text\\n- **boolean**: true/false\\n\\n## Method Overloading\\nYou can have multiple methods with the same name but different parameters.\\n\\n```java\\npublic int add(int a, int b) {\\n    return a + b;\\n}\\n\\npublic double add(double a, double b) {\\n    return a + b;\\n}\\n\\npublic int add(int a, int b, int c) {\\n    return a + b + c;\\n}\\n```\\n          ',\n                    example: 'public class MethodsDemo {\\n\\n    // Method Declaration - void method with no parameters\\n    public static void printWelcome() {\\n        System.out.println(\"Welcome to Java Methods!\");\\n        System.out.println(\"=========================\");\\n    }\\n\\n    // Method with Parameters - single parameter\\n    public static void greetUser(String userName) {\\n        System.out.println(\"Hello, \" + userName + \"! Nice to meet you.\");\\n    }\\n\\n    // Method with Return Type - returns an integer\\n    public static int addTwoNumbers(int num1, int num2) {\\n        int sum = num1 + num2;\\n        return sum;\\n    }\\n\\n    // Method with multiple parameters and return type\\n    public static double calculateRectangleArea(double length, double width) {\\n        return length * width;\\n    }\\n\\n    // Method Overloading - same name, different parameters\\n    public static int multiply(int a, int b) {\\n        return a * b;\\n    }\\n\\n    public static double multiply(double a, double b) {\\n        return a * b;\\n    }\\n\\n    public static int multiply(int a, int b, int c) {\\n        return a * b * c;\\n    }\\n\\n    public static void main(String[] args) {\\n        // Calling methods\\n        printWelcome();\\n\\n        greetUser(\"Alice\");\\n        greetUser(\"Bob\");\\n\\n        int result = addTwoNumbers(15, 25);\\n        System.out.println(\"15 + 25 = \" + result);\\n\\n        double area = calculateRectangleArea(5.5, 3.2);\\n        System.out.println(\"Rectangle area: \" + area);\\n\\n        // Method overloading examples\\n        System.out.println(\"2 * 3 = \" + multiply(2, 3));\\n        System.out.println(\"2.5 * 3.0 = \" + multiply(2.5, 3.0));\\n        System.out.println(\"2 * 3 * 4 = \" + multiply(2, 3, 4));\\n    }\\n}',\n                    exercise: {\n                        description: 'Create a program that demonstrates all four method topics: Method Declaration, Parameters, Return Types, and Method Overloading.\\n\\n**Requirements:**\\n1. **Method Declaration**: Create a void method that prints a welcome message\\n2. **Parameters**: Create a method that takes a name and age, prints a personalized message\\n3. **Return Types**: Create a method that calculates and returns the square of a number\\n4. **Method Overloading**: Create three versions of a \"calculate\" method:\\n   - One that adds two integers\\n   - One that adds two doubles\\n   - One that adds three integers\\n\\n**Expected output format:**\\nWelcome to the Method Exercise!\\nHello John, you are 25 years old.\\nThe square of 7 is: 49\\nSum of 5 and 3: 8\\nSum of 2.5 and 3.7: 6.2\\nSum of 1, 2, and 3: 6',\n                        starterCode: 'public class MethodsExercise {\\n\\n    // TODO: 1. Method Declaration - Create a void method called printWelcome()\\n\\n\\n    // TODO: 2. Parameters - Create a method called printPersonInfo(String name, int age)\\n\\n\\n    // TODO: 3. Return Types - Create a method called calculateSquare(int number) that returns int\\n\\n\\n    // TODO: 4. Method Overloading - Create three calculate methods:\\n    // - calculate(int a, int b) returns int\\n    // - calculate(double a, double b) returns double\\n    // - calculate(int a, int b, int c) returns int\\n\\n\\n\\n\\n    public static void main(String[] args) {\\n        // TODO: Call all your methods here with these values:\\n        // printWelcome()\\n        // printPersonInfo(\"John\", 25)\\n        // calculateSquare(7)\\n        // calculate(5, 3)\\n        // calculate(2.5, 3.7)\\n        // calculate(1, 2, 3)\\n\\n    }\\n}',\n                        expectedOutput: \"Welcome to the Method Exercise!\\nHello John, you are 25 years old.\\nThe square of 7 is: 49\\nSum of 5 and 3: 8\\nSum of 2.5 and 3.7: 6.2\\nSum of 1, 2, and 3: 6\",\n                        hints: [\n                            \"Use 'public static void' for methods that don't return values\",\n                            \"Use 'public static int' for methods that return integers\",\n                            \"Method overloading means same name, different parameter types or counts\",\n                            \"Don't forget to call all methods in main() and print the results\"\n                        ]\n                    }\n                },\n                nextLessonId: \"4\",\n                prevLessonId: \"2\"\n            },\n            \"4\": {\n                id: \"4\",\n                title: \"Object-Oriented Programming: Classes and Objects\",\n                description: \"Introduction to OOP concepts with classes, objects, and encapsulation.\",\n                difficulty: \"Intermediate\",\n                duration: \"60 min\",\n                content: {\n                    theory: '\\n# Object-Oriented Programming in Java\\n\\nObject-Oriented Programming (OOP) is a programming paradigm based on the concept of \"objects\" which contain data and code.\\n\\n## Classes\\nA class is a blueprint or template for creating objects. It defines the properties and behaviors that objects of that type will have.\\n\\n### Class Declaration:\\n```java\\npublic class ClassName {\\n    // fields (attributes)\\n    // constructors\\n    // methods\\n}\\n```\\n\\n### Example Class:\\n```java\\npublic class Car {\\n    // Fields (attributes)\\n    private String brand;\\n    private String model;\\n    private int year;\\n\\n    // Constructor\\n    public Car(String brand, String model, int year) {\\n        this.brand = brand;\\n        this.model = model;\\n        this.year = year;\\n    }\\n\\n    // Methods\\n    public void startEngine() {\\n        System.out.println(\"Engine started!\");\\n    }\\n}\\n```\\n\\n## Objects\\nAn object is an instance of a class. You create objects using the `new` keyword.\\n\\n```java\\nCar myCar = new Car(\"Toyota\", \"Camry\", 2023);\\n```\\n\\n## Constructors\\nConstructors are special methods used to initialize objects when they are created.\\n\\n### Default Constructor:\\n```java\\npublic Car() {\\n    // default values\\n}\\n```\\n\\n### Parameterized Constructor:\\n```java\\npublic Car(String brand, String model, int year) {\\n    this.brand = brand;\\n    this.model = model;\\n    this.year = year;\\n}\\n```\\n\\n## Encapsulation\\nEncapsulation is the practice of keeping fields private and providing public methods to access them.\\n\\n### Private Fields with Public Methods:\\n```java\\nprivate String name;\\n\\npublic String getName() {\\n    return name;\\n}\\n\\npublic void setName(String name) {\\n    this.name = name;\\n}\\n```\\n          ',\n                    example: '// Student class demonstrating OOP concepts\\nclass Student {\\n    // Private fields (Encapsulation)\\n    private String name;\\n    private int age;\\n    private String studentId;\\n    private double gpa;\\n\\n    // Default Constructor\\n    public Student() {\\n        this.name = \"Unknown\";\\n        this.age = 0;\\n        this.studentId = \"000000\";\\n        this.gpa = 0.0;\\n    }\\n\\n    // Parameterized Constructor\\n    public Student(String name, int age, String studentId, double gpa) {\\n        this.name = name;\\n        this.age = age;\\n        this.studentId = studentId;\\n        this.gpa = gpa;\\n    }\\n\\n    // Getter methods (Encapsulation)\\n    public String getName() {\\n        return name;\\n    }\\n\\n    public int getAge() {\\n        return age;\\n    }\\n\\n    public String getStudentId() {\\n        return studentId;\\n    }\\n\\n    public double getGpa() {\\n        return gpa;\\n    }\\n\\n    // Setter methods (Encapsulation)\\n    public void setName(String name) {\\n        this.name = name;\\n    }\\n\\n    public void setAge(int age) {\\n        if (age > 0) {\\n            this.age = age;\\n        }\\n    }\\n\\n    public void setGpa(double gpa) {\\n        if (gpa >= 0.0 && gpa <= 4.0) {\\n            this.gpa = gpa;\\n        }\\n    }\\n\\n    // Method to display student information\\n    public void displayInfo() {\\n        System.out.println(\"Student Information:\");\\n        System.out.println(\"Name: \" + name);\\n        System.out.println(\"Age: \" + age);\\n        System.out.println(\"Student ID: \" + studentId);\\n        System.out.println(\"GPA: \" + gpa);\\n    }\\n\\n    // Method to check if student is on honor roll\\n    public boolean isHonorRoll() {\\n        return gpa >= 3.5;\\n    }\\n}\\n\\npublic class OOPDemo {\\n    public static void main(String[] args) {\\n        // Creating objects using different constructors\\n        Student student1 = new Student();\\n        Student student2 = new Student(\"Alice Johnson\", 20, \"STU001\", 3.8);\\n\\n        System.out.println(\"=== Student 1 (Default Constructor) ===\");\\n        student1.displayInfo();\\n\\n        System.out.println(\"\\\\n=== Student 2 (Parameterized Constructor) ===\");\\n        student2.displayInfo();\\n\\n        // Using setter methods to modify student1\\n        student1.setName(\"Bob Smith\");\\n        student1.setAge(19);\\n        student1.setGpa(3.2);\\n\\n        System.out.println(\"\\\\n=== Student 1 (After modifications) ===\");\\n        student1.displayInfo();\\n\\n        // Using methods\\n        System.out.println(\"\\\\n=== Honor Roll Status ===\");\\n        System.out.println(student1.getName() + \" honor roll: \" + student1.isHonorRoll());\\n        System.out.println(student2.getName() + \" honor roll: \" + student2.isHonorRoll());\\n    }\\n}',\n                    exercise: {\n                        description: \"Create a Book class that demonstrates all four OOP topics: Classes, Objects, Constructors, and Encapsulation.\\n\\n**Requirements:**\\n1. **Classes**: Create a Book class with private fields: title, author, pages, price\\n2. **Objects**: Create two Book objects in main method\\n3. **Constructors**: Implement both default and parameterized constructors\\n4. **Encapsulation**: Create getter and setter methods for all fields, plus a displayInfo() method\\n\\n**Expected output format:**\\n=== Book 1 (Default Constructor) ===\\nTitle: Unknown\\nAuthor: Unknown\\nPages: 0\\nPrice: $0.0\\n\\n=== Book 2 (Parameterized Constructor) ===\\nTitle: Java Programming\\nAuthor: John Doe\\nPages: 500\\nPrice: $49.99\\n\\n=== Book 1 (After modifications) ===\\nTitle: Python Basics\\nAuthor: Jane Smith\\nPages: 300\\nPrice: $29.99\",\n                        starterCode: '// TODO: Create Book class here\\nclass Book {\\n    // TODO: 1. Classes - Add private fields: title, author, pages, price\\n\\n\\n    // TODO: 2. Constructors - Create default constructor\\n\\n\\n    // TODO: 2. Constructors - Create parameterized constructor\\n\\n\\n    // TODO: 3. Encapsulation - Create getter methods\\n\\n\\n\\n\\n\\n    // TODO: 3. Encapsulation - Create setter methods\\n\\n\\n\\n\\n\\n    // TODO: Create displayInfo() method\\n\\n}\\n\\npublic class BookDemo {\\n    public static void main(String[] args) {\\n        // TODO: 4. Objects - Create two Book objects\\n        // book1 using default constructor\\n        // book2 using parameterized constructor with values:\\n        // \"Java Programming\", \"John Doe\", 500, 49.99\\n\\n\\n        // TODO: Display both books\\n\\n\\n        // TODO: Modify book1 using setters:\\n        // title: \"Python Basics\", author: \"Jane Smith\", pages: 300, price: 29.99\\n\\n\\n        // TODO: Display book1 again\\n\\n    }\\n}',\n                        expectedOutput: \"=== Book 1 (Default Constructor) ===\\nTitle: Unknown\\nAuthor: Unknown\\nPages: 0\\nPrice: $0.0\\n\\n=== Book 2 (Parameterized Constructor) ===\\nTitle: Java Programming\\nAuthor: John Doe\\nPages: 500\\nPrice: $49.99\\n\\n=== Book 1 (After modifications) ===\\nTitle: Python Basics\\nAuthor: Jane Smith\\nPages: 300\\nPrice: $29.99\",\n                        hints: [\n                            \"Use private fields and public methods for encapsulation\",\n                            \"Default constructor should set default values like 'Unknown' and 0\",\n                            \"Parameterized constructor should accept all four parameters\",\n                            \"Getter methods return field values, setter methods update them\"\n                        ]\n                    }\n                },\n                nextLessonId: \"5\",\n                prevLessonId: \"3\"\n            }\n        };\n        return lessons[id] || lessons[\"1\"] // Default to lesson 1 if not found\n        ;\n    };\n    const mockLesson = getLessonData(params.id);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simulate loading lesson data\n        setLesson(mockLesson);\n        setUserCode(mockLesson.content.exercise.starterCode);\n        // Calculate progress based on current tab\n        const tabProgress = {\n            theory: 33,\n            example: 66,\n            exercise: 100\n        };\n        setProgress(tabProgress[currentTab]);\n    }, [\n        currentTab\n    ]);\n    const handleCodeChange = (code)=>{\n        setUserCode(code);\n    };\n    const handleRunCode = async (code)=>{\n        // Simulate code execution\n        try {\n            // In a real app, this would send the code to a backend service\n            const mockOutput = \"Student: Alice Johnson\\nAge: 20\\nGPA: 3.8\\nEnrolled: true\";\n            // Check if the output matches expected output\n            if (lesson && mockOutput.trim() === lesson.content.exercise.expectedOutput.trim()) {\n                setIsCompleted(true);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(\"Congratulations! Exercise completed successfully!\");\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Output doesn't match expected result. Keep trying!\");\n            }\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Error executing code\");\n        }\n    };\n    const handleNextLesson = ()=>{\n        if (lesson === null || lesson === void 0 ? void 0 : lesson.nextLessonId) {\n            router.push(\"/lessons/\".concat(lesson.nextLessonId));\n        }\n    };\n    const handlePrevLesson = ()=>{\n        if (lesson === null || lesson === void 0 ? void 0 : lesson.prevLessonId) {\n            router.push(\"/lessons/\".concat(lesson.prevLessonId));\n        }\n    };\n    if (!lesson) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                lineNumber: 951,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n            lineNumber: 950,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/dashboard\",\n                                        className: \"flex items-center text-gray-600 hover:text-primary-600 transition-colors mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-5 h-5 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 964,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Back to Dashboard\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 963,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-8 h-8 text-primary-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 967,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-xl font-bold text-gray-900\",\n                                        children: \"JavaLearn\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 968,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 962,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Progress: \",\n                                            progress,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 971,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-32 bg-gray-200 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-primary-600 h-2 rounded-full transition-all duration-300\",\n                                            style: {\n                                                width: \"\".concat(progress, \"%\")\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 975,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 974,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 970,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                        lineNumber: 961,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                    lineNumber: 960,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                lineNumber: 959,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        className: \"mb-8\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                            children: lesson.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 995,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: lesson.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 996,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 py-1 rounded-full text-xs \".concat(lesson.difficulty === \"Beginner\" ? \"bg-green-100 text-green-800\" : lesson.difficulty === \"Intermediate\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                                                    children: lesson.difficulty\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 998,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1006,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        lesson.duration\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1005,\n                                                    columnNumber: 17\n                                                }, this),\n                                                isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex items-center text-green-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1011,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Completed\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1010,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 997,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 994,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 993,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"-mb-px flex space-x-8\",\n                                    children: [\n                                        {\n                                            id: \"theory\",\n                                            label: \"Theory\",\n                                            icon: _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                                        },\n                                        {\n                                            id: \"example\",\n                                            label: \"Example\",\n                                            icon: _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                                        },\n                                        {\n                                            id: \"exercise\",\n                                            label: \"Exercise\",\n                                            icon: _barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n                                        }\n                                    ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setCurrentTab(tab.id),\n                                            className: \"flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors \".concat(currentTab === tab.id ? \"border-primary-500 text-primary-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1036,\n                                                    columnNumber: 19\n                                                }, this),\n                                                tab.label\n                                            ]\n                                        }, tab.id, true, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1027,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1021,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1020,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                        lineNumber: 987,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            duration: 0.4\n                        },\n                        children: [\n                            currentTab === \"theory\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card p-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"prose max-w-none\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        dangerouslySetInnerHTML: {\n                                            __html: lesson.content.theory.replace(/\\n/g, \"<br>\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1054,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1053,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1052,\n                                columnNumber: 13\n                            }, this),\n                            currentTab === \"example\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                            children: \"Example Code\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1062,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CodeEditor__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            initialCode: lesson.content.example,\n                                            readOnly: true,\n                                            height: \"500px\",\n                                            showOutput: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1063,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1061,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1060,\n                                columnNumber: 13\n                            }, this),\n                            currentTab === \"exercise\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900\",\n                                                        children: \"Practice Exercise\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1077,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowHints(!showHints),\n                                                        className: \"btn-secondary text-sm\",\n                                                        children: showHints ? \"Hide Hints\" : \"Show Hints\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1078,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1076,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"prose max-w-none mb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    dangerouslySetInnerHTML: {\n                                                        __html: lesson.content.exercise.description.replace(/\\n/g, \"<br>\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1086,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1085,\n                                                columnNumber: 17\n                                            }, this),\n                                            showHints && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-yellow-800 mb-2\",\n                                                        children: \"Hints:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1091,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"text-yellow-700 text-sm space-y-1\",\n                                                        children: lesson.content.exercise.hints.map((hint, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    \"• \",\n                                                                    hint\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 1094,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1092,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1090,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1075,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CodeEditor__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        initialCode: lesson.content.exercise.starterCode,\n                                        onCodeChange: handleCodeChange,\n                                        onRun: handleRunCode,\n                                        height: \"400px\",\n                                        showOutput: true,\n                                        expectedOutput: lesson.content.exercise.expectedOutput\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1101,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1074,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, currentTab, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                        lineNumber: 1045,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mt-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handlePrevLesson,\n                                disabled: !lesson.prevLessonId,\n                                className: \"flex items-center btn-secondary disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-4 h-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1120,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Previous Lesson\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1115,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4\",\n                                children: [\n                                    currentTab !== \"exercise\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            const nextTab = currentTab === \"theory\" ? \"example\" : \"exercise\";\n                                            setCurrentTab(nextTab);\n                                        },\n                                        className: \"btn-primary\",\n                                        children: [\n                                            \"Continue\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 ml-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1134,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1126,\n                                        columnNumber: 15\n                                    }, this),\n                                    currentTab === \"exercise\" && isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNextLesson,\n                                        disabled: !lesson.nextLessonId,\n                                        className: \"flex items-center btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: [\n                                            \"Next Lesson\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_CheckCircle_ChevronLeft_ChevronRight_Clock_Code_Star_Target_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 ml-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1145,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1139,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1124,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                        lineNumber: 1114,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n                lineNumber: 985,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Clients\\\\DMI\\\\Java-app\\\\src\\\\app\\\\lessons\\\\[id]\\\\page.tsx\",\n        lineNumber: 957,\n        columnNumber: 5\n    }, this);\n}\n_s(LessonPage, \"dBUNDWH67aICokwnd2zYUw3Quos=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = LessonPage;\nvar _c;\n$RefreshReg$(_c, \"LessonPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/lessons/[id]/page.tsx\n"));

/***/ })

});