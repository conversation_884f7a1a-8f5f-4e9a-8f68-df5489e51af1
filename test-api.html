<!DOCTYPE html>
<html>
<head>
    <title>Test Java Code Execution API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        textarea { width: 100%; height: 200px; font-family: monospace; }
        button { padding: 10px 20px; margin: 10px 0; background: #007bff; color: white; border: none; cursor: pointer; }
        button:hover { background: #0056b3; }
        .output { background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; margin: 10px 0; white-space: pre-wrap; font-family: monospace; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Java Code Execution API Test</h1>
        
        <h3>Test Code:</h3>
        <textarea id="codeInput">public class HelloWorld {
    public static void main(String[] args) {
        System.out.println("Hello, World!");
        System.out.println("Testing Java execution");
        
        int a = 10;
        int b = 20;
        System.out.println("Sum: " + (a + b));
    }
}</textarea>
        
        <br>
        <button onclick="testAPI()">🚀 Test Code Execution</button>
        <button onclick="testSimpleAPI()">🔧 Test API Endpoint</button>
        
        <h3>Result:</h3>
        <div id="output" class="output">Click "Test Code Execution" to run the test...</div>
        
        <h3>Debug Info:</h3>
        <div id="debug" class="output">Debug information will appear here...</div>
    </div>

    <script>
        async function testAPI() {
            const output = document.getElementById('output');
            const debug = document.getElementById('debug');
            const code = document.getElementById('codeInput').value;
            
            output.textContent = 'Testing...';
            debug.textContent = 'Starting API test...';
            
            try {
                debug.textContent += '\n1. Sending request to /api/execute...';
                
                const response = await fetch('/api/execute', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        code: code,
                        language: 'java'
                    })
                });
                
                debug.textContent += `\n2. Response status: ${response.status}`;
                debug.textContent += `\n3. Response headers: ${JSON.stringify([...response.headers.entries()])}`;
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const result = await response.json();
                debug.textContent += `\n4. Response data: ${JSON.stringify(result, null, 2)}`;
                
                if (result.success) {
                    output.className = 'output success';
                    output.textContent = `✅ SUCCESS!\n\nOutput:\n${result.output}`;
                } else {
                    output.className = 'output error';
                    output.textContent = `❌ ERROR!\n\n${result.error}`;
                }
                
            } catch (error) {
                debug.textContent += `\n❌ ERROR: ${error.message}`;
                output.className = 'output error';
                output.textContent = `❌ FAILED!\n\nError: ${error.message}`;
            }
        }
        
        async function testSimpleAPI() {
            const debug = document.getElementById('debug');
            
            try {
                debug.textContent = 'Testing simple API endpoint...';
                
                const response = await fetch('/api/test');
                const result = await response.json();
                
                debug.textContent += `\n✅ API Test Result: ${JSON.stringify(result, null, 2)}`;
                
            } catch (error) {
                debug.textContent += `\n❌ API Test Error: ${error.message}`;
            }
        }
        
        // Auto-test on page load
        window.onload = function() {
            testSimpleAPI();
        };
    </script>
</body>
</html>
