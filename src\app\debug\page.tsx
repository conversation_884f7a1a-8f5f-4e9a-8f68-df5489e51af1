'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Play, Bug, CheckCircle, XCircle, Info } from 'lucide-react'
import CodeEditor from '@/components/CodeEditor'
import toast from 'react-hot-toast'

export default function DebugPage() {
  const [testResults, setTestResults] = useState<any[]>([])
  const [isRunning, setIsRunning] = useState(false)

  const testCases = [
    {
      name: 'Simple Hello World',
      code: `public class HelloWorld {
    public static void main(String[] args) {
        System.out.println("Hello, World!");
    }
}`,
      expectedOutput: 'Hello, World!'
    },
    {
      name: 'Variable Declaration',
      code: `public class Variables {
    public static void main(String[] args) {
        String name = "Alice";
        int age = 25;
        System.out.println("Name: " + name);
        System.out.println("Age: " + age);
    }
}`,
      expectedOutput: 'Name: Alice\nAge: 25'
    },
    {
      name: 'Simple Math',
      code: `public class Math {
    public static void main(String[] args) {
        int a = 10;
        int b = 5;
        System.out.println("Sum: " + (a + b));
        System.out.println("Product: " + (a * b));
    }
}`,
      expectedOutput: 'Sum: 15\nProduct: 50'
    }
  ]

  const runAllTests = async () => {
    setIsRunning(true)
    setTestResults([])
    const results = []

    for (const testCase of testCases) {
      try {
        console.log(`🧪 Running test: ${testCase.name}`)
        
        const response = await fetch('/api/execute', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            code: testCase.code,
            language: 'java',
          }),
        })

        const result = await response.json()
        
        results.push({
          name: testCase.name,
          success: result.success,
          output: result.output,
          error: result.error,
          expected: testCase.expectedOutput,
          passed: result.success && result.output?.includes(testCase.expectedOutput.split('\n')[0])
        })

      } catch (error) {
        results.push({
          name: testCase.name,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          expected: testCase.expectedOutput,
          passed: false
        })
      }
    }

    setTestResults(results)
    setIsRunning(false)
    
    const passedTests = results.filter(r => r.passed).length
    if (passedTests === results.length) {
      toast.success(`All ${passedTests} tests passed!`)
    } else {
      toast.error(`${passedTests}/${results.length} tests passed`)
    }
  }

  const testAPI = async () => {
    try {
      const response = await fetch('/api/test')
      const result = await response.json()
      toast.success('API Test: ' + result.message)
    } catch (error) {
      toast.error('API Test failed: ' + (error instanceof Error ? error.message : 'Unknown error'))
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center mb-8">
            <Bug className="w-8 h-8 text-red-600 mr-3" />
            <h1 className="text-3xl font-bold text-gray-900">Debug & Test Console</h1>
          </div>

          {/* Quick Tests */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="card p-6">
              <h2 className="text-xl font-semibold mb-4">API Tests</h2>
              <div className="space-y-3">
                <button
                  onClick={testAPI}
                  className="btn-secondary w-full"
                >
                  Test API Endpoint
                </button>
                <button
                  onClick={runAllTests}
                  disabled={isRunning}
                  className="btn-primary w-full"
                >
                  {isRunning ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Running Tests...
                    </div>
                  ) : (
                    <>
                      <Play className="w-4 h-4 mr-2" />
                      Run All Code Tests
                    </>
                  )}
                </button>
              </div>
            </div>

            <div className="card p-6">
              <h2 className="text-xl font-semibold mb-4">System Info</h2>
              <div className="space-y-2 text-sm">
                <div>Platform: {typeof window !== 'undefined' ? navigator.platform : 'Server'}</div>
                <div>User Agent: {typeof window !== 'undefined' ? navigator.userAgent.substring(0, 50) + '...' : 'N/A'}</div>
                <div>Timestamp: {new Date().toISOString()}</div>
              </div>
            </div>
          </div>

          {/* Test Results */}
          {testResults.length > 0 && (
            <div className="card p-6 mb-8">
              <h2 className="text-xl font-semibold mb-4">Test Results</h2>
              <div className="space-y-4">
                {testResults.map((result, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium">{result.name}</h3>
                      {result.passed ? (
                        <CheckCircle className="w-5 h-5 text-green-600" />
                      ) : (
                        <XCircle className="w-5 h-5 text-red-600" />
                      )}
                    </div>
                    
                    {result.success ? (
                      <div>
                        <div className="text-sm text-gray-600 mb-1">Output:</div>
                        <pre className="bg-gray-900 text-green-400 p-2 rounded text-sm overflow-x-auto">
                          {result.output}
                        </pre>
                      </div>
                    ) : (
                      <div>
                        <div className="text-sm text-red-600 mb-1">Error:</div>
                        <pre className="bg-red-50 text-red-800 p-2 rounded text-sm overflow-x-auto">
                          {result.error}
                        </pre>
                      </div>
                    )}
                    
                    <div className="mt-2">
                      <div className="text-sm text-gray-600 mb-1">Expected:</div>
                      <pre className="bg-blue-50 text-blue-800 p-2 rounded text-sm">
                        {result.expected}
                      </pre>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Interactive Code Editor */}
          <div className="card p-6">
            <h2 className="text-xl font-semibold mb-4">Interactive Code Testing</h2>
            <CodeEditor
              initialCode={`public class Test {
    public static void main(String[] args) {
        System.out.println("Hello from debug console!");
        System.out.println("Current time: " + System.currentTimeMillis());
    }
}`}
              height="400px"
              showOutput={true}
            />
          </div>

          {/* Debug Info */}
          <div className="card p-6 mt-6">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <Info className="w-5 h-5 mr-2" />
              Debug Information
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <h3 className="font-medium mb-2">Frontend Status</h3>
                <ul className="space-y-1 text-gray-600">
                  <li>✅ React components loaded</li>
                  <li>✅ Monaco Editor available</li>
                  <li>✅ API endpoints accessible</li>
                  <li>✅ Toast notifications working</li>
                </ul>
              </div>
              <div>
                <h3 className="font-medium mb-2">Backend Status</h3>
                <ul className="space-y-1 text-gray-600">
                  <li>🔄 Java runtime: Testing...</li>
                  <li>🔄 File system: Testing...</li>
                  <li>🔄 Code execution: Testing...</li>
                  <li>🔄 Error handling: Testing...</li>
                </ul>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}
