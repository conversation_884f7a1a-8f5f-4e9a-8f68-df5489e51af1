'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { 
  ChevronLeft, 
  ChevronRight, 
  BookOpen, 
  CheckCircle, 
  Play, 
  Target,
  Clock,
  Star,
  Code,
  ArrowLeft
} from 'lucide-react'
import Link from 'next/link'
import CodeEditor from '@/components/CodeEditor'
import toast from 'react-hot-toast'

interface Lesson {
  id: string
  title: string
  description: string
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced'
  duration: string
  content: {
    theory: string
    example: string
    exercise: {
      description: string
      starterCode: string
      expectedOutput: string
      hints: string[]
    }
  }
  nextLessonId?: string
  prevLessonId?: string
}

export default function LessonPage() {
  const params = useParams()
  const router = useRouter()
  const [lesson, setLesson] = useState<Lesson | null>(null)
  const [currentTab, setCurrentTab] = useState<'theory' | 'example' | 'exercise'>('theory')
  const [userCode, setUserCode] = useState('')
  const [isCompleted, setIsCompleted] = useState(false)
  const [showHints, setShowHints] = useState(false)
  const [progress, setProgress] = useState(0)

  // Function to get lesson data based on ID
  const getLessonData = (id: string): Lesson => {
    const lessons: { [key: string]: Lesson } = {
      '1': {
        id: '1',
        title: 'Java Basics: Variables and Data Types',
        description: 'Learn about variables, primitive data types, and how to declare and use them in Java.',
        difficulty: 'Beginner',
        duration: '30 min',
        content: {
          theory: `
# Variables and Data Types in Java

## What are Variables?
Variables are containers that store data values. In Java, every variable has a specific type that determines what kind of data it can hold.

## Primitive Data Types
Java has 8 primitive data types:

### Numeric Types:
- **byte**: 8-bit signed integer (-128 to 127)
- **short**: 16-bit signed integer (-32,768 to 32,767)
- **int**: 32-bit signed integer (-2^31 to 2^31-1)
- **long**: 64-bit signed integer (-2^63 to 2^63-1)
- **float**: 32-bit floating point
- **double**: 64-bit floating point

### Other Types:
- **char**: 16-bit Unicode character
- **boolean**: true or false

## Variable Declaration
To declare a variable in Java:
\`\`\`java
dataType variableName = value;
\`\`\`

## Naming Rules
- Must start with a letter, underscore, or dollar sign
- Cannot start with a number
- Case-sensitive
- Cannot use Java keywords
- Use camelCase convention
          `,
          example: `public class VariableExample {
    public static void main(String[] args) {
        // Integer variables
        int age = 25;
        long population = 7800000000L;

        // Floating point variables
        double price = 19.99;
        float temperature = 98.6f;

        // Character and boolean
        char grade = 'A';
        boolean isStudent = true;

        // String (reference type)
        String name = "John Doe";

        // Print all variables
        System.out.println("Name: " + name);
        System.out.println("Age: " + age);
        System.out.println("Grade: " + grade);
        System.out.println("Is Student: " + isStudent);
        System.out.println("Price: $" + price);
    }
}`,
          exercise: {
            description: `Create a program that declares variables for a student's information and prints them out.

**Requirements:**
1. Declare a String variable for the student's name
2. Declare an int variable for the student's age
3. Declare a double variable for the student's GPA
4. Declare a boolean variable for enrollment status
5. Print all the information in a formatted way

**Expected format:**
Student: [name]
Age: [age]
GPA: [gpa]
Enrolled: [status]`,
            starterCode: `public class StudentInfo {
    public static void main(String[] args) {
        // TODO: Declare variables for student information


        // TODO: Print the student information

    }
}`,
            expectedOutput: `Student: Alice Johnson
Age: 20
GPA: 3.8
Enrolled: true`,
            hints: [
              "Use String for the student's name",
              "Use int for age, double for GPA, and boolean for enrollment status",
              "Use System.out.println() to print each line",
              "You can concatenate strings using the + operator"
            ]
          }
        },
        nextLessonId: '2',
        prevLessonId: undefined
      },
      '2': {
        id: '2',
        title: 'Control Structures: If Statements and Loops',
        description: 'Master conditional statements and loops to control program flow.',
        difficulty: 'Beginner',
        duration: '45 min',
        content: {
          theory: `
# Control Structures in Java

## If Statements
If statements allow you to execute code conditionally based on boolean expressions.

### Basic If Statement:
\`\`\`java
if (condition) {
    // code to execute if condition is true
}
\`\`\`

### If-Else Statement:
\`\`\`java
if (condition) {
    // code if true
} else {
    // code if false
}
\`\`\`

### If-Else If-Else:
\`\`\`java
if (condition1) {
    // code if condition1 is true
} else if (condition2) {
    // code if condition2 is true
} else {
    // code if all conditions are false
}
\`\`\`

## Loops
Loops allow you to repeat code multiple times.

### For Loop:
\`\`\`java
for (initialization; condition; increment) {
    // code to repeat
}
\`\`\`

### While Loop:
\`\`\`java
while (condition) {
    // code to repeat
}
\`\`\`

### Do-While Loop:
\`\`\`java
do {
    // code to repeat
} while (condition);
\`\`\`
          `,
          example: `public class ControlStructures {
    public static void main(String[] args) {
        int score = 85;

        // If-else statement
        if (score >= 90) {
            System.out.println("Grade: A");
        } else if (score >= 80) {
            System.out.println("Grade: B");
        } else if (score >= 70) {
            System.out.println("Grade: C");
        } else {
            System.out.println("Grade: F");
        }

        // For loop
        System.out.println("Counting from 1 to 5:");
        for (int i = 1; i <= 5; i++) {
            System.out.println("Count: " + i);
        }

        // While loop
        System.out.println("Countdown:");
        int countdown = 3;
        while (countdown > 0) {
            System.out.println(countdown);
            countdown--;
        }
        System.out.println("Blast off!");
    }
}`,
          exercise: {
            description: `Create a program that checks if a number is positive, negative, or zero, and then prints all even numbers from 1 to 10.

**Requirements:**
1. Declare an int variable with any value
2. Use if-else statements to check if the number is positive, negative, or zero
3. Print an appropriate message for each case
4. Use a for loop to print all even numbers from 1 to 10

**Expected output format:**
The number [number] is [positive/negative/zero]
Even numbers from 1 to 10:
2
4
6
8
10`,
            starterCode: `public class NumberChecker {
    public static void main(String[] args) {
        // TODO: Declare a number variable

        // TODO: Check if number is positive, negative, or zero


        // TODO: Print even numbers from 1 to 10

    }
}`,
            expectedOutput: `The number 7 is positive
Even numbers from 1 to 10:
2
4
6
8
10`,
            hints: [
              "Use if (number > 0), else if (number < 0), else for zero check",
              "For even numbers, use i % 2 == 0 to check if a number is even",
              "Use a for loop from 1 to 10 and check each number",
              "Remember to use System.out.println() for each output line"
            ]
          }
        },
        nextLessonId: '3',
        prevLessonId: '1'
      }
    }

    return lessons[id] || lessons['1'] // Default to lesson 1 if not found
  }

  const mockLesson = getLessonData(params.id as string)

  useEffect(() => {
    // Simulate loading lesson data
    setLesson(mockLesson)
    setUserCode(mockLesson.content.exercise.starterCode)
    
    // Calculate progress based on current tab
    const tabProgress = {
      theory: 33,
      example: 66,
      exercise: 100
    }
    setProgress(tabProgress[currentTab])
  }, [currentTab])

  const handleCodeChange = (code: string) => {
    setUserCode(code)
  }

  const handleRunCode = async (code: string) => {
    // Simulate code execution
    try {
      // In a real app, this would send the code to a backend service
      const mockOutput = `Student: Alice Johnson
Age: 20
GPA: 3.8
Enrolled: true`
      
      // Check if the output matches expected output
      if (lesson && mockOutput.trim() === lesson.content.exercise.expectedOutput.trim()) {
        setIsCompleted(true)
        toast.success('Congratulations! Exercise completed successfully!')
      } else {
        toast.error('Output doesn\'t match expected result. Keep trying!')
      }
    } catch (error) {
      toast.error('Error executing code')
    }
  }

  const handleNextLesson = () => {
    if (lesson?.nextLessonId) {
      router.push(`/lessons/${lesson.nextLessonId}`)
    }
  }

  const handlePrevLesson = () => {
    if (lesson?.prevLessonId) {
      router.push(`/lessons/${lesson.prevLessonId}`)
    }
  }

  if (!lesson) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/dashboard" className="flex items-center text-gray-600 hover:text-primary-600 transition-colors mr-4">
                <ArrowLeft className="w-5 h-5 mr-1" />
                Back to Dashboard
              </Link>
              <Code className="w-8 h-8 text-primary-600" />
              <span className="ml-2 text-xl font-bold text-gray-900">JavaLearn</span>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-600">
                Progress: {progress}%
              </div>
              <div className="w-32 bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Lesson Header */}
        <motion.div 
          className="mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{lesson.title}</h1>
              <p className="text-gray-600 mb-4">{lesson.description}</p>
              <div className="flex items-center space-x-4 text-sm text-gray-500">
                <span className={`px-2 py-1 rounded-full text-xs ${
                  lesson.difficulty === 'Beginner' ? 'bg-green-100 text-green-800' :
                  lesson.difficulty === 'Intermediate' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-red-100 text-red-800'
                }`}>
                  {lesson.difficulty}
                </span>
                <span className="flex items-center">
                  <Clock className="w-4 h-4 mr-1" />
                  {lesson.duration}
                </span>
                {isCompleted && (
                  <span className="flex items-center text-green-600">
                    <CheckCircle className="w-4 h-4 mr-1" />
                    Completed
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: 'theory', label: 'Theory', icon: BookOpen },
                { id: 'example', label: 'Example', icon: Star },
                { id: 'exercise', label: 'Exercise', icon: Target }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setCurrentTab(tab.id as any)}
                  className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                    currentTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <tab.icon className="w-4 h-4 mr-2" />
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </motion.div>

        {/* Content */}
        <motion.div
          key={currentTab}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.4 }}
        >
          {currentTab === 'theory' && (
            <div className="card p-8">
              <div className="prose max-w-none">
                <div dangerouslySetInnerHTML={{ __html: lesson.content.theory.replace(/\n/g, '<br>') }} />
              </div>
            </div>
          )}

          {currentTab === 'example' && (
            <div className="space-y-6">
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Example Code</h3>
                <CodeEditor
                  initialCode={lesson.content.example}
                  readOnly={true}
                  height="500px"
                  showOutput={true}
                />
              </div>
            </div>
          )}

          {currentTab === 'exercise' && (
            <div className="space-y-6">
              <div className="card p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">Practice Exercise</h3>
                  <button
                    onClick={() => setShowHints(!showHints)}
                    className="btn-secondary text-sm"
                  >
                    {showHints ? 'Hide Hints' : 'Show Hints'}
                  </button>
                </div>
                <div className="prose max-w-none mb-6">
                  <div dangerouslySetInnerHTML={{ __html: lesson.content.exercise.description.replace(/\n/g, '<br>') }} />
                </div>
                
                {showHints && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <h4 className="font-medium text-yellow-800 mb-2">Hints:</h4>
                    <ul className="text-yellow-700 text-sm space-y-1">
                      {lesson.content.exercise.hints.map((hint, index) => (
                        <li key={index}>• {hint}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>

              <CodeEditor
                initialCode={lesson.content.exercise.starterCode}
                onCodeChange={handleCodeChange}
                onRun={handleRunCode}
                height="400px"
                showOutput={true}
                expectedOutput={lesson.content.exercise.expectedOutput}
              />
            </div>
          )}
        </motion.div>

        {/* Navigation Buttons */}
        <div className="flex justify-between items-center mt-8">
          <button
            onClick={handlePrevLesson}
            disabled={!lesson.prevLessonId}
            className="flex items-center btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ChevronLeft className="w-4 h-4 mr-1" />
            Previous Lesson
          </button>

          <div className="flex space-x-4">
            {currentTab !== 'exercise' && (
              <button
                onClick={() => {
                  const nextTab = currentTab === 'theory' ? 'example' : 'exercise'
                  setCurrentTab(nextTab)
                }}
                className="btn-primary"
              >
                Continue
                <ChevronRight className="w-4 h-4 ml-1" />
              </button>
            )}
            
            {currentTab === 'exercise' && isCompleted && (
              <button
                onClick={handleNextLesson}
                disabled={!lesson.nextLessonId}
                className="flex items-center btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next Lesson
                <ChevronRight className="w-4 h-4 ml-1" />
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
