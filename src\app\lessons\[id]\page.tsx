'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { 
  ChevronLeft, 
  ChevronRight, 
  BookOpen, 
  CheckCircle, 
  Play, 
  Target,
  Clock,
  Star,
  Code,
  ArrowLeft
} from 'lucide-react'
import Link from 'next/link'
import CodeEditor from '@/components/CodeEditor'
import toast from 'react-hot-toast'

interface Lesson {
  id: string
  title: string
  description: string
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced'
  duration: string
  content: {
    theory: string
    example: string
    exercise: {
      description: string
      starterCode: string
      expectedOutput: string
      hints: string[]
    }
  }
  nextLessonId?: string
  prevLessonId?: string
}

export default function LessonPage() {
  const params = useParams()
  const router = useRouter()
  const [lesson, setLesson] = useState<Lesson | null>(null)
  const [currentTab, setCurrentTab] = useState<'theory' | 'example' | 'exercise'>('theory')
  const [userCode, setUserCode] = useState('')
  const [isCompleted, setIsCompleted] = useState(false)
  const [showHints, setShowHints] = useState(false)
  const [progress, setProgress] = useState(0)

  // Function to get lesson data based on ID
  const getLessonData = (id: string): Lesson => {
    const lessons: { [key: string]: Lesson } = {
      '1': {
        id: '1',
        title: 'Java Basics: Variables and Data Types',
        description: 'Learn about variables, primitive data types, and how to declare and use them in Java.',
        difficulty: 'Beginner',
        duration: '30 min',
        content: {
          theory: `
# Variables and Data Types in Java

## What are Variables?
Variables are containers that store data values. In Java, every variable has a specific type that determines what kind of data it can hold.

## Primitive Data Types
Java has 8 primitive data types:

### Numeric Types:
- **byte**: 8-bit signed integer (-128 to 127)
- **short**: 16-bit signed integer (-32,768 to 32,767)
- **int**: 32-bit signed integer (-2^31 to 2^31-1)
- **long**: 64-bit signed integer (-2^63 to 2^63-1)
- **float**: 32-bit floating point
- **double**: 64-bit floating point

### Other Types:
- **char**: 16-bit Unicode character
- **boolean**: true or false

## Variable Declaration
To declare a variable in Java:
\`\`\`java
dataType variableName = value;
\`\`\`

## Naming Rules
- Must start with a letter, underscore, or dollar sign
- Cannot start with a number
- Case-sensitive
- Cannot use Java keywords
- Use camelCase convention
          `,
          example: `public class VariableExample {
    public static void main(String[] args) {
        // Integer variables
        int age = 25;
        long population = 7800000000L;

        // Floating point variables
        double price = 19.99;
        float temperature = 98.6f;

        // Character and boolean
        char grade = 'A';
        boolean isStudent = true;

        // String (reference type)
        String name = "John Doe";

        // Print all variables
        System.out.println("Name: " + name);
        System.out.println("Age: " + age);
        System.out.println("Grade: " + grade);
        System.out.println("Is Student: " + isStudent);
        System.out.println("Price: $" + price);
    }
}`,
          exercise: {
            description: `Create a program that declares variables for a student's information and prints them out.

**Requirements:**
1. Declare a String variable for the student's name
2. Declare an int variable for the student's age
3. Declare a double variable for the student's GPA
4. Declare a boolean variable for enrollment status
5. Print all the information in a formatted way

**Expected format:**
Student: [name]
Age: [age]
GPA: [gpa]
Enrolled: [status]`,
            starterCode: `public class StudentInfo {
    public static void main(String[] args) {
        // TODO: Declare variables for student information


        // TODO: Print the student information

    }
}`,
            expectedOutput: `Student: Alice Johnson
Age: 20
GPA: 3.8
Enrolled: true`,
            hints: [
              "Use String for the student's name",
              "Use int for age, double for GPA, and boolean for enrollment status",
              "Use System.out.println() to print each line",
              "You can concatenate strings using the + operator"
            ]
          }
        },
        nextLessonId: '2',
        prevLessonId: undefined
      },
      '2': {
        id: '2',
        title: 'Control Structures: If Statements and Loops',
        description: 'Master conditional statements and loops to control program flow.',
        difficulty: 'Beginner',
        duration: '45 min',
        content: {
          theory: `
# Control Structures in Java

Control structures allow you to control the flow of execution in your Java programs. They include conditional statements and loops.

## If Statements
If statements execute code based on boolean conditions.

### Basic If Statement:
\`\`\`java
if (condition) {
    // code to execute if condition is true
}
\`\`\`

### If-Else Statement:
\`\`\`java
if (condition) {
    // code if true
} else {
    // code if false
}
\`\`\`

### If-Else If-Else Chain:
\`\`\`java
if (condition1) {
    // code if condition1 is true
} else if (condition2) {
    // code if condition2 is true
} else {
    // code if all conditions are false
}
\`\`\`

## For Loops
For loops repeat code a specific number of times.

### Basic For Loop:
\`\`\`java
for (initialization; condition; increment) {
    // code to repeat
}
\`\`\`

### Enhanced For Loop (for arrays):
\`\`\`java
for (dataType variable : array) {
    // code using variable
}
\`\`\`

## While Loops
While loops repeat code while a condition is true.

### While Loop:
\`\`\`java
while (condition) {
    // code to repeat
    // don't forget to update condition!
}
\`\`\`

### Do-While Loop:
\`\`\`java
do {
    // code to repeat at least once
} while (condition);
\`\`\`

## Switch Cases
Switch statements provide an alternative to multiple if-else statements.

\`\`\`java
switch (variable) {
    case value1:
        // code for value1
        break;
    case value2:
        // code for value2
        break;
    default:
        // default code
        break;
}
\`\`\`
          `,
          example: `public class ControlStructuresDemo {
    public static void main(String[] args) {
        // If Statements Example
        int score = 85;
        System.out.println("=== If Statements ===");

        if (score >= 90) {
            System.out.println("Grade: A - Excellent!");
        } else if (score >= 80) {
            System.out.println("Grade: B - Good job!");
        } else if (score >= 70) {
            System.out.println("Grade: C - Average");
        } else {
            System.out.println("Grade: F - Need improvement");
        }

        // For Loop Example
        System.out.println("\\n=== For Loop ===");
        System.out.println("Counting from 1 to 5:");
        for (int i = 1; i <= 5; i++) {
            System.out.println("Count: " + i);
        }

        // While Loop Example
        System.out.println("\\n=== While Loop ===");
        System.out.println("Countdown:");
        int countdown = 3;
        while (countdown > 0) {
            System.out.println(countdown + "...");
            countdown--;
        }
        System.out.println("Blast off!");

        // Switch Case Example
        System.out.println("\\n=== Switch Case ===");
        int dayOfWeek = 3;
        switch (dayOfWeek) {
            case 1:
                System.out.println("Monday");
                break;
            case 2:
                System.out.println("Tuesday");
                break;
            case 3:
                System.out.println("Wednesday");
                break;
            case 4:
                System.out.println("Thursday");
                break;
            case 5:
                System.out.println("Friday");
                break;
            default:
                System.out.println("Weekend!");
                break;
        }
    }
}`,
          exercise: {
            description: `Create a program that demonstrates all four control structure topics: If Statements, For Loops, While Loops, and Switch Cases.

**Requirements:**
1. **If Statements**: Check if a student's grade (0-100) and print the letter grade (A, B, C, D, F)
2. **For Loops**: Print multiplication table for number 5 (5x1 to 5x10)
3. **While Loops**: Print numbers from 10 down to 1
4. **Switch Cases**: Given a month number (1-12), print the season

**Expected output format:**
Grade: [letter grade]
=== Multiplication Table ===
5 x 1 = 5
5 x 2 = 10
...
=== Countdown ===
10
9
...
1
Season: [season name]`,
            starterCode: `public class ControlStructuresExercise {
    public static void main(String[] args) {
        // Test values
        int grade = 87;
        int tableNumber = 5;
        int month = 6;

        // TODO: 1. If Statements - Check grade and print letter grade
        // A: 90-100, B: 80-89, C: 70-79, D: 60-69, F: below 60


        // TODO: 2. For Loop - Print multiplication table for tableNumber
        System.out.println("=== Multiplication Table ===");


        // TODO: 3. While Loop - Print countdown from 10 to 1
        System.out.println("=== Countdown ===");


        // TODO: 4. Switch Case - Print season based on month
        // Spring: 3,4,5  Summer: 6,7,8  Fall: 9,10,11  Winter: 12,1,2

    }
}`,
            expectedOutput: `Grade: B
=== Multiplication Table ===
5 x 1 = 5
5 x 2 = 10
5 x 3 = 15
5 x 4 = 20
5 x 5 = 25
5 x 6 = 30
5 x 7 = 35
5 x 8 = 40
5 x 9 = 45
5 x 10 = 50
=== Countdown ===
10
9
8
7
6
5
4
3
2
1
Season: Summer`,
            hints: [
              "For if statements: Use >= for grade ranges (90, 80, 70, 60)",
              "For multiplication: Use for(int i = 1; i <= 10; i++)",
              "For countdown: Use while loop with int counter = 10; counter >= 1; counter--",
              "For switch: Use cases 3,4,5 for Spring; 6,7,8 for Summer, etc."
            ]
          }
        },
        nextLessonId: '3',
        prevLessonId: '1'
      },
      '3': {
        id: '3',
        title: 'Methods and Functions',
        description: 'Learn how to create reusable code with methods and understand parameters.',
        difficulty: 'Beginner',
        duration: '40 min',
        content: {
          theory: `
# Methods and Functions in Java

Methods are blocks of code that perform specific tasks and can be reused throughout your program.

## Method Declaration
The basic syntax for declaring a method:

\`\`\`java
accessModifier returnType methodName(parameters) {
    // method body
    return value; // if returnType is not void
}
\`\`\`

### Components:
- **Access Modifier**: public, private, protected
- **Return Type**: void, int, String, etc.
- **Method Name**: follows camelCase convention
- **Parameters**: input values (optional)

## Method Declaration Examples

### Void Method (no return value):
\`\`\`java
public void printMessage() {
    System.out.println("Hello from method!");
}
\`\`\`

### Method with Return Value:
\`\`\`java
public int addNumbers(int a, int b) {
    return a + b;
}
\`\`\`

## Parameters
Parameters allow you to pass data into methods.

### Single Parameter:
\`\`\`java
public void greetUser(String name) {
    System.out.println("Hello, " + name + "!");
}
\`\`\`

### Multiple Parameters:
\`\`\`java
public double calculateArea(double length, double width) {
    return length * width;
}
\`\`\`

## Return Types
Methods can return different types of data.

### Common Return Types:
- **void**: No return value
- **int**: Integer numbers
- **double**: Decimal numbers
- **String**: Text
- **boolean**: true/false

## Method Overloading
You can have multiple methods with the same name but different parameters.

\`\`\`java
public int add(int a, int b) {
    return a + b;
}

public double add(double a, double b) {
    return a + b;
}

public int add(int a, int b, int c) {
    return a + b + c;
}
\`\`\`
          `,
          example: `public class MethodsDemo {

    // Method Declaration - void method with no parameters
    public static void printWelcome() {
        System.out.println("Welcome to Java Methods!");
        System.out.println("=========================");
    }

    // Method with Parameters - single parameter
    public static void greetUser(String userName) {
        System.out.println("Hello, " + userName + "! Nice to meet you.");
    }

    // Method with Return Type - returns an integer
    public static int addTwoNumbers(int num1, int num2) {
        int sum = num1 + num2;
        return sum;
    }

    // Method with multiple parameters and return type
    public static double calculateRectangleArea(double length, double width) {
        return length * width;
    }

    // Method Overloading - same name, different parameters
    public static int multiply(int a, int b) {
        return a * b;
    }

    public static double multiply(double a, double b) {
        return a * b;
    }

    public static int multiply(int a, int b, int c) {
        return a * b * c;
    }

    public static void main(String[] args) {
        // Calling methods
        printWelcome();

        greetUser("Alice");
        greetUser("Bob");

        int result = addTwoNumbers(15, 25);
        System.out.println("15 + 25 = " + result);

        double area = calculateRectangleArea(5.5, 3.2);
        System.out.println("Rectangle area: " + area);

        // Method overloading examples
        System.out.println("2 * 3 = " + multiply(2, 3));
        System.out.println("2.5 * 3.0 = " + multiply(2.5, 3.0));
        System.out.println("2 * 3 * 4 = " + multiply(2, 3, 4));
    }
}`,
          exercise: {
            description: `Create a program that demonstrates all four method topics: Method Declaration, Parameters, Return Types, and Method Overloading.

**Requirements:**
1. **Method Declaration**: Create a void method that prints a welcome message
2. **Parameters**: Create a method that takes a name and age, prints a personalized message
3. **Return Types**: Create a method that calculates and returns the square of a number
4. **Method Overloading**: Create three versions of a "calculate" method:
   - One that adds two integers
   - One that adds two doubles
   - One that adds three integers

**Expected output format:**
Welcome to the Method Exercise!
Hello John, you are 25 years old.
The square of 7 is: 49
Sum of 5 and 3: 8
Sum of 2.5 and 3.7: 6.2
Sum of 1, 2, and 3: 6`,
            starterCode: `public class MethodsExercise {

    // TODO: 1. Method Declaration - Create a void method called printWelcome()


    // TODO: 2. Parameters - Create a method called printPersonInfo(String name, int age)


    // TODO: 3. Return Types - Create a method called calculateSquare(int number) that returns int


    // TODO: 4. Method Overloading - Create three calculate methods:
    // - calculate(int a, int b) returns int
    // - calculate(double a, double b) returns double
    // - calculate(int a, int b, int c) returns int




    public static void main(String[] args) {
        // TODO: Call all your methods here with these values:
        // printWelcome()
        // printPersonInfo("John", 25)
        // calculateSquare(7)
        // calculate(5, 3)
        // calculate(2.5, 3.7)
        // calculate(1, 2, 3)

    }
}`,
            expectedOutput: `Welcome to the Method Exercise!
Hello John, you are 25 years old.
The square of 7 is: 49
Sum of 5 and 3: 8
Sum of 2.5 and 3.7: 6.2
Sum of 1, 2, and 3: 6`,
            hints: [
              "Use 'public static void' for methods that don't return values",
              "Use 'public static int' for methods that return integers",
              "Method overloading means same name, different parameter types or counts",
              "Don't forget to call all methods in main() and print the results"
            ]
          }
        },
        nextLessonId: '4',
        prevLessonId: '2'
      },
      '4': {
        id: '4',
        title: 'Object-Oriented Programming: Classes and Objects',
        description: 'Introduction to OOP concepts with classes, objects, and encapsulation.',
        difficulty: 'Intermediate',
        duration: '60 min',
        content: {
          theory: `
# Object-Oriented Programming in Java

Object-Oriented Programming (OOP) is a programming paradigm based on the concept of "objects" which contain data and code.

## Classes
A class is a blueprint or template for creating objects. It defines the properties and behaviors that objects of that type will have.

### Class Declaration:
\`\`\`java
public class ClassName {
    // fields (attributes)
    // constructors
    // methods
}
\`\`\`

### Example Class:
\`\`\`java
public class Car {
    // Fields (attributes)
    private String brand;
    private String model;
    private int year;

    // Constructor
    public Car(String brand, String model, int year) {
        this.brand = brand;
        this.model = model;
        this.year = year;
    }

    // Methods
    public void startEngine() {
        System.out.println("Engine started!");
    }
}
\`\`\`

## Objects
An object is an instance of a class. You create objects using the \`new\` keyword.

\`\`\`java
Car myCar = new Car("Toyota", "Camry", 2023);
\`\`\`

## Constructors
Constructors are special methods used to initialize objects when they are created.

### Default Constructor:
\`\`\`java
public Car() {
    // default values
}
\`\`\`

### Parameterized Constructor:
\`\`\`java
public Car(String brand, String model, int year) {
    this.brand = brand;
    this.model = model;
    this.year = year;
}
\`\`\`

## Encapsulation
Encapsulation is the practice of keeping fields private and providing public methods to access them.

### Private Fields with Public Methods:
\`\`\`java
private String name;

public String getName() {
    return name;
}

public void setName(String name) {
    this.name = name;
}
\`\`\`
          `,
          example: `// Student class demonstrating OOP concepts
class Student {
    // Private fields (Encapsulation)
    private String name;
    private int age;
    private String studentId;
    private double gpa;

    // Default Constructor
    public Student() {
        this.name = "Unknown";
        this.age = 0;
        this.studentId = "000000";
        this.gpa = 0.0;
    }

    // Parameterized Constructor
    public Student(String name, int age, String studentId, double gpa) {
        this.name = name;
        this.age = age;
        this.studentId = studentId;
        this.gpa = gpa;
    }

    // Getter methods (Encapsulation)
    public String getName() {
        return name;
    }

    public int getAge() {
        return age;
    }

    public String getStudentId() {
        return studentId;
    }

    public double getGpa() {
        return gpa;
    }

    // Setter methods (Encapsulation)
    public void setName(String name) {
        this.name = name;
    }

    public void setAge(int age) {
        if (age > 0) {
            this.age = age;
        }
    }

    public void setGpa(double gpa) {
        if (gpa >= 0.0 && gpa <= 4.0) {
            this.gpa = gpa;
        }
    }

    // Method to display student information
    public void displayInfo() {
        System.out.println("Student Information:");
        System.out.println("Name: " + name);
        System.out.println("Age: " + age);
        System.out.println("Student ID: " + studentId);
        System.out.println("GPA: " + gpa);
    }

    // Method to check if student is on honor roll
    public boolean isHonorRoll() {
        return gpa >= 3.5;
    }
}

public class OOPDemo {
    public static void main(String[] args) {
        // Creating objects using different constructors
        Student student1 = new Student();
        Student student2 = new Student("Alice Johnson", 20, "STU001", 3.8);

        System.out.println("=== Student 1 (Default Constructor) ===");
        student1.displayInfo();

        System.out.println("\\n=== Student 2 (Parameterized Constructor) ===");
        student2.displayInfo();

        // Using setter methods to modify student1
        student1.setName("Bob Smith");
        student1.setAge(19);
        student1.setGpa(3.2);

        System.out.println("\\n=== Student 1 (After modifications) ===");
        student1.displayInfo();

        // Using methods
        System.out.println("\\n=== Honor Roll Status ===");
        System.out.println(student1.getName() + " honor roll: " + student1.isHonorRoll());
        System.out.println(student2.getName() + " honor roll: " + student2.isHonorRoll());
    }
}`,
          exercise: {
            description: `Create a Book class that demonstrates all four OOP topics: Classes, Objects, Constructors, and Encapsulation.

**Requirements:**
1. **Classes**: Create a Book class with private fields: title, author, pages, price
2. **Objects**: Create two Book objects in main method
3. **Constructors**: Implement both default and parameterized constructors
4. **Encapsulation**: Create getter and setter methods for all fields, plus a displayInfo() method

**Expected output format:**
=== Book 1 (Default Constructor) ===
Title: Unknown
Author: Unknown
Pages: 0
Price: $0.0

=== Book 2 (Parameterized Constructor) ===
Title: Java Programming
Author: John Doe
Pages: 500
Price: $49.99

=== Book 1 (After modifications) ===
Title: Python Basics
Author: Jane Smith
Pages: 300
Price: $29.99`,
            starterCode: `// TODO: Create Book class here
class Book {
    // TODO: 1. Classes - Add private fields: title, author, pages, price


    // TODO: 2. Constructors - Create default constructor


    // TODO: 2. Constructors - Create parameterized constructor


    // TODO: 3. Encapsulation - Create getter methods





    // TODO: 3. Encapsulation - Create setter methods





    // TODO: Create displayInfo() method

}

public class BookDemo {
    public static void main(String[] args) {
        // TODO: 4. Objects - Create two Book objects
        // book1 using default constructor
        // book2 using parameterized constructor with values:
        // "Java Programming", "John Doe", 500, 49.99


        // TODO: Display both books


        // TODO: Modify book1 using setters:
        // title: "Python Basics", author: "Jane Smith", pages: 300, price: 29.99


        // TODO: Display book1 again

    }
}`,
            expectedOutput: `=== Book 1 (Default Constructor) ===
Title: Unknown
Author: Unknown
Pages: 0
Price: $0.0

=== Book 2 (Parameterized Constructor) ===
Title: Java Programming
Author: John Doe
Pages: 500
Price: $49.99

=== Book 1 (After modifications) ===
Title: Python Basics
Author: Jane Smith
Pages: 300
Price: $29.99`,
            hints: [
              "Use private fields and public methods for encapsulation",
              "Default constructor should set default values like 'Unknown' and 0",
              "Parameterized constructor should accept all four parameters",
              "Getter methods return field values, setter methods update them"
            ]
          }
        },
        nextLessonId: '5',
        prevLessonId: '3'
      },
      '5': {
        id: '5',
        title: 'Arrays and Collections',
        description: 'Work with arrays and Java collections like ArrayList and HashMap.',
        difficulty: 'Intermediate',
        duration: '50 min',
        content: {
          theory: `
# Arrays and Collections in Java

Arrays and Collections are used to store multiple values in Java.

## Arrays
Arrays store multiple values of the same type in a fixed-size sequential collection.

### Array Declaration and Initialization:
\`\`\`java
// Declaration
int[] numbers;
String[] names;

// Initialization
int[] numbers = new int[5];  // Array of 5 integers
String[] names = {"Alice", "Bob", "Charlie"};  // Array with values
\`\`\`

### Accessing Array Elements:
\`\`\`java
int[] numbers = {10, 20, 30, 40, 50};
System.out.println(numbers[0]);  // Prints 10
numbers[1] = 25;  // Changes second element to 25
\`\`\`

### Array Properties:
\`\`\`java
int[] numbers = {1, 2, 3, 4, 5};
System.out.println(numbers.length);  // Prints 5
\`\`\`

## ArrayList
ArrayList is a resizable array implementation that can grow and shrink dynamically.

### ArrayList Declaration and Initialization:
\`\`\`java
import java.util.ArrayList;

ArrayList<String> names = new ArrayList<>();
ArrayList<Integer> numbers = new ArrayList<>();
\`\`\`

### ArrayList Methods:
\`\`\`java
ArrayList<String> fruits = new ArrayList<>();
fruits.add("Apple");        // Add element
fruits.add("Banana");
fruits.get(0);             // Get element at index 0
fruits.set(0, "Orange");   // Replace element at index 0
fruits.remove(1);          // Remove element at index 1
fruits.size();             // Get size
\`\`\`

## HashMap
HashMap stores key-value pairs and allows fast lookup by key.

### HashMap Declaration and Usage:
\`\`\`java
import java.util.HashMap;

HashMap<String, Integer> ages = new HashMap<>();
ages.put("Alice", 25);     // Add key-value pair
ages.put("Bob", 30);
ages.get("Alice");         // Get value by key (returns 25)
ages.remove("Bob");        // Remove key-value pair
\`\`\`

## Iteration
You can iterate through arrays and collections using loops.

### For-each Loop:
\`\`\`java
// Arrays
int[] numbers = {1, 2, 3, 4, 5};
for (int num : numbers) {
    System.out.println(num);
}

// ArrayList
ArrayList<String> names = new ArrayList<>();
for (String name : names) {
    System.out.println(name);
}
\`\`\`
          `,
          example: `import java.util.ArrayList;
import java.util.HashMap;

public class ArraysCollectionsDemo {
    public static void main(String[] args) {

        // === ARRAYS ===
        System.out.println("=== Arrays Demo ===");

        // Array declaration and initialization
        int[] scores = {85, 92, 78, 96, 88};
        String[] subjects = new String[3];
        subjects[0] = "Math";
        subjects[1] = "Science";
        subjects[2] = "English";

        // Accessing and modifying arrays
        System.out.println("First score: " + scores[0]);
        System.out.println("Array length: " + scores.length);
        scores[0] = 90;  // Modify first element

        // Iteration through array
        System.out.println("All scores:");
        for (int score : scores) {
            System.out.println("Score: " + score);
        }

        // === ARRAYLIST ===
        System.out.println("\\n=== ArrayList Demo ===");

        ArrayList<String> students = new ArrayList<>();

        // Adding elements
        students.add("Alice");
        students.add("Bob");
        students.add("Charlie");
        students.add("Diana");

        System.out.println("Number of students: " + students.size());
        System.out.println("First student: " + students.get(0));

        // Modifying ArrayList
        students.set(1, "Robert");  // Change Bob to Robert
        students.remove("Charlie"); // Remove Charlie

        // Iteration through ArrayList
        System.out.println("Current students:");
        for (String student : students) {
            System.out.println("Student: " + student);
        }

        // === HASHMAP ===
        System.out.println("\\n=== HashMap Demo ===");

        HashMap<String, Integer> studentGrades = new HashMap<>();

        // Adding key-value pairs
        studentGrades.put("Alice", 95);
        studentGrades.put("Robert", 87);
        studentGrades.put("Diana", 92);

        // Accessing values
        System.out.println("Alice's grade: " + studentGrades.get("Alice"));
        System.out.println("Number of grades: " + studentGrades.size());

        // Iteration through HashMap
        System.out.println("All grades:");
        for (String name : studentGrades.keySet()) {
            System.out.println(name + ": " + studentGrades.get(name));
        }

        // Check if key exists
        if (studentGrades.containsKey("Alice")) {
            System.out.println("Alice's grade found!");
        }
    }
}`,
          exercise: {
            description: `Create a program that demonstrates all four collection topics: Arrays, ArrayList, HashMap, and Iteration.

**Requirements:**
1. **Arrays**: Create an array of 5 integers, display them, and calculate their sum
2. **ArrayList**: Create an ArrayList of fruits, add 4 fruits, remove one, and display the list
3. **HashMap**: Create a HashMap of countries and their capitals, add 3 pairs, and display them
4. **Iteration**: Use for-each loops to iterate through all collections

**Expected output format:**
=== Arrays ===
Numbers: 10 20 30 40 50
Sum: 150

=== ArrayList ===
Fruits: [Apple, Banana, Orange]

=== HashMap ===
Countries and Capitals:
USA: Washington DC
France: Paris
Japan: Tokyo`,
            starterCode: `import java.util.ArrayList;
import java.util.HashMap;

public class CollectionsExercise {
    public static void main(String[] args) {

        // TODO: 1. Arrays - Create array with values {10, 20, 30, 40, 50}
        System.out.println("=== Arrays ===");


        // TODO: Display array elements and calculate sum using iteration


        // TODO: 2. ArrayList - Create ArrayList of fruits
        System.out.println("\\n=== ArrayList ===");


        // TODO: Add fruits: "Apple", "Banana", "Cherry", "Orange"
        // TODO: Remove "Cherry"
        // TODO: Display remaining fruits


        // TODO: 3. HashMap - Create HashMap of countries and capitals
        System.out.println("\\n=== HashMap ===");


        // TODO: Add pairs: "USA"->"Washington DC", "France"->"Paris", "Japan"->"Tokyo"
        // TODO: Display all pairs using iteration

    }
}`,
            expectedOutput: `=== Arrays ===
Numbers: 10 20 30 40 50
Sum: 150

=== ArrayList ===
Fruits: [Apple, Banana, Orange]

=== HashMap ===
Countries and Capitals:
USA: Washington DC
France: Paris
Japan: Tokyo`,
            hints: [
              "Use for-each loop: for(int num : array) to iterate arrays",
              "ArrayList methods: add(), remove(), toString() for display",
              "HashMap methods: put(), keySet() for iteration",
              "Calculate sum by adding each array element in the loop"
            ]
          }
        },
        nextLessonId: '6',
        prevLessonId: '4'
      },
      '6': {
        id: '6',
        title: 'Exception Handling',
        description: 'Learn to handle errors gracefully with try-catch blocks and custom exceptions.',
        difficulty: 'Advanced',
        duration: '45 min',
        content: {
          theory: `
# Exception Handling in Java

Exception handling allows you to manage runtime errors gracefully and prevent your program from crashing.

## Try-Catch Blocks
The basic structure for handling exceptions:

\`\`\`java
try {
    // Code that might throw an exception
} catch (ExceptionType e) {
    // Handle the exception
}
\`\`\`

### Example:
\`\`\`java
try {
    int result = 10 / 0;  // This will throw ArithmeticException
} catch (ArithmeticException e) {
    System.out.println("Cannot divide by zero!");
}
\`\`\`

## Finally Block
The finally block always executes, whether an exception occurs or not:

\`\`\`java
try {
    // risky code
} catch (Exception e) {
    // handle exception
} finally {
    // cleanup code - always runs
    System.out.println("Cleanup completed");
}
\`\`\`

## Custom Exceptions
You can create your own exception classes:

\`\`\`java
class CustomException extends Exception {
    public CustomException(String message) {
        super(message);
    }
}
\`\`\`

## Throws Keyword
Use throws to declare that a method might throw an exception:

\`\`\`java
public void riskyMethod() throws IOException {
    // code that might throw IOException
}
\`\`\`

### Calling methods that throw exceptions:
\`\`\`java
try {
    riskyMethod();
} catch (IOException e) {
    System.out.println("IO Error: " + e.getMessage());
}
\`\`\`

## Common Exception Types
- **ArithmeticException**: Division by zero
- **NullPointerException**: Using null reference
- **ArrayIndexOutOfBoundsException**: Invalid array index
- **NumberFormatException**: Invalid number conversion
- **IOException**: Input/output operations
- **FileNotFoundException**: File not found

## Best Practices
1. Catch specific exceptions rather than generic Exception
2. Always clean up resources in finally block
3. Don't ignore exceptions - at least log them
4. Use meaningful error messages
          `,
          example: `// Custom Exception class
class InvalidAgeException extends Exception {
    public InvalidAgeException(String message) {
        super(message);
    }
}

public class ExceptionHandlingDemo {

    // Method that throws custom exception
    public static void validateAge(int age) throws InvalidAgeException {
        if (age < 0 || age > 150) {
            throw new InvalidAgeException("Age must be between 0 and 150. Got: " + age);
        }
        System.out.println("Valid age: " + age);
    }

    // Method demonstrating different exception types
    public static void demonstrateExceptions() {

        // Try-Catch with ArithmeticException
        System.out.println("=== ArithmeticException Demo ===");
        try {
            int result = 10 / 0;
            System.out.println("Result: " + result);
        } catch (ArithmeticException e) {
            System.out.println("Error: Cannot divide by zero!");
        }

        // Try-Catch with ArrayIndexOutOfBoundsException
        System.out.println("\\n=== ArrayIndexOutOfBoundsException Demo ===");
        try {
            int[] numbers = {1, 2, 3};
            System.out.println("Element at index 5: " + numbers[5]);
        } catch (ArrayIndexOutOfBoundsException e) {
            System.out.println("Error: Array index out of bounds!");
        }

        // Try-Catch with NumberFormatException
        System.out.println("\\n=== NumberFormatException Demo ===");
        try {
            String text = "abc";
            int number = Integer.parseInt(text);
            System.out.println("Number: " + number);
        } catch (NumberFormatException e) {
            System.out.println("Error: Cannot convert '" + e.getMessage().split("\"")[1] + "' to number!");
        }
    }

    public static void main(String[] args) {

        // Demonstrate common exceptions
        demonstrateExceptions();

        // Custom Exception with Throws
        System.out.println("\\n=== Custom Exception Demo ===");

        int[] testAges = {25, -5, 200, 30};

        for (int age : testAges) {
            try {
                validateAge(age);
            } catch (InvalidAgeException e) {
                System.out.println("Custom Error: " + e.getMessage());
            }
        }

        // Finally Block Demo
        System.out.println("\\n=== Finally Block Demo ===");
        try {
            System.out.println("Executing risky operation...");
            int result = 10 / 2;  // This works fine
            System.out.println("Result: " + result);
        } catch (Exception e) {
            System.out.println("Exception caught: " + e.getMessage());
        } finally {
            System.out.println("Finally block: Cleanup completed!");
        }

        System.out.println("\\nProgram completed successfully!");
    }
}`,
          exercise: {
            description: `Create a program that demonstrates all four exception handling topics: Try-Catch, Finally Block, Custom Exceptions, and Throws.

**Requirements:**
1. **Try-Catch**: Handle division by zero and array index out of bounds
2. **Finally Block**: Use finally to print cleanup message
3. **Custom Exceptions**: Create InvalidScoreException for scores outside 0-100 range
4. **Throws**: Create a method that throws the custom exception

**Expected output format:**
=== Try-Catch Demo ===
Error: Division by zero!
Error: Array index out of bounds!

=== Custom Exception Demo ===
Valid score: 85
Error: Score must be between 0 and 100. Got: 150

=== Finally Block Demo ===
Processing...
Finally: Cleanup completed!`,
            starterCode: `// TODO: 1. Custom Exceptions - Create InvalidScoreException class


public class ExceptionExercise {

    // TODO: 2. Throws - Create validateScore method that throws InvalidScoreException
    // Method should accept int score and throw exception if score < 0 or score > 100


    public static void main(String[] args) {

        System.out.println("=== Try-Catch Demo ===");

        // TODO: 3. Try-Catch - Handle division by zero
        try {

        } catch () {

        }

        // TODO: 3. Try-Catch - Handle array index out of bounds
        // Create array {1, 2, 3} and try to access index 5
        try {

        } catch () {

        }

        System.out.println("\\n=== Custom Exception Demo ===");

        // TODO: Test validateScore with values 85 and 150


        System.out.println("\\n=== Finally Block Demo ===");

        // TODO: 4. Finally Block - Use try-catch-finally
        try {
            System.out.println("Processing...");
            // Some operation
        } catch (Exception e) {
            System.out.println("Error occurred");
        } finally {
            // TODO: Print cleanup message
        }
    }
}`,
            expectedOutput: `=== Try-Catch Demo ===
Error: Division by zero!
Error: Array index out of bounds!

=== Custom Exception Demo ===
Valid score: 85
Error: Score must be between 0 and 100. Got: 150

=== Finally Block Demo ===
Processing...
Finally: Cleanup completed!`,
            hints: [
              "Custom exception: class InvalidScoreException extends Exception",
              "Throws: public static void validateScore(int score) throws InvalidScoreException",
              "Try-catch: catch (ArithmeticException e) and catch (ArrayIndexOutOfBoundsException e)",
              "Finally block always executes after try-catch"
            ]
          }
        },
        nextLessonId: undefined,
        prevLessonId: '5'
      }
    }

    return lessons[id] || lessons['1'] // Default to lesson 1 if not found
  }

  const mockLesson = getLessonData(params.id as string)

  useEffect(() => {
    // Simulate loading lesson data
    setLesson(mockLesson)
    setUserCode(mockLesson.content.exercise.starterCode)
    
    // Calculate progress based on current tab
    const tabProgress = {
      theory: 33,
      example: 66,
      exercise: 100
    }
    setProgress(tabProgress[currentTab])
  }, [currentTab])

  const handleCodeChange = (code: string) => {
    setUserCode(code)
  }

  const handleRunCode = async (code: string) => {
    console.log('🚀 Lesson handleRunCode called with code:', code.substring(0, 100) + '...')

    try {
      // Call the real API for code execution
      const apiUrl = window.location.origin + '/api/execute'
      console.log('📡 Calling API:', apiUrl)

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code: code,
          language: 'java'
        })
      })

      console.log('📡 API Response status:', response.status)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      console.log('✅ API Result:', result)

      if (result.success) {
        const actualOutput = result.output || ''
        console.log('📝 Actual output:', actualOutput)
        console.log('📝 Expected output:', lesson?.content.exercise.expectedOutput)

        // Check if the output matches expected output (compare first lines for flexibility)
        if (lesson && lesson.content.exercise.expectedOutput) {
          const expectedLines = lesson.content.exercise.expectedOutput.trim().split('\n')
          const actualLines = actualOutput.trim().split('\n')

          // Check if the main content matches (ignoring simulation message)
          const actualMainContent = actualLines.filter(line => !line.includes('Simulation Mode')).join('\n')
          const expectedMainContent = expectedLines.join('\n')

          if (actualMainContent.includes(expectedMainContent) || expectedMainContent.includes(actualMainContent)) {
            setIsCompleted(true)
            toast.success('Congratulations! Exercise completed successfully!')
          } else {
            toast.error('Output doesn\'t match expected result. Keep trying!')
          }
        } else {
          // If no expected output, just show success
          toast.success('Code executed successfully!')
        }
      } else {
        toast.error(`Execution failed: ${result.error}`)
      }
    } catch (error) {
      console.error('💥 Lesson execution error:', error)
      toast.error('Error executing code: ' + (error instanceof Error ? error.message : 'Unknown error'))
    }
  }

  const handleNextLesson = () => {
    if (lesson?.nextLessonId) {
      router.push(`/lessons/${lesson.nextLessonId}`)
    }
  }

  const handlePrevLesson = () => {
    if (lesson?.prevLessonId) {
      router.push(`/lessons/${lesson.prevLessonId}`)
    }
  }

  if (!lesson) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/dashboard" className="flex items-center text-gray-600 hover:text-primary-600 transition-colors mr-4">
                <ArrowLeft className="w-5 h-5 mr-1" />
                Back to Dashboard
              </Link>
              <Code className="w-8 h-8 text-primary-600" />
              <span className="ml-2 text-xl font-bold text-gray-900">JavaLearn</span>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-600">
                Progress: {progress}%
              </div>
              <div className="w-32 bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Lesson Header */}
        <motion.div 
          className="mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{lesson.title}</h1>
              <p className="text-gray-600 mb-4">{lesson.description}</p>
              <div className="flex items-center space-x-4 text-sm text-gray-500">
                <span className={`px-2 py-1 rounded-full text-xs ${
                  lesson.difficulty === 'Beginner' ? 'bg-green-100 text-green-800' :
                  lesson.difficulty === 'Intermediate' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-red-100 text-red-800'
                }`}>
                  {lesson.difficulty}
                </span>
                <span className="flex items-center">
                  <Clock className="w-4 h-4 mr-1" />
                  {lesson.duration}
                </span>
                {isCompleted && (
                  <span className="flex items-center text-green-600">
                    <CheckCircle className="w-4 h-4 mr-1" />
                    Completed
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: 'theory', label: 'Theory', icon: BookOpen },
                { id: 'example', label: 'Example', icon: Star },
                { id: 'exercise', label: 'Exercise', icon: Target }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setCurrentTab(tab.id as any)}
                  className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                    currentTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <tab.icon className="w-4 h-4 mr-2" />
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </motion.div>

        {/* Content */}
        <motion.div
          key={currentTab}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.4 }}
        >
          {currentTab === 'theory' && (
            <div className="card p-8">
              <div className="prose max-w-none">
                <div dangerouslySetInnerHTML={{ __html: lesson.content.theory.replace(/\n/g, '<br>') }} />
              </div>
            </div>
          )}

          {currentTab === 'example' && (
            <div className="space-y-6">
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Example Code</h3>
                <p className="text-gray-600 mb-4">
                  This is a working example that demonstrates the concepts. Click "Run" to see it in action!
                </p>
                <CodeEditor
                  initialCode={lesson.content.example}
                  readOnly={false}
                  height="500px"
                  showOutput={true}
                  onRun={handleRunCode}
                />
              </div>
            </div>
          )}

          {currentTab === 'exercise' && (
            <div className="space-y-6">
              <div className="card p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">Practice Exercise</h3>
                  <button
                    onClick={() => setShowHints(!showHints)}
                    className="btn-secondary text-sm"
                  >
                    {showHints ? 'Hide Hints' : 'Show Hints'}
                  </button>
                </div>
                <div className="prose max-w-none mb-6">
                  <div dangerouslySetInnerHTML={{ __html: lesson.content.exercise.description.replace(/\n/g, '<br>') }} />
                </div>
                
                {showHints && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <h4 className="font-medium text-yellow-800 mb-2">Hints:</h4>
                    <ul className="text-yellow-700 text-sm space-y-1">
                      {lesson.content.exercise.hints.map((hint, index) => (
                        <li key={index}>• {hint}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>

              <CodeEditor
                initialCode={lesson.content.exercise.starterCode}
                onCodeChange={handleCodeChange}
                onRun={handleRunCode}
                height="400px"
                showOutput={true}
                expectedOutput={lesson.content.exercise.expectedOutput}
              />
            </div>
          )}
        </motion.div>

        {/* Navigation Buttons */}
        <div className="flex justify-between items-center mt-8">
          <button
            onClick={handlePrevLesson}
            disabled={!lesson.prevLessonId}
            className="flex items-center btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ChevronLeft className="w-4 h-4 mr-1" />
            Previous Lesson
          </button>

          <div className="flex space-x-4">
            {currentTab !== 'exercise' && (
              <button
                onClick={() => {
                  const nextTab = currentTab === 'theory' ? 'example' : 'exercise'
                  setCurrentTab(nextTab)
                }}
                className="btn-primary"
              >
                Continue
                <ChevronRight className="w-4 h-4 ml-1" />
              </button>
            )}
            
            {currentTab === 'exercise' && isCompleted && (
              <button
                onClick={handleNextLesson}
                disabled={!lesson.nextLessonId}
                className="flex items-center btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next Lesson
                <ChevronRight className="w-4 h-4 ml-1" />
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
